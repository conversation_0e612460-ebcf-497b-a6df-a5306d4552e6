<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <title>title</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style id="futuristic-style">
        @import url('https://fonts.googleapis.com/css2?family=Orbitron&display=swap');

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Orbitron', Arial, Helvetica, sans-serif;
            min-height: 100vh;
            min-width: 100vw;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e0e7ef;
            box-sizing: border-box;
        }

        h1 {
            color: #38bdf8;
            font-size: 2.5rem;
            letter-spacing: 2px;
            text-shadow: 0 0 8px #38bdf8, 0 0 16px #0ea5e9;
            margin-bottom: 16px;
            font-family: 'Or<PERSON><PERSON>', Arial, Helvetica, sans-serif;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(5, minmax(36px, 50px));
            grid-template-rows: repeat(5, minmax(36px, 50px));
            border: 2px solid #38bdf8;
            background: rgba(15, 23, 42, 0.7);
            box-shadow: 0 0 24px #38bdf8, 0 0 48px #f472b6;
            margin-bottom: 20px;
            position: relative;
            max-width: 100vw;
            max-height: 100vw;
        }

        .cell {
            width: 9vw;
            max-width: 50px;
            min-width: 36px;
            height: 9vw;
            max-height: 50px;
            min-height: 36px;
            border: 1.5px solid #334155;
            background: rgba(30, 41, 59, 0.85);
            color: #e0e7ef;
            font-family: 'Orbitron', Arial, Helvetica, sans-serif;
            text-shadow: 0 0 4px #0ea5e9;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 4vw;
            cursor: pointer;
        }

        .cell:hover {
            background: #0ea5e9;
            color: #fff;
            box-shadow: 0 0 16px #38bdf8, 0 0 8px #f472b6;
        }

        @media (max-width: 600px) {
            .grid-container {
                grid-template-columns: repeat(5, minmax(12vw, 1fr));
                grid-template-rows: repeat(5, minmax(12vw, 1fr));
                max-width: 98vw;
                max-height: 98vw;
            }

            .cell {
                width: 12vw;
                height: 12vw;
                font-size: 6vw;
                min-width: 32px;
                min-height: 32px;
            }
        }

        @media (max-width: 400px) {
            .grid-container {
                grid-template-columns: repeat(5, minmax(16vw, 1fr));
                grid-template-rows: repeat(5, minmax(16vw, 1fr));
            }

            .cell {
                width: 16vw;
                height: 16vw;
                font-size: 7vw;
            }
        }

        .path {
            background: linear-gradient(135deg, #38bdf8 60%, #f472b6 100%);
            color: #fff;
            box-shadow: 0 0 16px #38bdf8, 0 0 8px #f472b6;
        }

        .numbered {
            background: linear-gradient(135deg, #f472b6 60%, #38bdf8 100%);
            color: #fff;
            font-weight: bold;
            border: 2px solid #f472b6;
            text-shadow: 0 0 8px #f472b6, 0 0 4px #38bdf8;
        }

        .current {
            background: #0ea5e9 !important;
            color: #fff;
            box-shadow: 0 0 24px #38bdf8, 0 0 8px #f472b6;
        }

        #message {
            margin-bottom: 10px;
            font-weight: bold;
            color: #f472b6;
            text-shadow: 0 0 8px #f472b6;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            font-family: 'Orbitron', Arial, Helvetica, sans-serif;
            font-size: 1.1rem;
            border-radius: 8px;
            border: 2px solid #38bdf8;
            background: linear-gradient(90deg, #0ea5e9 60%, #f472b6 100%);
            color: #fff;
            box-shadow: 0 0 8px #38bdf8, 0 0 4px #f472b6;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        }

        button:hover {
            background: linear-gradient(90deg, #f472b6 60%, #0ea5e9 100%);
            color: #fff;
            box-shadow: 0 0 16px #f472b6, 0 0 8px #38bdf8;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100vw;
            height: 100vh;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background: linear-gradient(135deg, #1e293b 60%, #0ea5e9 100%);
            border: 2px solid #f472b6;
            color: #fff;
            box-shadow: 0 0 24px #38bdf8, 0 0 8px #f472b6;
            margin: 15% auto;
            padding: 20px;
            text-align: center;
            width: 80vw;
            max-width: 350px;
        }

        .close-button {
            color: #f472b6;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close-button:hover,
        .close-button:focus {
            color: #38bdf8;
            text-decoration: none;
            cursor: pointer;
        }

        .legal-move::after {
            content: '';
            width: 10px;
            height: 10px;
            background-color: black;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .cell {
            position: relative;
        }
    </style>
    <style id="classic-style" disabled>
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: sans-serif;
            min-height: 100vh;
            min-width: 100vw;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            box-sizing: border-box;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(5, minmax(36px, 50px));
            grid-template-rows: repeat(5, minmax(36px, 50px));
            border: 1px solid black;
            margin-bottom: 20px;
            position: relative;
            max-width: 100vw;
            max-height: 100vw;
        }

        .cell {
            width: 9vw;
            max-width: 50px;
            min-width: 36px;
            height: 9vw;
            max-height: 50px;
            min-height: 36px;
            border: 1px solid lightgray;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 4vw;
            cursor: pointer;
        }

        @media (max-width: 600px) {
            .grid-container {
                grid-template-columns: repeat(5, minmax(12vw, 1fr));
                grid-template-rows: repeat(5, minmax(12vw, 1fr));
                max-width: 98vw;
                max-height: 98vw;
            }

            .cell {
                width: 12vw;
                height: 12vw;
                font-size: 6vw;
                min-width: 32px;
                min-height: 32px;
            }
        }

        @media (max-width: 400px) {
            .grid-container {
                grid-template-columns: repeat(5, minmax(16vw, 1fr));
                grid-template-rows: repeat(5, minmax(16vw, 1fr));
            }

            .cell {
                width: 16vw;
                height: 16vw;
                font-size: 7vw;
            }
        }

        .path {
            background-color: #c0eaff;
        }

        .numbered {
            background-color: orange;
            color: white;
            font-weight: bold;
        }

        #message {
            margin-bottom: 10px;
            font-weight: bold;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100vw;
            height: 100vh;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80vw;
            max-width: 350px;
            text-align: center;
        }

        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .current {
            background-color: darkblue !important;
            color: white;
        }

        .legal-move::after {
            content: '';
            width: 10px;
            height: 10px;
            background-color: black;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .cell {
            position: relative;
        }
    </style>
</head>

<body>
    <h3>Mums Numbers!</h3>
    <div id="message"></div>
    <div class="grid-container" id="gridContainer">
        <div id="winModal" class="modal">
            <div class="modal-content">
                <span class="close-button" id="winClose">&times;</span>
                <p>You win! Try again?</p>
                <button id="winRestart">Restart</button>
            </div>
        </div>
        <div id="loseModal" class="modal">
            <div class="modal-content">
                <span class="close-button" id="loseClose">&times;</span>
                <p>Try again!</p>
                <button id="loseRestart">Restart</button>
            </div>
        </div>
    </div>
    <div>
        <button id="toggleLegalMoves">Show Legal Moves</button>
        <button id="hintButton">Hint</button>
        <button id="restartButton">Restart</button>
        <button id="toggleStyleButton" style="margin-bottom: 12px;">Switch Style</button>
    </div>
    <p>Clicks: <span id="clicks">0</span></p>
    <p>Time: <span id="time">0</span> seconds</p>
</body>
<script type="module">
    const gridSize = 5
    let grid = []
    let numberedCells = {}
    let currentPath = []
    let currentNumber = 1
    let totalClicks = 0
    let startTime
    let timerInterval
    let solved = false
    let maxNumber = 5 // Mandatory 5 numbers
    let savedGrid = null
    let savedNumberedCells = null
    let timerStarted = false
    let showLegalMoves = false

    const gridContainer = document.getElementById("gridContainer")
    const messageElement = document.getElementById("message")
    const hintButton = document.getElementById("hintButton")
    const restartButton = document.getElementById("restartButton")
    const clicksElement = document.getElementById("clicks")
    const timeElement = document.getElementById("time")
    const toggleLegalMovesButton = document.getElementById("toggleLegalMoves")

    function createGrid() {
        gridContainer.innerHTML = ""
        grid = Array(gridSize)
            .fill(null)
            .map(() => Array(gridSize).fill(null))
        numberedCells = {}
        currentPath = []
        currentNumber = 1
        totalClicks = 0
        startTime = null
        clearInterval(timerInterval)
        timeElement.textContent = "0"
        clicksElement.textContent = "0"
        solved = false
        messageElement.textContent = ""
        document.querySelectorAll(".cell.legal").forEach((cell) => {
            cell.classList.remove("legal")
        })
    }

    function updateLegalMoveIndicators() {
        document.querySelectorAll(".cell").forEach((cell) => {
            cell.classList.remove("legal-move")
        })

        if (!showLegalMoves || solved || currentPath.length === 0) return

        const last = currentPath[currentPath.length - 1]
        const neighbors = getNeighbors(last.row, last.col)
        const inlineCells = []

        // Horizontal and vertical inline moves
        for (let r = 0; r < gridSize; r++)
        {
            if (r !== last.row) inlineCells.push({ row: r, col: last.col })
        }
        for (let c = 0; c < gridSize; c++)
        {
            if (c !== last.col) inlineCells.push({ row: last.row, col: c })
        }

        const legalMoves = [...neighbors, ...inlineCells].filter(
            (cell) =>
                !currentPath.some((p) => p.row === cell.row && p.col === cell.col),
        )

        legalMoves.forEach((cell) => {
            const el = document.querySelector(
                `.cell[data-row="${cell.row}"][data-col="${cell.col}"]`,
            )
            if (el) el.classList.add("legal-move")
        })
    }

    function highlightLegalMoves(fromCell) {
        document.querySelectorAll(".cell.legal").forEach((cell) => {
            cell.classList.remove("legal")
        })

        const { row, col } = fromCell

        // Adjacent directions
        const directions = [
            { dr: -1, dc: 0 }, // up
            { dr: 1, dc: 0 }, // down
            { dr: 0, dc: -1 }, // left
            { dr: 0, dc: 1 }, // right
        ]

        for (const { dr, dc } of directions)
        {
            // Single adjacent
            const r = row + dr
            const c = col + dc
            if (isInBounds(r, c) && !isInPath(r, c))
            {
                markCellLegal(r, c)
            }

            // Inline cells
            let currRow = row + dr
            let currCol = col + dc
            while (isInBounds(currRow, currCol) && !isInPath(currRow, currCol))
            {
                markCellLegal(currRow, currCol)
                currRow += dr
                currCol += dc
            }
        }
    }

    function markCellLegal(r, c) {
        const el = document.querySelector(`.cell[data-row="${r}"][data-col="${c}"]`)
        if (el) el.classList.add("legal")
    }

    function isInBounds(r, c) {
        return r >= 0 && r < gridSize && c >= 0 && c < gridSize
    }

    function isInPath(r, c) {
        return currentPath.some((cell) => cell.row === r && cell.col === c)
    }

    function resetToSavedPuzzle() {
        if (!savedGrid || !savedNumberedCells) return;

        grid = savedGrid.map((row) => [...row]);
        numberedCells = JSON.parse(JSON.stringify(savedNumberedCells));

        currentPath = [];
        currentNumber = 1;
        totalClicks = 0;
        solved = false;
        startCell = null;

        expectedNumberedCell = numberedCells[1] || null; // expecting to go to number 2

        messageElement.textContent = "";
        timerStarted = false;
        clearInterval(timerInterval);
        clicksElement.textContent = "0";
        timeElement.textContent = "0";

        renderGrid();

        document.querySelectorAll(".cell.legal").forEach((cell) => {
            cell.classList.remove("legal");
        });

        updateLegalMoves(); // ✅ Recalculate legal moves from 1
    }


    // Import and use the LevelGenerator for consistency
    class LevelGenerator {
        constructor(gridSize = 5) {
            this.gridSize = gridSize;
        }
        
        generateSolvablePuzzle() {
            let attempts = 0;
            const maxAttempts = 1000;
            
            while (attempts < maxAttempts) {
                attempts++;
                
                const availableCells = [];
                for (let r = 0; r < this.gridSize; r++) {
                    for (let c = 0; c < this.gridSize; c++) {
                        availableCells.push({ row: r, col: c });
                    }
                }
                
                const startIndex = Math.floor(Math.random() * availableCells.length);
                const start = availableCells[startIndex];
                
                const path = this.findCompletePath(start, {}, [start]);
                
                if (path && path.length === this.gridSize * this.gridSize) {
                    return {
                        solutionPath: path,
                        numberPositions: this.placeNumbers(path)
                    };
                }
            }
            
            console.warn('Complex path generation failed, using fallback method');
            return this.generateFallbackPuzzle();
        }
        
        findCompletePath(currentPos, visited, path) {
            if (path.length === this.gridSize * this.gridSize) {
                return path;
            }
            
            const { row, col } = currentPos;
            const key = `${row}-${col}`;
            visited[key] = true;
            
            const neighbors = this.shuffleArray(this.getNeighbors(row, col));
            
            for (const neighbor of neighbors) {
                const nKey = `${neighbor.row}-${neighbor.col}`;
                if (!visited[nKey]) {
                    const result = this.findCompletePath(neighbor, { ...visited }, [...path, neighbor]);
                    if (result) return result;
                }
            }
            
            return null;
        }
        
        getNeighbors(row, col) {
            const neighbors = [];
            if (row > 0) neighbors.push({ row: row - 1, col });
            if (row < this.gridSize - 1) neighbors.push({ row: row + 1, col });
            if (col > 0) neighbors.push({ row, col: col - 1 });
            if (col < this.gridSize - 1) neighbors.push({ row, col: col + 1 });
            return neighbors;
        }
        
        shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }
        
        placeNumbers(path) {
            const numberPositions = {};
            const pathLength = path.length;
            
            // Use the same spacing as before for consistency
            const numberSteps = [0, 6, 12, 18, 24];
            const adjustedSteps = numberSteps.map(step => {
                if (pathLength <= 25) {
                    return Math.min(step, pathLength - 1);
                }
                return Math.floor((step / 24) * (pathLength - 1));
            });
            
            for (let i = 0; i < adjustedSteps.length && i < 5; i++) {
                const stepIndex = Math.max(0, Math.min(adjustedSteps[i], pathLength - 1));
                const position = path[stepIndex];
                const number = i + 1;
                
                grid[position.row][position.col] = number;
                numberPositions[number] = position;
            }
            
            return numberPositions;
        }
        
        generateFallbackPuzzle() {
            const path = [];
            
            // Generate a simple snake pattern
            for (let row = 0; row < this.gridSize; row++) {
                if (row % 2 === 0) {
                    for (let col = 0; col < this.gridSize; col++) {
                        path.push({ row, col });
                    }
                } else {
                    for (let col = this.gridSize - 1; col >= 0; col--) {
                        path.push({ row, col });
                    }
                }
            }
            
            return {
                solutionPath: path,
                numberPositions: this.placeNumbers(path)
            };
        }
    }
    
    const levelGenerator = new LevelGenerator(gridSize);

    function generateNumberedCells() {
        createGrid(); // reset grid
        
        const generatedLevel = levelGenerator.generateSolvablePuzzle();
        
        if (!generatedLevel) {
            messageElement.textContent = "Failed to generate a solvable puzzle. Please restart.";
            return;
        }
        
        // Apply the generated level
        numberedCells = generatedLevel.numberPositions;
        maxNumber = 5;
        
        savedGrid = grid.map((row) => [...row]);
        savedNumberedCells = JSON.parse(JSON.stringify(numberedCells));
        
        renderGrid();
        startTimer();
    }

    // These functions are now part of the LevelGenerator class above

    function getNeighbors(row, col) {
        const neighbors = []
        if (row > 0) neighbors.push({ row: row - 1, col })
        if (row < gridSize - 1) neighbors.push({ row: row + 1, col })
        if (col > 0) neighbors.push({ row, col: col - 1 })
        if (col < gridSize - 1) neighbors.push({ row, col: col + 1 })
        return neighbors
    }

    function renderGrid() {
        gridContainer.innerHTML = ""
        for (let r = 0; r < gridSize; r++)
        {
            for (let c = 0; c < gridSize; c++)
            {
                const cell = document.createElement("div")
                cell.classList.add("cell")
                cell.dataset.row = r
                cell.dataset.col = c
                if (grid[r][c])
                {
                    cell.textContent = grid[r][c]
                    cell.classList.add("numbered")
                }
                gridContainer.appendChild(cell)
                cell.addEventListener("click", handleCellClick)
            }
        }
        const startCell = document.querySelector(
            `.cell[data-row="${numberedCells[1]?.row}"][data-col="${numberedCells[1]?.col}"]`,
        )
        if (startCell)
        {
            startCell.classList.add("path")
            currentPath.push(numberedCells[1])
        }
        highlightLegalMoves(numberedCells[1])
    }

    function handleCellClick(event) {
        if (solved) return

        const row = parseInt(event.target.dataset.row)
        const col = parseInt(event.target.dataset.col)
        const clickedCell = { row, col }

        if (currentPath.some((cell) => cell.row === row && cell.col === col))
        {
            alert("Try again! You are not allowed to cross lines.")
            resetToSavedPuzzle()
            clearInterval(timerInterval)
            return
        }

        const lastPathCell =
            currentPath.length > 0
                ? currentPath[currentPath.length - 1]
                : numberedCells[1]

        const deltaRow = Math.abs(row - lastPathCell.row)
        const deltaCol = Math.abs(col - lastPathCell.col)

        const isAdjacent = deltaRow + deltaCol === 1
        const isInline =
            (deltaRow === 0 && deltaCol > 0) || (deltaCol === 0 && deltaRow > 0)

        if (isAdjacent)
        {
            processMove(clickedCell, event.target)
        } else if (isInline)
        {
            processInlineMove(lastPathCell, clickedCell)
        } else
        {
            alert("Illegal move!")
        }
    }

    function processMove(clickedCell, targetElement) {
        if (!timerStarted)
        {
            startTimer()
            timerStarted = true
        }

        document.querySelectorAll(".cell.current").forEach((cell) => {
            cell.classList.remove("current")
        })

        const { row, col } = clickedCell
        const isNewCell = !currentPath.some((p) => p.row === row && p.col === col)

        if (isNewCell)
        {
            totalClicks++
            clicksElement.textContent = totalClicks
            currentPath.push(clickedCell)
            targetElement.classList.add("path")
            targetElement.classList.add("current")

            const cellValue = grid[row][col]
            const nextExpectedNumber = currentNumber + 1
            const expectedNumberedCell = numberedCells[nextExpectedNumber]

            if (
                cellValue === nextExpectedNumber &&
                expectedNumberedCell &&
                row === expectedNumberedCell.row &&
                col === expectedNumberedCell.col
            )
            {
                currentNumber++
            } else if (cellValue !== null && cellValue !== currentNumber)
            {
                alert("Try again! You must pass the numbers in the correct order.")
                resetToSavedPuzzle() // same puzzle
                clearInterval(timerInterval)
            }

            checkWinCondition()
        }
        highlightLegalMoves(clickedCell)
        updateLegalMoveIndicators()
    }

    function processInlineMove(startCell, endCell) {
        const { row: startRow, col: startCol } = startCell
        const { row: endRow, col: endCol } = endCell
        const cellsToProcess = []
        let error = false
        const stepRow = Math.sign(endRow - startRow)
        const stepCol = Math.sign(endCol - startCol)
        let currentRow = startRow + stepRow
        let currentCol = startCol + stepCol

        while (currentRow !== endRow || currentCol !== endCol)
        {
            const cell = { row: currentRow, col: currentCol }
            if (currentPath.some((p) => p.row === cell.row && p.col === cell.col))
            {
                error = true
                break
            }
            const cellElement = document.querySelector(
                `.cell[data-row="${currentRow}"][data-col="${currentCol}"]`,
            )
            if (cellElement)
            {
                cellsToProcess.push({ cell, element: cellElement })
            }
            currentRow += stepRow
            currentCol += stepCol
        }

        // Add the end cell
        const endCellElement = document.querySelector(
            `.cell[data-row="${endRow}"][data-col="${endCol}"]`,
        )
        if (endCellElement)
        {
            if (currentPath.some((p) => p.row === endRow && p.col === endCol))
            {
                alert("Try again! You are not allowed to travese the path you already took!")
                resetToSavedPuzzle()
                clearInterval(timerInterval)
                return
            }
            cellsToProcess.push({ cell: endCell, element: endCellElement })
        }

        if (error)
        {
            alert("Try again! You can't cross lines!")
            resetToSavedPuzzle()
            clearInterval(timerInterval)
            return
        }

        // Process all the cells
        cellsToProcess.forEach(({ cell, element }) => {
            processMove(cell, element)
        })
        updateLegalMoveIndicators()
    }

    function checkWinCondition() {
        if (solved) return

        if (
            currentPath.length === gridSize * gridSize &&
            currentNumber >= maxNumber
        )
        {
            solved = true
            clearInterval(timerInterval)
            setTimeout(() => {
                alert(`You win in ${timeElement.textContent} seconds!`)
                restartGame()
            }, 100)
        } else if (
            currentPath.length === gridSize * gridSize &&
            currentNumber < maxNumber
        )
        {
            solved = true
            clearInterval(timerInterval)
            setTimeout(() => {
                alert("Try again! You must pass the numbers in the correct order.")
                resetToSavedPuzzle()
            }, 100)
        } else if (
            currentNumber >= maxNumber &&
            currentPath.length < gridSize * gridSize
        )
        {
            alert("Try again! All squares must be filled!")
            resetToSavedPuzzle()
            clearInterval(timerInterval)
        }
    }
    function showHint() {
        if (solved) return
        if (currentPath.length > 0)
        {
            const lastPathCell = currentPath[currentPath.length - 1]
            const possibleNextMoves = getNeighbors(
                lastPathCell.row,
                lastPathCell.col,
            ).filter(
                (neighbor) =>
                    !currentPath.some(
                        (cell) => cell.row === neighbor.row && cell.col === neighbor.col,
                    ),
            )

            if (possibleNextMoves.length > 0)
            {
                const randomIndex = Math.floor(Math.random() * possibleNextMoves.length)
                const hintCell = possibleNextMoves[randomIndex]
                messageElement.textContent = `Try the cell at row ${hintCell.row + 1}, column ${hintCell.col + 1}.`
            } else
            {
                messageElement.textContent = "No obvious next move."
            }
        }
    }

    function startTimer() {
        startTime = Date.now()
        timerInterval = setInterval(() => {
            const elapsedTime = Math.floor((Date.now() - startTime) / 1000)
            timeElement.textContent = elapsedTime
        }, 1000)
    }

    function restartGame() {
        createGrid()
        generateNumberedCells()
        resetToSavedPuzzle() // Call the reset logic when retry is clicked
    }

    restartButton.addEventListener("click", restartGame)
    hintButton.addEventListener("click", showHint)
    toggleLegalMovesButton.addEventListener("click", () => {
        showLegalMoves = !showLegalMoves
        toggleLegalMovesButton.textContent = showLegalMoves
            ? "Hide Legal Moves"
            : "Show Legal Moves"
        updateLegalMoveIndicators() // Refresh visibility
    })

    document.addEventListener('DOMContentLoaded', function () {
        const toggleBtn = document.getElementById('toggleStyleButton');
        const futuristic = document.getElementById('futuristic-style');
        const classic = document.getElementById('classic-style');
        let usingFuturistic = true;
        toggleBtn.addEventListener('click', function () {
            usingFuturistic = !usingFuturistic;
            futuristic.disabled = !usingFuturistic;
            classic.disabled = usingFuturistic;
        });
    });

    createGrid()
    generateNumberedCells()
</script>

</html>