{"version": 3, "file": "_page.svelte-CHyZwDjL.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/_page.svelte.js"], "sourcesContent": ["import { G as attr, F as escape_html, I as bind_props, E as pop, A as push, J as ensure_array_like, K as head, M as attr_class, N as stringify } from \"../../chunks/index.js\";\nimport \"@sveltejs/kit/internal\";\nimport \"../../chunks/exports.js\";\nimport \"../../chunks/state.svelte.js\";\nimport \"../../chunks/MumsNumbers.svelte_svelte_type_style_lang.js\";\nimport \"phaser\";\nimport \"pixi.js\";\nfunction RoomIdManager($$payload, $$props) {\n  push();\n  let randomRoomId = \"\";\n  let inputRoomId = \"\";\n  function getRoomId() {\n    return inputRoomId.trim() || randomRoomId;\n  }\n  $$payload.out.push(`<div class=\"space-y-4 p-4 bg-white/10 rounded-lg backdrop-blur-sm\"><div><label class=\"block text-sm font-medium text-white mb-2\">Generated Room ID:</label> <div class=\"flex items-center gap-2\"><input type=\"text\"${attr(\"value\", randomRoomId)} readonly class=\"flex-1 px-3 py-2 bg-black/20 border border-white/20 rounded text-white text-sm font-mono\"/> <button class=\"px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm transition-colors\">${escape_html(\"Copy\")}</button></div></div> <div><label class=\"block text-sm font-medium text-white mb-2\">Or enter custom Room ID (optional):</label> <input type=\"text\"${attr(\"value\", inputRoomId)} placeholder=\"Leave empty to use generated ID\" class=\"w-full px-3 py-2 bg-black/20 border border-white/20 rounded text-white placeholder-white/50\"/></div> <p class=\"text-xs text-white/70\">${escape_html(inputRoomId.trim() ? `Using custom room: ${inputRoomId.trim()}` : `Using generated room: ${randomRoomId}`)}</p></div>`);\n  bind_props($$props, { getRoomId });\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  const games = [\n    {\n      id: \"finger-frenzy\",\n      name: \"Finger Frenzy\",\n      description: \"Fast-paced tapping game\",\n      color: \"from-red-500 to-orange-500\",\n      available: true\n    },\n    {\n      id: \"bingo\",\n      name: \"Bingo\",\n      description: \"Classic bingo game\",\n      color: \"from-blue-500 to-purple-500\",\n      available: true\n    },\n    {\n      id: \"matching-mayhem\",\n      name: \"Matching Mayhem\",\n      description: \"Memory matching game\",\n      color: \"from-green-500 to-teal-500\",\n      available: true\n    },\n    {\n      id: \"numbers\",\n      name: \"Number Sequence\",\n      description: \"Number pattern recognition\",\n      color: \"from-purple-500 to-pink-500\",\n      available: true\n    },\n    {\n      id: \"mums-numbers\",\n      name: \"Mums Numbers!\",\n      description: \"Draw a continuous path through numbers 1-5 covering all grid cells\",\n      color: \"from-cyan-500 to-blue-500\",\n      available: true\n    }\n  ];\n  const each_array = ensure_array_like(games);\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>TicTaps Games</title>`;\n    $$payload2.out.push(`<meta name=\"description\" content=\"Select from our collection of exciting mini-games\"/>`);\n  });\n  $$payload.out.push(`<div class=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\"><header class=\"text-center py-12 px-4\"><h1 class=\"text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">TicTaps Games</h1></header> <div class=\"container mx-auto px-4 mb-8\"><div class=\"max-w-md mx-auto\">`);\n  RoomIdManager($$payload, {});\n  $$payload.out.push(`<!----></div></div> <main class=\"container mx-auto px-4 pb-12\"><div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto\"><!--[-->`);\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let game = each_array[$$index];\n    $$payload.out.push(`<div class=\"game-card group svelte-um5wpz\">`);\n    if (game.available) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<button${attr_class(`block h-full w-full p-6 rounded-xl bg-gradient-to-br ${stringify(game.color)} shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/10 text-left cursor-pointer`, \"svelte-um5wpz\")}><div class=\"flex items-center justify-between mb-4\"><div class=\"px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white\">Available</div></div> <h2 class=\"text-2xl font-bold text-white mb-2 group-hover:text-yellow-200 transition-colors\">${escape_html(game.name)}</h2> <p class=\"text-white/80 text-sm leading-relaxed mb-4\">${escape_html(game.description)}</p> <div class=\"flex items-center text-white/60 text-sm\"><svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path></svg> Play Now</div></button>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<div class=\"block h-full p-6 rounded-xl bg-gradient-to-br from-gray-600 to-gray-700 shadow-lg border border-white/10 opacity-60 cursor-not-allowed\"><div class=\"flex items-center justify-between mb-4\"><div class=\"px-3 py-1 bg-gray-500/50 rounded-full text-sm font-medium text-gray-300\">Coming Soon</div></div> <h2 class=\"text-2xl font-bold text-gray-300 mb-2\">${escape_html(game.name)}</h2> <p class=\"text-gray-400 text-sm leading-relaxed mb-4\">${escape_html(game.description)}</p> <div class=\"flex items-center text-gray-500 text-sm\"><svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path></svg> Not Available</div></div>`);\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  }\n  $$payload.out.push(`<!--]--></div></main></div>`);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;AAOA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,OAAO,WAAW,CAAC,IAAI,EAAE,IAAI,YAAY;AAC7C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mNAAmN,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,kNAAkN,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,kJAAkJ,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,4LAA4L,EAAE,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAC/9B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC;AACpC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI;AACJ,MAAM,EAAE,EAAE,eAAe;AACzB,MAAM,IAAI,EAAE,eAAe;AAC3B,MAAM,WAAW,EAAE,yBAAyB;AAC5C,MAAM,KAAK,EAAE,4BAA4B;AACzC,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,KAAK,EAAE,6BAA6B;AAC1C,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,iBAAiB;AAC3B,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,KAAK,EAAE,4BAA4B;AACzC,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,KAAK,EAAE,6BAA6B;AAC1C,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,cAAc;AACxB,MAAM,IAAI,EAAE,eAAe;AAC3B,MAAM,WAAW,EAAE,oEAAoE;AACvF,MAAM,KAAK,EAAE,2BAA2B;AACxC,MAAM,SAAS,EAAE;AACjB;AACA,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;AAC7C,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,4BAA4B,CAAC;AACrD,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sFAAsF,CAAC,CAAC;AACjH,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4VAA4V,CAAC,CAAC;AACpX,EAAE,aAAa,CAAC,SAAS,EAAE,EAAE,CAAC;AAC9B,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2JAA2J,CAAC,CAAC;AACnL,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2CAA2C,CAAC,CAAC;AACrE,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,qDAAqD,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,iIAAiI,CAAC,EAAE,eAAe,CAAC,CAAC,uPAAuP,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,4DAA4D,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,4VAA4V,CAAC,CAAC;AAC39B,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uWAAuW,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,4DAA4D,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,6aAA6a,CAAC,CAAC;AACr6B,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,CAAC;AACnD,EAAE,GAAG,EAAE;AACP;;;;"}