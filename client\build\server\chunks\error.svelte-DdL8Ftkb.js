import { v as push, T as escape_html, x as pop } from './exports-CtUTVNuO.js';
import { p as page } from './index2-DNUERyeR.js';
import './state.svelte-Bbvw5NZb.js';

function Error($$payload, $$props) {
  push();
  $$payload.out.push(`<h1>${escape_html(page.status)}</h1> <p>${escape_html(page.error?.message)}</p>`);
  pop();
}

export { Error as default };
//# sourceMappingURL=error.svelte-DdL8Ftkb.js.map
