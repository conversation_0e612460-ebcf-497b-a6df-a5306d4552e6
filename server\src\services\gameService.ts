import { GameType, GameState, GameAction, GameResults, EndReason, RoomSession } from '../types/game';
import { logger } from '../utils/logger';
import { DEFAULT_GAME_DURATION } from '../utils/constants';
import type { Server, Socket } from 'socket.io';
import { sessionService } from './sessionService';

export class GameService {
  // private roomSessions: Map<string, RoomSession> = new Map();
  // Timers keyed by roomId:userId (userId may be omitted for legacy room-level usage)
  private gameTimers: Map<string, { timer: NodeJS.Timeout, duration: number }> = new Map();
  // Optional controller-provided hooks invoked just before timeout endGame
  private timeoutHooks: Map<string, () => void> = new Map();


  createGameState(roomId: string, gameType: GameType, initialLives: number, userId?: string): GameState {
    const gameState: GameState = {
      gameType,
      status: 'waiting',
      score: 0,
      scoreAry: [],
      lives: initialLives
    };

    const roomSession = sessionService.getRoomSession(roomId);
    if (!roomSession || roomSession.users.size === 0) {
      logger.warn(`Cannot attach game state: no users in room ${roomId}`);
      return gameState;
    }

    let targetSession = userId ? roomSession.users.get(userId) || null : null;
    if (!targetSession) {
      // Fallback to first user for backward compatibility
      targetSession = Array.from(roomSession.users.values())[0] || null;
    }
    if (!targetSession) {
      logger.warn(`Cannot attach game state: no valid target user in room ${roomId}`);
      return gameState;
    }
    targetSession.gameState = gameState;

    logger.info(`Game state created for room ${roomId}, game type: ${gameType}${initialLives ? `, lives: ${initialLives}` : ''}`);
    return gameState;
  }

  getGameState(roomId: string, userId?: string): GameState | undefined {
    const roomSession = sessionService.getRoomSession(roomId);
    if (!roomSession || roomSession.users.size === 0) return undefined;
    const targetSession = (userId ? roomSession.users.get(userId) : Array.from(roomSession.users.values())[0]) || null;
    return targetSession?.gameState ?? undefined;
  }

  startGame(roomId: string , socket: Socket, userId?: string): boolean {
    const gameState = this.getGameState(roomId, userId);
    if (!gameState) {
      logger.warn(`Cannot start game: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'waiting') {
      logger.warn(`Cannot start game: game in room ${roomId} is not in waiting state`);
      return false;
    }

    gameState.status = 'active';
    gameState.startTime = Date.now();

    // Set up game timer
    const duration = DEFAULT_GAME_DURATION[gameState.gameType as keyof typeof DEFAULT_GAME_DURATION] || 60;

    const timerKey = `${roomId}:${userId ?? ''}`;
    const timer = setInterval(() => {
      console.log(`Game timer tick in room ${roomId}${userId ? ` (user ${userId})` : ''}`);
      const timerObj = this.gameTimers.get(timerKey);
      if (timerObj) {
        timerObj.duration--;

        this.gameTimers.set(timerKey, timerObj);
        logger.debug(`Game timer tick in room ${roomId}${userId ? ` (user ${userId})` : ''}, remaining duration: ${timerObj.duration}ms`);

        socket.emit('timer_tick', {
          duration: timerObj.duration
        });

        // If duration reaches zero, invoke any registered timeout hook, then end the game
        if (timerObj.duration <= 0) {
          logger.info(`Game timer ended in room ${roomId}${userId ? ` (user ${userId})` : ''}`);

          // Run controller-provided timeout hook before ending the game (partial credit, etc.)
          const hook = this.timeoutHooks.get(timerKey);
          if (hook) {
            try {
              hook();
            } catch (e) {
              logger.error('Error running timeout hook:', e);
            } finally {
              this.timeoutHooks.delete(timerKey);
            }
          }

          const results = this.endGame(roomId, 'timeout', userId);

          if (results) {
            logger.info(`Game ended in room ${roomId}, reason: ${results.endReason}`);
            socket.emit('ended', results);
          }
        }
      }
    }, 1000);

    this.gameTimers.set(timerKey, { timer, duration });

    logger.info(`Game started in room ${roomId}${userId ? ` (user ${userId})` : ''}, duration: ${duration}ms`);
    // Emit initial leaderboard snapshot on start
    sessionService.emitRoomLeaderboard(roomId);
    return true;
  }

  endGame(roomId: string, reason: EndReason, userId?: string): GameResults | null {
    // Clear timer(s) first to avoid repeated callbacks
    const keysToClear = userId !== undefined
      ? [`${roomId}:${userId}`]
      : Array.from(this.gameTimers.keys()).filter(k => k.startsWith(`${roomId}:`));
    for (const key of keysToClear) {
      const gameTimer = this.gameTimers.get(key);
      if (gameTimer) {
        clearInterval(gameTimer.timer);
        this.gameTimers.delete(key);
      }
    }

    const gameState = this.getGameState(roomId, userId);
    if (!gameState) {
      logger.debug(`Skip endGame: no game state for room ${roomId}${userId ? ` (user ${userId})` : ''}`);
      return null;
    }

    gameState.status = 'ended';

    const results: GameResults = {
      gameType: gameState.gameType,
      score: gameState.score,
      endReason: reason,
    };

    // Emit leaderboard snapshot on end (status may have changed)
    sessionService.emitRoomLeaderboard(roomId);

    logger.info(`Game ended in room ${roomId}${userId ? ` (user ${userId})` : ''}, reason: ${reason}`);
    return results;
  }

  updateScore(roomId: string, score: number, action: "add" | "subtract", userId?: string): boolean {
    const gameState = this.getGameState(roomId, userId);
    if (!gameState) {
      logger.warn(`Cannot update score: no game state for room ${roomId}`);
      return false;
    }


    if (gameState.status !== 'active') {
      logger.warn(`Cannot update score: game in room ${roomId} is not active`);
      return false;
    }

    switch (action) {
      case "add":
        gameState.score += score;
        gameState.scoreAry.push(score);
        // Emit leaderboard update for the room when a user's score changes
        sessionService.emitRoomLeaderboard(roomId);
        break;
      case "subtract":
        const actual = Math.min(score, gameState.score);

        gameState.score -= actual;
        gameState.scoreAry.push(-actual);
        sessionService.emitRoomLeaderboard(roomId);
        break;
      default:
        logger.warn(`Cannot update score: invalid action ${action}`);
        return false;
    }

    logger.info(`Score updated ${gameState.score} for room ${roomId}${userId ? ` (user ${userId})` : ''}`);
    return true;
  }

  updateLives(roomId: string, lives: number, userId?: string): boolean {
    const gameState = this.getGameState(roomId, userId);
    if (!gameState) {
      logger.warn(`Cannot update lives: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot update lives: game in room ${roomId} is not active`);
      return false;
    }

    gameState.lives = lives;
    // Emit leaderboard/opponent updates when lives change
    sessionService.emitRoomLeaderboard(roomId);

    logger.info(`Lives updated in room ${roomId}${userId ? ` (user ${userId})` : ''}: ${lives}`);
    return true;
  }

  deductLife(roomId: string, userId?: string): { newLives: number; gameEnded: boolean } {
    const gameState = this.getGameState(roomId, userId);
    if (!gameState || gameState.lives === undefined) {
      return { newLives: 0, gameEnded: false };
    }

    const newLives = Math.max(0, gameState.lives - 1);
    this.updateLives(roomId, newLives, userId);

    const gameEnded = newLives <= 0;
    if (gameEnded) {
      this.endGame(roomId, 'no_lives', userId);
    }

    return { newLives, gameEnded };
  }

  getLives(roomId: string, userId?: string): number {
    const gameState = this.getGameState(roomId, userId);
    return gameState?.lives || 0;
  }

  processGameAction(roomId: string, action: GameAction, userId?: string): boolean {
    const gameState = this.getGameState(roomId, userId);
    if (!gameState) {
      logger.warn(`Cannot process action: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot process action: game in room ${roomId} is not active`);
      return false;
    }

    // Basic action validation (extend based on game type)
    if (!this.validateAction(gameState.gameType, action)) {
      logger.warn(`Invalid action in room ${roomId}:`, action);
      return false;
    }

    logger.debug(`Action processed in room ${roomId}${userId ? ` (user ${userId})` : ''}:`, action.type);
    return true;
  }

  deleteGameState(roomId: string, userId?: string): boolean {
    const timerKey = `${roomId}:${userId ?? ''}`;
    const gameTimer = this.gameTimers.get(timerKey);
    if (gameTimer) {
      clearInterval(gameTimer.timer);
      this.gameTimers.delete(timerKey);
    }

    const roomSession = sessionService.getRoomSession(roomId);
    if (!roomSession || roomSession.users.size === 0) {
      return false;
    }

    let targetSession = userId ? roomSession.users.get(userId) || null : null;
    if (!targetSession) {
      targetSession = Array.from(roomSession.users.values())[0] || null;
    }
    if (!targetSession) return false;

    const hadState = !!targetSession.gameState;
    targetSession.gameState = null;

    if (hadState) {
      logger.info(`Game state deleted for room ${roomId}${userId ? ` (user ${userId})` : ''}`);
    }
    return hadState;
  }

  private validateAction(gameType: GameType, action: GameAction): boolean {
    // Basic validation
    if (!action.type || typeof action.timestamp !== 'number') {
      return false;
    }

    // Game-specific validation
    switch (gameType) {
      case 'finger-frenzy':
        return ['tile_tap'].includes(action.type);
      case 'bingo':
        return ['cell_mark', 'call_number'].includes(action.type);
      case 'matching-mayhem':
        return ['card_select'].includes(action.type);
      case 'numbers':
        return ['number_click'].includes(action.type);
      case 'mums-numbers':
        return ['path_move'].includes(action.type);
      default:
        return true;
    }
  }

  // Register a controller-provided hook to be invoked immediately before timeout endGame
  registerTimeoutHook(roomId: string, userId: string | undefined, hook: () => void): void {
    const timerKey = `${roomId}:${userId ?? ''}`;
    this.timeoutHooks.set(timerKey, hook);
    logger.info(`Registered timeout hook for room ${roomId}${userId ? ` (user ${userId})` : ''}`);
  }

}

export const gameService = new GameService();
export default gameService;
