<script lang="ts">
  import Icon from "@iconify/svelte";

  let {
    waiting = true,
    score = null,
    lives = null,
    maxLives,
    name = "Opponent",
    avatarUrl,
    compare,
  } = $props<{
    waiting?: boolean;
    score?: number | null;
    lives?: number | null;
    maxLives: number;
    name?: string;
    avatarUrl?: string;
    compare?: "win" | "lose" | "tie" | null;
  }>();

  let borderClass = $derived(
    compare === "win"
      ? "border-green-400"
      : compare === "lose"
        ? "border-red-400"
        : compare === "tie"
          ? "border-yellow-400"
          : "border-white/40"
  );
</script>

<div class="pointer-events-auto flex items-center gap-2">
  <div class="flex flex-col items-end gap-1">
    {#if waiting || score === null}
      <div class="text-[2vh] italic opacity-80">Waiting for player…</div>
    {:else}
      <div class="flex items-center gap-2">
        <div class="flex flex-col">
          <div class="text-[1.8vh] text-right font-medium leading-tight">
            {name}
          </div>

          <div class="flex flex-row items-center gap-2">
            <span class="font-bold text-[2vh] align-middle">{score}</span>

            <div class="flex gap-1">
              {#each Array(maxLives) as _, i (i)}
                {#if lives !== null && i < lives}
                  <Icon
                    height="2vh"
                    color="red"
                    icon="material-symbols:favorite"
                  />
                {:else}
                  <Icon
                    height="2vh"
                    color="red"
                    icon="material-symbols:favorite-outline"
                  />
                {/if}
              {/each}
            </div>
          </div>
        </div>

        {#if avatarUrl}
          <img
            src={avatarUrl}
            alt="Opponent avatar"
            class="w-[5vh] h-[5vh] rounded-full object-cover border-2 {borderClass}"
          />
        {:else}
          <div
            class="w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 {borderClass}"
          >
            <Icon icon="mdi:account" color="white" height="3vh" />
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>
