import{e as g,u as d,g as c,h as m,i as b,j as i,k as p,l as h,m as k,n as v}from"./4UAai7vz.js";function x(t=!1){const s=g,e=s.l.u;if(!e)return;let f=()=>h(s.s);if(t){let a=0,n={};const _=k(()=>{let l=!1;const r=s.s;for(const o in r)r[o]!==n[o]&&(n[o]=r[o],l=!0);return l&&a++,a});f=()=>p(_)}e.b.length&&d(()=>{u(s,f),i(e.b)}),c(()=>{const a=m(()=>e.m.map(b));return()=>{for(const n of a)typeof n=="function"&&n()}}),e.a.length&&c(()=>{u(s,f),i(e.a)})}function u(t,s){if(t.l.s)for(const e of t.l.s)p(e);s()}v();export{x as i};
