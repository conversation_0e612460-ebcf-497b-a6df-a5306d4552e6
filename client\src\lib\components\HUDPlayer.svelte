<script lang="ts">
  import Icon from "@iconify/svelte";

  let {
    score,
    lives,
    maxLives,
    name = "You",
    avatarUrl,
    compare,
  } = $props<{
    score: number;
    lives: number;
    maxLives: number;
    name?: string;
    avatarUrl?: string;
    compare?: "win" | "lose" | "tie" | null;
  }>();

  let borderClass = $derived(
    compare === "win"
      ? "border-green-400"
      : compare === "lose"
        ? "border-red-400"
        : compare === "tie"
          ? "border-yellow-400"
          : "border-white/40"
  );
</script>

<div class="pointer-events-auto flex flex-col items-start gap-2">
  <!-- Avatar + Name -->
  <div class="flex items-center gap-2">
    {#if avatarUrl}
      <img
        src={avatarUrl}
        alt="Player avatar"
        class="w-[5vh] h-[5vh] rounded-full object-cover border-2 {borderClass}"
      />
    {:else}
      <div
        class="w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 {borderClass}"
      >
        <Icon icon="mdi:account" color="white" height="3vh" />
      </div>
    {/if}
    <div class="flex flex-col">
      <div class="text-[1.8vh] text-left font-medium leading-tight">{name}</div>
      <div class="flex flex-row items-center gap-2">
        <!-- Score + Lives -->
        <div class="flex gap-1">
          {#each Array(maxLives) as _, i (i)}
            {#if i < lives}
              <Icon height="2vh" color="red" icon="material-symbols:favorite" />
            {:else}
              <Icon
                height="2vh"
                color="red"
                icon="material-symbols:favorite-outline"
              />
            {/if}
          {/each}
        </div>
        <span class="font-bold text-[2vh] align-middle">{score}</span>
      </div>
    </div>
  </div>
</div>
