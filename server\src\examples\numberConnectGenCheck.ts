import { LevelGenerator } from "../controllers/games/NumberConnectLevelGenerator.js";

async function main() {
  const gen = new LevelGenerator(5, 5);
  let ok = 0;
  const trials = 10;

  for (let i = 0; i < trials; i++) {
    const lvl = gen.generateSolvablePuzzle();
    const valid = gen.validateSolution(lvl.solutionPath, lvl.numberPositions);
    if (valid) ok++;
    console.log(
      `Trial ${i + 1}: pathLen=${lvl.solutionPath.length}, valid=${valid}, numbers=`,
      Object.entries(lvl.numberPositions)
        .sort((a, b) => Number(a[0]) - Number(b[0]))
        .map(([n, p]) => `${n}(${p.row},${p.col})`)
        .join(" ")
    );
  }

  console.log(`\nValid ${ok}/${trials}`);
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});

