import express from 'express';
import http from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import * as dotenv from 'dotenv';

import { setupSocketHandlers } from './sockets/index.js';
import { jwtService } from './services/jwtService.js';
import { logger } from './utils/logger.js';
import { GAME_TYPES } from './utils/constants.js';

// Load environment variables
dotenv.config();

const app = express();
const server = http.createServer(app);

const CORS_ORIGIN : string = process.env.CORS_ORIGIN || "https://games.tictaps.dev"
console.log(CORS_ORIGIN)

// Allowed origins (add more as needed)
const allowedOrigins = [
  "http://localhost:5173",
   CORS_ORIGIN
];

// CORS configuration for Express routes
app.use(cors({
  origin: (origin, callback) => {
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error(`CORS blocked for origin: ${origin}`));
    }
  },
  credentials: true
}));

app.use(express.json());

// JWT Generation endpoint for development/testing
app.post('/api/generate-token', (req, res) => {
  try {
    const { gameId, userId, username, clientSeed } = req.body;

    // Validate required fields
    if (!gameId) {
      return res.status(400).json({
        error: 'Missing required field: gameId'
      });
    }

    // Validate game type
    const validGameTypes = Object.values(GAME_TYPES);
    if (!validGameTypes.includes(gameId)) {
      return res.status(400).json({
        error: 'Invalid gameId',
        validGameTypes
      });
    }

    // Generate unique identifiers for this game session
    const roomId = req.body.roomId || `room-${gameId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const scoreSubmitId = `score-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const authToken = `auth-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Create user data for JWT
    const userData = {
      userId: userId || `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      username: username || `Player-${Math.floor(Math.random() * 1000)}`,
      gameId,
      roomId,
      scoreSubmitId,
      authToken,
      clientSeed: clientSeed || `client-${roomId}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
    };

    // Generate JWT token
    const token = jwtService.generateToken(userData);

    logger.info('JWT token generated for game', {
      gameId,
      userId: userData.userId,
      roomId,
      scoreSubmitId
    });

    res.json({
      success: true,
      token,
      userData: {
        gameId,
        roomId,
        userId: userData.userId,
        username: userData.username,
        clientSeed: (userData as any).clientSeed
      }
    });

  } catch (error) {
    logger.error('Error generating JWT token:', error);
    res.status(500).json({
      error: 'Failed to generate token',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'tictaps-games-server'
  });
});

// CORS configuration for Socket.IO
const io = new Server(server, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST"],
    credentials: true
  }
});

setupSocketHandlers(io);

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`TicTaps Games Server listening on http://localhost:${PORT}`);
});
