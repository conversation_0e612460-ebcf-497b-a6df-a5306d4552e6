# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Development Commands

### Client (SvelteKit GameApp)
All client commands should be run from the `client/` directory:

```bash
cd client/
pnpm install          # Install dependencies
pnpm run dev          # Start development server
pnpm run build        # Build for production
pnpm run start        # Start production server
pnpm run preview      # Preview production build
pnpm run check        # Run Svelte type checking
pnpm run check:watch  # Run type checking in watch mode
```

### Server (Node.js Game Server)
All server commands should be run from the `server/` directory:

```bash
cd server/
pnpm install          # Install dependencies
pnpm run dev          # Start development server with hot reload
pnpm run build        # Build TypeScript to JavaScript
pnpm run start        # Start production server
```

### Testing Individual Games
To test a specific game during development:
1. Start both client and server: `pnpm run dev` in both directories
2. Navigate to `http://localhost:5173/game/{game-id}` where game-id is:
   - `finger-frenzy` - Fast-paced finger tapping game
   - `bingo` - Classic multiplayer bingo
   - `matching-mayhem` - Memory/matching card game
   - `number-sequence` - Pattern recognition game

## Architecture Overview

### System Architecture
This is a multiplayer gaming platform with three main components:

```
PWA (Parent) --> iframe postMessage --> GameApp (Client)
GameApp --> Socket.IO --> Node.js Game Server
Game Server --> Auth/Score APIs --> Python Backend (external)
```

**TicTaps Games** is a SvelteKit-based game client that runs in an iframe within a PWA. The client receives authentication and game data via postMessage and communicates with a Node.js game server via Socket.IO for real-time multiplayer functionality.

### Directory Structure

```
├── client/           # SvelteKit GameApp
│   ├── src/
│   │   ├── lib/
│   │   │   ├── components/      # Shared UI components (GameHUD, Countdown, EndGame)
│   │   │   ├── games/          # Phaser 3 game integrations
│   │   │   ├── stores/         # Game state management (Svelte stores)
│   │   │   ├── socket/         # Socket.IO client setup
│   │   │   └── utils/          # PostMessage handlers, utilities
│   │   └── routes/game/[id]/   # Dynamic game routing
│   └── static/assets-*/        # Game-specific assets
├── server/           # Node.js real-time game server
│   └── src/
│       ├── controllers/games/  # Game-specific logic controllers
│       ├── services/          # Authentication, game state management
│       ├── sockets/           # Socket.IO event handlers
│       └── types/             # TypeScript definitions per game
└── task.md          # Comprehensive project roadmap and tasks
```

### Communication Patterns

1. **PWA Integration**: Client receives via postMessage API:
   - Auth tokens (JWT)
   - Submit score IDs for tracking
   - Room IDs for multiplayer
   - Opponent scores for display
   - Game IDs for routing

2. **Real-time Multiplayer**: Socket.IO communication between client and server for:
   - Game room management
   - Real-time score synchronization
   - Timer management
   - Game state updates

3. **Game Architecture**: Each Phaser 3 game follows consistent structure:
   - `index.ts` - Game class with init/start/pause/resume/destroy methods
   - `scenes/` - Phaser 3 scenes (PreloadScene, GameStartScene, GameScene, GameEndScene)
   - `managers/` - Game logic (ScoreManager, TimerManager, LivesManager)
   - `objects/` - Game-specific Phaser objects
   - `utils/` - TicTapsConnector for Socket.IO integration

### Game Integration Pattern

Games are integrated through a factory pattern in `client/src/lib/games/index.ts`. Each game class implements:
- `init()` - Initialize Phaser 3 instance
- `start()` - Start game logic  
- `pause()/resume()` - Pause/resume functionality
- `destroy()` - Clean up resources
- `getCurrentScore()` - Return current score

Games receive callbacks:
- `onScoreUpdate(score)` - Called when score changes
- `onGameComplete(finalScore)` - Called when game ends

### Shared UI Components

All games use generic UI components:
- **GameHUD.svelte** - Universal HUD showing score, time, lives, opponent score
- **Countdown.svelte** - Shared countdown timer
- **EndGame.svelte** - Generic end game screen
- **StartScreen.svelte** - Game start screen
- **Preloading.svelte** - Loading screen

### Server Game Controllers

Each game has its own controller in `server/src/controllers/games/`:
- Handles game-specific Socket.IO events
- Manages game timers and scoring logic
- Validates player actions and prevents cheating
- Synchronizes game state between players

### Technology Stack

**Client:**
- SvelteKit 2.x with TypeScript
- Phaser 3.90.0 (game engine)
- Tailwind CSS 4.x (styling)
- Socket.IO client (real-time communication)

**Server:**
- Node.js with Express.js
- Socket.IO (real-time server)
- TypeScript for type safety
- JWT authentication validation
- In-memory hash maps for game state (Redis planned for production)

## Important Development Notes

### Game Development
- All games share the same UI framework and Socket.IO communication patterns
- Game assets are organized per-game in `client/static/assets-{game}/`
- Each game has its own TypeScript types and Phaser 3 scene structure
- Server-side game logic prevents cheating and validates all player actions

### Socket.IO Integration
- Client connects to server on game start with authentication token
- Server validates tokens and manages game rooms using in-memory storage
- Real-time events include: game actions, score updates, timer ticks, game state changes
- Each game has specific event handlers in both client and server

### PostMessage Communication
- Client listens for PWA data on window load and via postMessage events
- Authentication tokens are received from parent PWA for server validation
- Room IDs enable multiplayer game joining
- Submit score IDs track score submissions back to Python backend

### Environment Setup
- Client and server each have `.env.example` files for configuration
- Both use pnpm as the package manager
- Development requires running both client and server simultaneously
- Client runs on port 5173, server on port 3000 (configurable via env)

## Next Development Steps

Based on `task.md`, current priorities include:
1. Integrating remaining games (Bingo, Matching Mayhem, Number Sequence) with generic UI components
2. Implementing score submission to Python backend from server
3. Adding comprehensive anti-cheat measures
4. Setting up Redis for production game state persistence
5. Creating comprehensive testing harness for multiplayer flows
