import { Server, Socket } from 'socket.io';
import { Finger<PERSON><PERSON>zy<PERSON><PERSON>roller, BingoController, MatchingMayhemController, NumberSequenceController, NumberConnectController } from '../controllers';
import gameService from '../services/gameService';
import { authMiddleware, AuthenticatedSocket, getAuthenticatedUser, validateRoomAccess } from '../middleware/auth.js';
import { sessionService } from '../services/sessionService.js';
import { logger } from '../utils/logger.js';

// Initialize game controllers
const fingerFrenzyController = new FingerFrenzyController(gameService);
const bingoController = new BingoController(gameService);
const matchingMayhemController = new MatchingMayhemController(gameService);
const numberSequenceController = new NumberSequenceController(gameService);
const numberConnectController = new NumberConnectController(gameService);

export function setupSocketHandlers(io: Server) {
  // Provide io to sessionService for room broadcasts (leaderboard updates, etc.)
  sessionService.setIo(io);
  // Apply authentication middleware
  io.use(authMiddleware);

  io.on('connection', (socket: Socket) => {
    // After authentication middleware, socket has user property
    const authenticatedSocket = socket as AuthenticatedSocket;
    const user = getAuthenticatedUser(authenticatedSocket);
    if (!user) {
      logger.error('Authenticated socket missing user data', { socketId: authenticatedSocket.id });
      authenticatedSocket.disconnect();
      return;
    }

    logger.info(`Authenticated client connected`, {
      socketId: authenticatedSocket.id,
      userId: user.userId,
      gameId: user.gameId,
      roomId: user.roomId
    });

    // Create user session
    sessionService.createUserSession(user, authenticatedSocket.id);

    // Automatically join the user's designated room
    authenticatedSocket.join(user.roomId);

    // Setup game-specific handlers
    fingerFrenzyController.setupSocketHandlers(authenticatedSocket);
    bingoController.setupSocketHandlers(authenticatedSocket);
    matchingMayhemController.setupSocketHandlers(authenticatedSocket);
    numberSequenceController.setupSocketHandlers(authenticatedSocket);
    numberConnectController.setupSocketHandlers(authenticatedSocket);

    // Handle room joining (with authentication validation)
    authenticatedSocket.on('join_room', (data) => {
      const { roomId, gameId } = data;

      // Validate room access (for now, allow any room join)
      // if (!validateRoomAccess(user, roomId)) {
      //   authenticatedSocket.emit('room_error', {
      //     message: 'Access denied to room',
      //     code: 'ROOM_ACCESS_DENIED'
      //   });
      //   return;
      // }

      // Update user activity
      sessionService.updateUserActivity(user.userId);

      logger.info(`User joining room`, {
        userId: user.userId,
        roomId,
        gameId: gameId || user.gameId
      });

      // Join the socket room
      authenticatedSocket.join(roomId);

      // Get current room state
      const roomUsers = sessionService.getRoomUsers(roomId);
      const isFirstUser = roomUsers.length === 0;

      // Determine host (first user becomes host)
      const hostUserId = isFirstUser ? user.userId : roomUsers[0]?.user.userId;

      // Create player list with current room state
      const players = roomUsers.map(userSession => ({
        userId: userSession.user.userId,
        name: userSession.user.username,
        displayName: userSession.user.username,
        isHost: userSession.user.userId === hostUserId,
        isReady: false, // TODO: Track ready state in session
        isConnected: true,
        joinedAt: userSession.connectedAt
      }));

      // Add current user to the list
      players.push({
        userId: user.userId,
        name: user.username,
        displayName: user.username,
        isHost: user.userId === hostUserId,
        isReady: false,
        isConnected: true,
        joinedAt: new Date()
      });

      // Send room joined confirmation to the joining user
      authenticatedSocket.emit('room_joined', {
        roomId,
        roomCode: roomId, // For now, use roomId as room code
        userId: user.userId,
        gameId: gameId || user.gameId,
        players,
        hostUserId,
        maxPlayers: 8,
        message: 'Successfully joined room'
      });

      // Notify other players in the room about the new player
      authenticatedSocket.to(roomId).emit('player_joined', {
        userId: user.userId,
        name: user.username,
        displayName: user.username,
        gameId: gameId || user.gameId
      });

      // Broadcast updated player list to all users in room
      io.to(roomId).emit('room_players_update', {
        players,
        hostUserId
      });
    });

    // Handle room leaving
    authenticatedSocket.on('leave_room', (data) => {
      const { roomId } = data;

      logger.info(`User leaving room`, {
        userId: user.userId,
        roomId
      });

      // Leave the socket room
      authenticatedSocket.leave(roomId);

      // Get current room state before removing user
      const roomUsers = sessionService.getRoomUsers(roomId);
      const wasHost = roomUsers.length > 0 && roomUsers[0]?.user.userId === user.userId;

      // Send confirmation to leaving user
      authenticatedSocket.emit('room_left', { roomId, userId: user.userId });

      // Notify other players in the room
      authenticatedSocket.to(roomId).emit('player_left', { userId: user.userId });

      // Handle host transfer if the leaving user was the host
      if (wasHost && roomUsers.length > 1) {
        // Find next user to become host (second user in the list)
        const newHost = roomUsers.find(userSession => userSession.user.userId !== user.userId);

        if (newHost) {
          logger.info(`Transferring host from ${user.userId} to ${newHost.user.userId} in room ${roomId}`);

          // Notify all remaining players about host change
          io.to(roomId).emit('host_changed', {
            roomId,
            oldHostUserId: user.userId,
            hostUserId: newHost.user.userId,
            message: `${newHost.user.username} is now the host`
          });

          // Update player list
          const updatedPlayers = roomUsers
            .filter(userSession => userSession.user.userId !== user.userId)
            .map(userSession => ({
              userId: userSession.user.userId,
              name: userSession.user.username,
              displayName: userSession.user.username,
              isHost: userSession.user.userId === newHost.user.userId,
              isReady: false, // TODO: Track ready state
              isConnected: true,
              joinedAt: userSession.connectedAt
            }));

          // Broadcast updated player list
          io.to(roomId).emit('room_players_update', {
            players: updatedPlayers,
            hostUserId: newHost.user.userId
          });
        }
      } else if (!wasHost) {
        // Just update the player list for non-host leaving
        const updatedPlayers = roomUsers
          .filter(userSession => userSession.user.userId !== user.userId)
          .map(userSession => ({
            userId: userSession.user.userId,
            name: userSession.user.username,
            displayName: userSession.user.username,
            isHost: roomUsers[0]?.user.userId === userSession.user.userId,
            isReady: false, // TODO: Track ready state
            isConnected: true,
            joinedAt: userSession.connectedAt
          }));

        // Broadcast updated player list
        io.to(roomId).emit('room_players_update', {
          players: updatedPlayers,
          hostUserId: roomUsers[0]?.user.userId
        });
      }
    });

    // Handle player ready status
    authenticatedSocket.on('player_ready', (data) => {
      const { roomId, playerId, ready } = data;
      console.log(`Player ${playerId} ready status: ${ready} in room ${roomId}`);

      // Broadcast to room
      authenticatedSocket.to(roomId).emit('player_ready_update', { playerId, ready });
    });

    // Handle game actions
    authenticatedSocket.on('game_action', (data) => {
      const { roomId, playerId, action, gameData } = data;
      console.log(`Game action from ${playerId} in room ${roomId}:`, action);

      // Broadcast action to other players in the room
      authenticatedSocket.to(roomId).emit('opponent_action', {
        playerId,
        action,
        gameData,
        timestamp: Date.now()
      });
    });

    // Handle score submission
    authenticatedSocket.on('submit_score', (data) => {
      const { roomId, playerId, score, gameType } = data;
      console.log(`Score submission from ${playerId}: ${score} in room ${roomId}`);

      // TODO: Validate and submit score to Python backend

      // Broadcast score update to room
      authenticatedSocket.to(roomId).emit('score_update', {
        playerId,
        score,
        gameType,
        timestamp: Date.now()
      });
    });

    // Handle game start (host only)
    authenticatedSocket.on('start_game', (data) => {
      const { roomId, gameType } = data;

      // Get current room state to verify host status
      const roomUsers = sessionService.getRoomUsers(roomId);
      const isHost = roomUsers.length > 0 && roomUsers[0]?.user.userId === user.userId;

      if (!isHost) {
        authenticatedSocket.emit('room_error', {
          message: 'Only the host can start the game',
          code: 'NOT_HOST'
        });
        return;
      }

      // Verify minimum players (at least 2 for multiplayer)
      if (roomUsers.length < 2) {
        authenticatedSocket.emit('room_error', {
          message: 'Need at least 2 players to start the game',
          code: 'INSUFFICIENT_PLAYERS'
        });
        return;
      }

      logger.info(`Host starting game ${gameType} in room ${roomId}`, {
        hostUserId: user.userId,
        playerCount: roomUsers.length
      });

      // Broadcast game start to all players in room
      io.to(roomId).emit('game_start', {
        gameType: gameType || user.gameId,
        roomId,
        startTime: Date.now(),
        hostUserId: user.userId,
        playerCount: roomUsers.length,
        message: 'Game has started!'
      });
    });

    // Handle game end
    authenticatedSocket.on('end_game', (data) => {
      const { roomId, gameType, results } = data;
      console.log(`Ending game ${gameType} in room ${roomId}`);

      // Broadcast game end to all players in room
      io.to(roomId).emit('game_end', {
        gameType,
        results,
        endTime: Date.now(),
        message: 'Game has ended!'
      });
    });

    // Handle disconnection
    authenticatedSocket.on('disconnect', () => {
      logger.info(`Authenticated client disconnected`, {
        socketId: authenticatedSocket.id,
        userId: user.userId,
        roomId: user.roomId
      });

      // Get all rooms this socket was in
      const rooms = Array.from(authenticatedSocket.rooms);

      // Handle room cleanup and host transfer for each room
      rooms.forEach(roomId => {
        if (roomId !== authenticatedSocket.id) {
          // Get current room state before removing user
          const roomUsers = sessionService.getRoomUsers(roomId);
          const wasHost = roomUsers.length > 0 && roomUsers[0]?.user.userId === user.userId;

          logger.info(`Cleaning up room due to disconnect`, {
            roomId,
            userId: user.userId,
            wasHost,
            remainingPlayers: roomUsers.length - 1
          });

          // Notify other players about disconnection
          authenticatedSocket.to(roomId).emit('player_disconnected', {
            userId: user.userId,
            socketId: authenticatedSocket.id,
            timestamp: Date.now()
          });

          // Handle host transfer if disconnected user was host
          if (wasHost && roomUsers.length > 1) {
            // Find next user to become host
            const newHost = roomUsers.find(userSession => userSession.user.userId !== user.userId);

            if (newHost) {
              logger.info(`Transferring host from ${user.userId} to ${newHost.user.userId} in room ${roomId} due to disconnect`);

              // Notify remaining players about host change
              io.to(roomId).emit('host_changed', {
                roomId,
                oldHostUserId: user.userId,
                hostUserId: newHost.user.userId,
                reason: 'host_disconnected',
                message: `${newHost.user.username} is now the host (previous host disconnected)`
              });

              // Update player list
              const updatedPlayers = roomUsers
                .filter(userSession => userSession.user.userId !== user.userId)
                .map(userSession => ({
                  userId: userSession.user.userId,
                  name: userSession.user.username,
                  displayName: userSession.user.username,
                  isHost: userSession.user.userId === newHost.user.userId,
                  isReady: false,
                  isConnected: true,
                  joinedAt: userSession.connectedAt
                }));

              // Broadcast updated player list
              io.to(roomId).emit('room_players_update', {
                players: updatedPlayers,
                hostUserId: newHost.user.userId
              });
            }
          }

          // Trigger cleanup in all game controllers
          fingerFrenzyController.cleanup(roomId);
          bingoController.cleanup(roomId);
          matchingMayhemController.cleanup(roomId);
          numberSequenceController.cleanup(roomId);
        }
      });

      // Clean up user session after room processing
      sessionService.removeUserSessionBySocket(authenticatedSocket.id);
    });

    // Basic message echo for testing
    authenticatedSocket.on('message', (data) => {
      console.log(`Received message: ${data}`);
      authenticatedSocket.emit('message', `Echo: ${data}`);
    });
  });
}
