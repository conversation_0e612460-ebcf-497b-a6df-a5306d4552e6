<script lang="ts">
  let randomRoomId = $state("");
  let inputRoomId = $state("");
  let copied = $state(false);

  // Generate random room ID on component mount
  $effect(() => {
    generateRandomRoomId();
  });

  function generateRandomRoomId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    randomRoomId = `room-${timestamp}-${random}`;
  }

  async function copyToClipboard() {
    try {
      await navigator.clipboard.writeText(randomRoomId);
      copied = true;
      setTimeout(() => (copied = false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }

  export function getRoomId(): string {
    return inputRoomId.trim() || randomRoomId;
  }
</script>

<div class="space-y-4 p-4 bg-white/10 rounded-lg backdrop-blur-sm">
  <div>
    <label class="block text-sm font-medium text-white mb-2">
      Generated Room ID:
    </label>
    <div class="flex items-center gap-2">
      <input
        type="text"
        value={randomRoomId}
        readonly
        class="flex-1 px-3 py-2 bg-black/20 border border-white/20 rounded text-white text-sm font-mono"
      />
      <button
        on:click={copyToClipboard}
        class="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm transition-colors"
      >
        {copied ? "Copied!" : "Copy"}
      </button>
    </div>
  </div>

  <div>
    <label class="block text-sm font-medium text-white mb-2">
      Or enter custom Room ID (optional):
    </label>
    <input
      type="text"
      bind:value={inputRoomId}
      placeholder="Leave empty to use generated ID"
      class="w-full px-3 py-2 bg-black/20 border border-white/20 rounded text-white placeholder-white/50"
    />
  </div>

  <p class="text-xs text-white/70">
    {inputRoomId.trim()
      ? `Using custom room: ${inputRoomId.trim()}`
      : `Using generated room: ${randomRoomId}`}
  </p>
</div>
