import { gameState, gameActions } from '../stores';
// import { PUBLIC_SOCKET_SERVER_URL } from '$env/static/public';
import { env } from '$env/dynamic/public';
import io from 'socket.io-client';
import { get } from 'svelte/store';
import { opponentActions } from '../stores/opponent';
import { roomState, roomActions, type Player } from '../stores/roomState';

export class SocketClient {
	private socket: any = null;
	private serverUrl: string;

	constructor() {
		this.serverUrl = env.PUBLIC_SOCKET_SERVER_URL;
		console.log(this.serverUrl)
	}

	// Helper method to get current game state
	private getCurrentGameState() {
		return get(gameState);
	}

	async connect(authToken: string, callbacks?: any): Promise<void> {
		return new Promise((resolve, reject) => {
			console.log('[SocketClient] Connecting with JWT token for server-side authentication');

			this.socket = io(this.serverUrl, {
				auth: {
					token: authToken
				}
			});

			this.socket.on('connect', () => {
				console.log('[SocketClient] Connected to game server with authenticated session');
				this.setupEventListeners(callbacks);
				resolve();
			});

			this.socket.on('connect_error', (error: any) => {
				console.error('[SocketClient] Connection error:', error);
				if (error.message?.includes('Authentication')) {
					console.error('[SocketClient] Authentication failed - invalid or expired JWT token');
				}
				reject(error);
			});
		});
	}

	private setupEventListeners(callbacks?: any) {
		if (!this.socket) return;

		this.socket.on('gameUpdate', (data: any) => {
			// Handle game state updates from server
			console.log('Game update received:', data);
		});

		this.socket.on('roomJoined', (roomData: any) => {
			console.log('Joined room:', roomData);
		});

		this.socket.on('playerJoined', (playerData: any) => {
			console.log('Player joined:', playerData);
		});

		this.socket.on('playerLeft', (playerData: any) => {
			console.log('Player left:', playerData);
		});

		this.socket.on('gameStarted', () => {
			gameActions.startGame();
		});

		this.socket.on('gameEnded', (results: any) => {
			gameActions.endGame();
			console.log('Game ended:', results);
		});

		// Generic game event listeners
		this.socket.on('initialized', (data: any) => {
			console.log('Game initialized:', data);
			// Data includes: gameState, gridState, message
			// Game is initialized but not yet started
			gameActions.initGame();
		});

		this.socket.on('started', (data: any) => {
			console.log('Game started:', data);
			// Data includes: gameId, gameState, gridState, message
			gameActions.startGame();
		});

		this.socket.on('ended', (data: any) => {
			console.log('Game ended:', data);
			// Data includes: gameId, reason, finalScore, playerId

			callbacks.onGameComplete(data.finalScore);
		});

		this.socket.on('action_result', (data: any) => {
			console.log('Game action result:', data);
			// Data includes: gameId, actionType, playerId, data

			// If payload includes lives or score, update stores
			if (data?.data) {
				if (typeof data.data.newLives === 'number') {
					gameActions.updateLives(data.data.newLives);
				}
				if (typeof data.data.newScore === 'number') {
					gameActions.updateScore(data.data.newScore);
				}
			}

			// Handle different callbacks based on actionType (legacy callbacks)
			if (data.actionType === 'tile_tap') {
				console.log('Tile tap result:', data.data);
				callbacks.onScoreUpdate(data.data.newScore);
			}
			if (data.actionType === 'cell_mark') {
				console.log('Cell mark result:', data.data);
				callbacks.onScoreUpdate(data.data.newScore);
			}
		});

		this.socket.on('score', (data: any) => {
			console.log('Score update from server:', data);

			gameActions.updateScore(data.score);
		});

		this.socket.on('timer_tick', (data: any) => {
			console.log('Timer update from server:', data);

			gameActions.updateTime(data.duration);
		});

		// Per-user opponent info updates
		this.socket.on('opponent_update', (data: any) => {
			console.log('Opponent update from server:', data);
			if (data && data.opponent) {
				const { userId, name, score, lives, status } = data.opponent;
				opponentActions.setOpponent({ userId, name, score, lives, status });
			} else {
				opponentActions.setOpponent(null);
			}
		});

		this.socket.on('error', (data: any) => {
			console.error('Game error:', data);
			// Data includes: gameId, message
		});

		this.socket.on('game_fatal_error', (data: any) => {
			console.error('Fatal game error:', data);
			// Data includes: message, errorType, roomId, timestamp
			// This will be handled by individual game scenes
		});

		// Room management event listeners
		this.setupRoomEventListeners();
	}

	private setupRoomEventListeners() {
		if (!this.socket) return;

		// Room joined successfully
		this.socket.on('room_joined', (data: any) => {
			console.log('[SocketClient] Room joined:', data);
			const { roomId, userId, gameId, players, hostUserId, roomCode } = data;

			roomActions.joinedRoom({
				roomId,
				roomCode: roomCode || roomId, // Fallback to roomId if no code provided
				players: players || [],
				hostUserId: hostUserId || userId, // First player becomes host
				currentUserId: userId,
				gameId,
				maxPlayers: data.maxPlayers || 8
			});
		});

		// Player joined the room (informational only - room_players_update is authoritative)
		this.socket.on('player_joined', (data: any) => {
			console.log('[SocketClient] Player joined (informational):', data);
			// Don't add player here - wait for room_players_update which is authoritative
			// This event is mainly for logging/notifications
		});

		// Player left the room
		this.socket.on('player_left', (data: any) => {
			console.log('[SocketClient] Player left:', data);
			const { userId } = data;
			roomActions.removePlayer(userId);
		});

		// Player disconnected
		this.socket.on('player_disconnected', (data: any) => {
			console.log('[SocketClient] Player disconnected:', data);
			const { userId } = data;
			roomActions.updatePlayerConnection(userId, false);
		});

		// Host changed
		this.socket.on('host_changed', (data: any) => {
			console.log('[SocketClient] Host changed:', data);
			const { hostUserId } = data;
			const currentState = get(roomState);
			if (currentState.currentUserId) {
				roomActions.updateHost(hostUserId, currentState.currentUserId);
			}
		});

		// Player ready status update
		this.socket.on('player_ready_update', (data: any) => {
			console.log('[SocketClient] Player ready update:', data);
			const { playerId, ready } = data;
			roomActions.updatePlayerReady(playerId, ready);
		});

		// Room players list update
		this.socket.on('room_players_update', (data: any) => {
			console.log('[SocketClient] Room players update:', data);
			const { players, hostUserId } = data;

			if (players) {
				// Convert server player data to client format
				const clientPlayers: Player[] = players.map((player: any) => ({
					userId: player.userId,
					name: player.name || player.displayName || `Player ${player.userId.slice(-4)}`,
					displayName: player.displayName || player.name,
					isHost: player.isHost,
					isReady: player.isReady || false,
					isConnected: player.isConnected !== false,
					joinedAt: new Date(player.joinedAt)
				}));

				roomActions.updatePlayers(clientPlayers);
			}

			if (hostUserId) {
				const currentState = get(roomState);
				if (currentState.currentUserId) {
					roomActions.updateHost(hostUserId, currentState.currentUserId);
				}
			}
		});

		// Room list update (for room browser if implemented)
		this.socket.on('room_list_update', (data: any) => {
			console.log('[SocketClient] Room list update:', data);
			// Could be used for a room browser feature
		});

		// Game start initiated by host
		this.socket.on('game_start', (data: any) => {
			console.log('[SocketClient] Game start initiated:', data);
			roomActions.gameStarted();
			// The existing game start logic will handle the actual game initialization
		});

		// Room errors
		this.socket.on('room_error', (data: any) => {
			console.error('[SocketClient] Room error:', data);
			const { message } = data;
			roomActions.setError(message || 'Room operation failed');
		});

		// Room left successfully
		this.socket.on('room_left', (data: any) => {
			console.log('[SocketClient] Room left:', data);
			roomActions.leftRoom();
		});
	}

	// Legacy room methods (keeping for backward compatibility)
	joinRoom(roomId: string) {
		if (this.socket) {
			this.socket.emit('joinRoom', { roomId });
		}
	}

	createRoom(gameId: string) {
		if (this.socket) {
			this.socket.emit('createRoom', { gameId });
		}
	}

	// New room management methods
	joinRoomByCode(roomCode: string, gameId: string) {
		if (!this.socket) {
			roomActions.setError('Not connected to server');
			return;
		}

		console.log('[SocketClient] Joining room by code:', roomCode);
		roomActions.startJoining(roomCode);

		this.socket.emit('join_room', {
			roomId: roomCode, // Server expects roomId, but we're using it as room code
			gameId
		});
	}

	leaveRoom() {
		if (!this.socket) {
			roomActions.setError('Not connected to server');
			return;
		}

		const currentRoomState = get(roomState);
		if (!currentRoomState.roomId) {
			roomActions.setError('Not in a room');
			return;
		}

		console.log('[SocketClient] Leaving room:', currentRoomState.roomId);
		roomActions.startLeaving();

		this.socket.emit('leave_room', {
			roomId: currentRoomState.roomId
		});
	}

	startGameAsHost() {
		if (!this.socket) {
			roomActions.setError('Not connected to server');
			return;
		}

		const currentRoomState = get(roomState);
		if (!currentRoomState.isHost) {
			roomActions.setError('Only the host can start the game');
			return;
		}

		if (!currentRoomState.roomId || !currentRoomState.gameId) {
			roomActions.setError('Room or game information missing');
			return;
		}

		console.log('[SocketClient] Starting game as host');
		roomActions.startGame();

		this.socket.emit('start_game', {
			roomId: currentRoomState.roomId,
			gameType: currentRoomState.gameId
		});
	}

	setPlayerReady(ready: boolean) {
		if (!this.socket) {
			roomActions.setError('Not connected to server');
			return;
		}

		const currentRoomState = get(roomState);
		if (!currentRoomState.roomId || !currentRoomState.currentUserId) {
			roomActions.setError('Not in a room');
			return;
		}

		console.log('[SocketClient] Setting player ready status:', ready);

		this.socket.emit('player_ready', {
			roomId: currentRoomState.roomId,
			playerId: currentRoomState.currentUserId,
			ready
		});

		// Optimistically update local state
		roomActions.updatePlayerReady(currentRoomState.currentUserId, ready);
	}

	// Utility method to get current room info
	getCurrentRoomInfo() {
		const currentRoomState = get(roomState);
		return {
			roomId: currentRoomState.roomId,
			roomCode: currentRoomState.roomCode,
			isHost: currentRoomState.isHost,
			playerCount: currentRoomState.players.length,
			isInRoom: currentRoomState.roomId !== null
		};
	}

	// Utility method to check if user can start game
	canStartGame() {
		const currentRoomState = get(roomState);
		return currentRoomState.isHost &&
			   currentRoomState.players.length > 1 &&
			   currentRoomState.players.filter(p => p.isConnected).every(p => p.isReady);
	}

	sendScore(score: number) {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';
			const submitScoreId = currentState.submitScoreId;

			console.log('Submitting score with state data:', {
				score,
				gameId,
				roomId,
				submitScoreId: submitScoreId ? 'present' : 'missing'
			});

			this.socket.emit('submit_score', {
				score,
				gameId,
				roomId,
				submitScoreId,
				playerId: 'player-1', // TODO: Get actual player ID
			});
		}
	}

	sendGameEvent(eventType: string, data: any) {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';

			this.socket.emit('game_action', {
				action: eventType,
				gameData: data,
				gameId,
				roomId,
				playerId: 'player-1' // TODO: Get actual player ID
			});
		}
	}

	// Generic game methods
	initGame() {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			console.log('Initializing game with state:', currentState);

			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';
			const submitScoreId = currentState.submitScoreId;

			console.log('Initializing game with state data:', {
				gameId,
				roomId,
				submitScoreId: submitScoreId ? 'present' : 'missing'
			});

			this.socket.emit('init', {
				gameId,
				roomId,
				submitScoreId
			});
		}
	}

	startGame() {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			console.log('Starting game with state:', currentState);

			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';

			console.log('Starting game with state data:', {
				gameId,
				roomId
			});

			this.socket.emit('start', {
				gameId,
				roomId
			});
		}
	}

	endGame(reason: string = 'manual') {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';
			const submitScoreId = currentState.submitScoreId;

			console.log('Ending game with state data:', {
				gameId,
				roomId,
				submitScoreId: submitScoreId ? 'present' : 'missing',
				reason
			});

			this.socket.emit('end', {
				gameId,
				roomId,
				submitScoreId,
				reason
			});
		}
	}

	sendGameAction(actionType: string, actionData: any) {
		if (this.socket) {
			const currentState = this.getCurrentGameState();
			const gameId = currentState.gameId || 'default-game';
			const roomId = currentState.roomId || 'default-room';

			this.socket.emit('action', {
				gameId,
				roomId,
				action: {
					type: actionType,
					data: actionData
				}
			});
		}
	}

	// Convenience methods for specific actions
	sendTileTap(tileId: string, reactionTime?: number) {
		this.sendGameAction('tile_tap', {
			tileId,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	sendCardSelect(cardId: string, reactionTime?: number) {
		this.sendGameAction('card_select', {
			cardId,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	sendNumberSelect(numberValue: number, reactionTime?: number) {
		this.sendGameAction('number_select', {
			numberValue,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	sendPathMove(newCell: { row: number; col: number }, moveType: 'start' | 'continue' | 'end') {
		this.sendGameAction('path_move', {
			newCell,
			moveType,
			timestamp: Date.now()
		});
	}

	disconnect() {
		if (this.socket) {
			this.socket.disconnect();
			this.socket = null;
		}
	}

	isConnected(): boolean {
		return this.socket?.connected ?? false;
	}

	// Add custom event listener
	addCustomEventListener(event: string, callback: (data: any) => void) {
		if (this.socket) {
			this.socket.on(event, callback);
		}
	}

	// Remove custom event listener
	removeCustomEventListener(event: string, callback: (data: any) => void) {
		if (this.socket) {
			this.socket.off(event, callback);
		}
	}
}

// Singleton instance
export const socketClient = new SocketClient();
