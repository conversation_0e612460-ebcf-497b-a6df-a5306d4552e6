import { get, writable } from 'svelte/store';


export enum GameStatus {
	Waiting = 'waiting',
	Loading = 'loading',
	Starting = 'starting',
	Countdown = 'countdown',
	Active = 'active',
	Paused = 'paused',
	Ended = 'ended',
}

export const GAME_TYPES = {
  FINGER_FRENZY: 'finger-frenzy',
  BINGO: 'bingo',
  MATCHING_MAYHEM: 'matching-mayhem',
  NUMBER_SEQUENCE: 'numbers',
  MUMS_NUMBERS: 'mums-numbers'
} as const;

export const DEFAULT_GAME_DURATION = {
  [GAME_TYPES.FINGER_FRENZY]: 30, // 30 seconds
  [GAME_TYPES.BINGO]: 60, // 60 seconds
  [GAME_TYPES.MATCHING_MAYHEM]: 20, // 20 seconds
  [GAME_TYPES.NUMBER_SEQUENCE]: 30, // 30 seconds
  [GAME_TYPES.MUMS_NUMBERS]: 60 // 60 seconds
} as const;

export interface GameState {
	score: number;
	time: number;
	totalTime: number;
	maxLives: number;
	lives: number;
	status: GameStatus;
	// isLoading: boolean;
	loadingProgress: number;
	// isCountdown: boolean;
	// isPlaying: boolean;
	// isPaused: boolean;
	// gameOver: boolean;
	roomId: string | null;
	authToken: string | null;
	submitScoreId: string | null;
	gameId: string | null;
}

const initialState: GameState = {
	score: 0,
	time: 30,
	totalTime: 30,
	maxLives: 3,
	lives: 3,
	status: GameStatus.Waiting,
	// isLoading: true,
	loadingProgress: 0,
	// isCountdown: false,
	// isPlaying: false,
	// isPaused: false,
	// gameOver: false,
	roomId: null,
	authToken: null,
	submitScoreId: null,
	gameId: null
};

export const gameState = writable<GameState>(initialState);

// Helper functions for updating game state
export const gameActions = {
	updateLoadingProgress: (progress: number) => {
		console.log('[GameState] Updating loading progress:', progress);
		gameState.update(state => ({ ...state, loadingProgress: progress }));
		console.log('[GameState] New state:', get(gameState));
	},

	updateScore: (score: number) => {
		console.log('[GameState] Updating score:', score);
		gameState.update(state => ({ ...state, score }));
		console.log('[GameState] New state:', get(gameState));
	},

	updateTime: (time: number) => {
		console.log('[GameState] Updating time:', time);
		gameState.update(state => ({ ...state, time }));
		console.log('[GameState] New state:', get(gameState));
	},

	updateLives: (lives: number) => {
		console.log('[GameState] Updating lives:', lives);
		gameState.update(state => ({ ...state, lives }));
		console.log('[GameState] New state:', get(gameState));
	},

	preloadComplete: () => {
		console.log('[GameState] Preload complete');
		gameState.update(state => ({ 
			...state, 
			status: GameStatus.Waiting,
			// isLoading: false 
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	initGame: () => {
		console.log('[GameState] Initializing game');
		gameState.update(state => ({
			...state,
			status: GameStatus.Countdown
			// isCountdown: true,
			// isPlaying: false,
			// isPaused: false,
			// gameOver: false  
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	startGame: () => {
		console.log('[GameState] Starting game');
		gameState.update(state => ({
			...state,
			status: GameStatus.Active,
			// isPlaying: true,
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	pauseGame: () => {
		console.log('[GameState] Pausing game');
		gameState.update(state => ({ 
			...state, 
			status: GameStatus.Paused,
			// isPaused: true 
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	resumeGame: () => {
		console.log('[GameState] Resuming game');
		gameState.update(state => ({ 
			...state, 
			status: GameStatus.Active,
			// isPaused: false 
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	endGame: () => {
		console.log('[GameState] Ending game');
		gameState.update(state => ({
			...state,
			status: GameStatus.Ended,
			// isPlaying: false,
			// gameOver: true
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	resetGame: () => {
		console.log('[GameState] Resetting game');
		gameState.set(initialState);
		console.log('[GameState] New state:', get(gameState));
	},

	setRoomData: (roomId: string, authToken: string, submitScoreId: string) => {
		console.log('[GameState] Setting room data:', { roomId, authToken, submitScoreId });
		gameState.update(state => ({
			...state,
			roomId,
			authToken,
			submitScoreId
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	setAuthToken: (authToken: string) => {
		console.log('[GameState] Setting auth token');
		gameState.update(state => ({
			...state,
			authToken
		}));
		console.log('[GameState] New state:', get(gameState));
	},

	setGameId: (gameId: string) => {
		console.log('[GameState] Setting game ID:', gameId);
		// gameState.update(state => ({ ...state, gameId }));

		const duration = DEFAULT_GAME_DURATION[gameId as keyof typeof DEFAULT_GAME_DURATION] || 30;
		gameState.update(state => ({ 
			...state, 
			gameId,
			time: duration,
			totalTime: duration
		}));

		console.log('[GameState] New state:', get(gameState));
	}
};
