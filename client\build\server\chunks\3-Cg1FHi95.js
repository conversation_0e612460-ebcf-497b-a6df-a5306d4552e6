const index = 3;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CyCjrDJm.js')).default;
const universal = {
  "ssr": false
};
const universal_id = "src/routes/game/[id]/+page.ts";
const imports = ["_app/immutable/nodes/3.CQxkl77G.js","_app/immutable/chunks/DHzIY8Hm.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/4UAai7vz.js","_app/immutable/chunks/DJNDnN69.js","_app/immutable/chunks/DMnCbMI3.js","_app/immutable/chunks/mXOxeudE.js","_app/immutable/chunks/D6Z45t_z.js","_app/immutable/chunks/BwME0dYm.js","_app/immutable/chunks/C1M19Mmo.js"];
const stylesheets = ["_app/immutable/assets/MumsNumbers.yT0SHS_s.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, universal, universal_id };
//# sourceMappingURL=3-Cg1FHi95.js.map
