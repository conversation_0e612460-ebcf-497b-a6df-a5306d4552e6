/**
 * Debug utilities for the room system
 * These functions help debug room state and player list issues
 */

import { get } from 'svelte/store';
import { roomState } from '../stores/roomState';

export function debugRoomState() {
  const state = get(roomState);
  console.group('🏠 Room State Debug');
  console.log('Status:', state.status);
  console.log('Room ID:', state.roomId);
  console.log('Room Code:', state.roomCode);
  console.log('Current User ID:', state.currentUserId);
  console.log('Is Host:', state.isHost);
  console.log('Host User ID:', state.hostUserId);
  console.log('Player Count:', state.players.length);
  console.log('Max Players:', state.maxPlayers);
  console.log('Is Connected:', state.isConnected);
  console.log('Error:', state.error);
  console.log('Is Loading:', state.isLoading);
  console.log('Loading Message:', state.loadingMessage);
  
  console.group('👥 Players');
  state.players.forEach((player, index) => {
    console.log(`Player ${index + 1}:`, {
      userId: player.userId,
      name: player.name,
      displayName: player.displayName,
      isHost: player.isHost,
      isReady: player.isReady,
      isConnected: player.isConnected,
      joinedAt: player.joinedAt
    });
  });
  console.groupEnd();
  
  console.groupEnd();
  
  return state;
}

export function debugPlayerList() {
  const state = get(roomState);
  console.group('👥 Player List Debug');
  console.log(`Total Players: ${state.players.length}/${state.maxPlayers}`);
  
  if (state.players.length === 0) {
    console.log('❌ No players in room');
  } else {
    state.players.forEach((player, index) => {
      const badges = [];
      if (player.isHost) badges.push('HOST');
      if (player.userId === state.currentUserId) badges.push('YOU');
      if (player.isReady) badges.push('READY');
      if (!player.isConnected) badges.push('DISCONNECTED');
      
      console.log(`${index + 1}. ${player.name || player.displayName || `Player ${player.userId.slice(-4)}`} ${badges.length > 0 ? `[${badges.join(', ')}]` : ''}`);
    });
  }
  
  console.groupEnd();
}

export function validateRoomState() {
  const state = get(roomState);
  const issues = [];
  
  // Check for duplicate players
  const userIds = state.players.map(p => p.userId);
  const uniqueUserIds = new Set(userIds);
  if (userIds.length !== uniqueUserIds.size) {
    issues.push('❌ Duplicate players detected');
  }
  
  // Check host consistency
  const hostPlayers = state.players.filter(p => p.isHost);
  if (hostPlayers.length === 0 && state.players.length > 0) {
    issues.push('❌ No host designated but players exist');
  } else if (hostPlayers.length > 1) {
    issues.push('❌ Multiple hosts detected');
  } else if (hostPlayers.length === 1 && hostPlayers[0].userId !== state.hostUserId) {
    issues.push('❌ Host player mismatch with hostUserId');
  }
  
  // Check current user
  if (state.currentUserId) {
    const currentUserInList = state.players.find(p => p.userId === state.currentUserId);
    if (!currentUserInList) {
      issues.push('❌ Current user not found in player list');
    } else if (currentUserInList.isHost !== state.isHost) {
      issues.push('❌ Current user host status mismatch');
    }
  }
  
  // Check player count
  if (state.players.length > state.maxPlayers) {
    issues.push('❌ Too many players in room');
  }
  
  console.group('🔍 Room State Validation');
  if (issues.length === 0) {
    console.log('✅ Room state is valid');
  } else {
    console.log('❌ Room state issues found:');
    issues.forEach(issue => console.log(issue));
  }
  console.groupEnd();
  
  return issues.length === 0;
}

// Auto-debug function that can be called from browser console
(window as any).debugRoom = () => {
  debugRoomState();
  debugPlayerList();
  validateRoomState();
};

// Export for use in components
export { debugRoomState as default };
