const index = 2;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CHyZwDjL.js')).default;
const imports = ["_app/immutable/nodes/2.DL9bb4bx.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/C1M19Mmo.js","_app/immutable/chunks/4UAai7vz.js","_app/immutable/chunks/DJNDnN69.js","_app/immutable/chunks/DMnCbMI3.js","_app/immutable/chunks/BwME0dYm.js","_app/immutable/chunks/D6Z45t_z.js"];
const stylesheets = ["_app/immutable/assets/MumsNumbers.yT0SHS_s.css","_app/immutable/assets/2.BN6ceEAQ.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=2-CLjDNosd.js.map
