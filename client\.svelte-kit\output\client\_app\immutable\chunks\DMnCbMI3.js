import{z as U,A as R,B,E as $,C,H as M,D as Y,F as q,G as T,I as k,J as A,K as F,U as K,L as Z,M as j,N as z,O as G,P as H,Q as W,h as O,R as J,S as D,T as Q,V,W as N,x as X,v as x,k as y,X as ee,Y as re,Z as ne,m as te,_ as se,a0 as ae,a1 as ie,a2 as ue,a3 as fe,a4 as le,a5 as ce,a6 as oe,a7 as de,a8 as _e,a9 as ve}from"./4UAai7vz.js";import{a as he,g as be}from"./DJNDnN69.js";function ye(e,r,u=!1){R&&B();var n=e,i=null,t=null,f=K,l=u?$:0,v=!1;const S=(h,s=!0)=>{v=!0,o(s,h)};var a=null;function d(){a!==null&&(a.lastChild.remove(),n.before(a),a=null);var h=f?i:t,s=f?t:i;h&&j(h),s&&z(s,()=>{f?t=null:i=null})}const o=(h,s)=>{if(f===(f=h))return;let b=!1;if(R){const g=C(n)===M;!!f===g&&(n=Y(),q(n),T(!1),b=!0)}var _=Z(),m=n;if(_&&(a=document.createDocumentFragment(),a.append(m=k())),f?i??=s&&A(()=>s(m)):t??=s&&A(()=>s(m)),_){var E=F,c=f?i:t,p=f?t:i;c&&E.skipped_effects.delete(c),p&&E.skipped_effects.add(p),E.add_callback(d)}else d();b&&T(!0)};U(()=>{v=!1,r(S),v||o(null,null)},l),R&&(n=G)}function w(e,r){return e===r||e?.[D]===r}function Re(e={},r,u,n){return H(()=>{var i,t;return W(()=>{i=t,t=[],O(()=>{e!==u(...t)&&(r(e,...t),i&&w(u(...i),e)&&r(null,...i))})}),()=>{J(()=>{t&&w(u(...t),e)&&r(null,...t)})}}),e}let P=!1,I=Symbol();function Ie(e,r,u){const n=u[r]??={store:null,source:X(void 0),unsubscribe:N};if(n.store!==e&&!(I in u))if(n.unsubscribe(),n.store=e??null,e==null)n.source.v=void 0,n.unsubscribe=N;else{var i=!0;n.unsubscribe=he(e,t=>{i?n.source.v=t:x(n.source,t)}),i=!1}return e&&I in u?be(e):y(n.source)}function Te(){const e={};function r(){Q(()=>{for(var u in e)e[u].unsubscribe();V(e,I,{enumerable:!1,value:!0})})}return[e,r]}function me(e){var r=P;try{return P=!1,[e(),P]}finally{P=r}}const pe={get(e,r){if(!e.exclude.includes(r))return e.props[r]},set(e,r){return!1},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function Ae(e,r,u){return new Proxy({props:e,exclude:r},pe)}function Ne(e,r,u,n){var i=!le||(u&ce)!==0,t=(u&fe)!==0,f=(u&de)!==0,l=n,v=!0,S=()=>(v&&(v=!1,l=f?O(n):n),l),a;if(t){var d=D in e||ve in e;a=ee(e,r)?.set??(d&&r in e?c=>e[r]=c:void 0)}var o,h=!1;t?[o,h]=me(()=>e[r]):o=e[r],o===void 0&&n!==void 0&&(o=S(),a&&(i&&re(),a(o)));var s;if(i?s=()=>{var c=e[r];return c===void 0?S():(v=!0,c)}:s=()=>{var c=e[r];return c!==void 0&&(l=void 0),c===void 0?l:c},i&&(u&ne)===0)return s;if(a){var b=e.$$legacy;return function(c,p){return arguments.length>0?((!i||!p||b||h)&&a(p?s():c),c):s()}}var _=!1,m=((u&oe)!==0?te:se)(()=>(_=!1,s()));t&&y(m);var E=ie;return function(c,p){if(arguments.length>0){const g=p?y(m):i&&t?ae(c):c;return x(m,g),_=!0,l!==void 0&&(l=g),c}return _e&&_||(E.f&ue)!==0?m.v:y(m)}}const Se="modulepreload",Ee=function(e,r){return new URL(e,r).href},L={},we=function(r,u,n){let i=Promise.resolve();if(u&&u.length>0){let S=function(a){return Promise.all(a.map(d=>Promise.resolve(d).then(o=>({status:"fulfilled",value:o}),o=>({status:"rejected",reason:o}))))};const f=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),v=l?.nonce||l?.getAttribute("nonce");i=S(u.map(a=>{if(a=Ee(a,n),a in L)return;L[a]=!0;const d=a.endsWith(".css"),o=d?'[rel="stylesheet"]':"";if(!!n)for(let b=f.length-1;b>=0;b--){const _=f[b];if(_.href===a&&(!d||_.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${o}`))return;const s=document.createElement("link");if(s.rel=d?"stylesheet":Se,d||(s.as="script"),s.crossOrigin="",s.href=a,v&&s.setAttribute("nonce",v),document.head.appendChild(s),d)return new Promise((b,_)=>{s.addEventListener("load",b),s.addEventListener("error",()=>_(new Error(`Unable to preload CSS for ${a}`)))})}))}function t(f){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=f,window.dispatchEvent(l),!l.defaultPrevented)throw f}return i.then(f=>{for(const l of f||[])l.status==="rejected"&&t(l.reason);return r().catch(t)})};export{we as _,Te as a,Re as b,ye as i,Ne as p,Ae as r,Ie as s};
