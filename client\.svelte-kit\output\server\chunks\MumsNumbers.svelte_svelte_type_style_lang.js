import { w as writable, i as get } from "./exports.js";
import { p as public_env } from "./shared-server.js";
import io from "socket.io-client";
import * as Phaser from "phaser";
const matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;
const stringToIcon = (value, validate, allowSimpleName, provider = "") => {
  const colonSeparated = value.split(":");
  if (value.slice(0, 1) === "@") {
    if (colonSeparated.length < 2 || colonSeparated.length > 3) {
      return null;
    }
    provider = colonSeparated.shift().slice(1);
  }
  if (colonSeparated.length > 3 || !colonSeparated.length) {
    return null;
  }
  if (colonSeparated.length > 1) {
    const name2 = colonSeparated.pop();
    const prefix = colonSeparated.pop();
    const result = {
      // Allow provider without '@': "provider:prefix:name"
      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,
      prefix,
      name: name2
    };
    return validate && !validateIconName(result) ? null : result;
  }
  const name = colonSeparated[0];
  const dashSeparated = name.split("-");
  if (dashSeparated.length > 1) {
    const result = {
      provider,
      prefix: dashSeparated.shift(),
      name: dashSeparated.join("-")
    };
    return validate && !validateIconName(result) ? null : result;
  }
  if (allowSimpleName && provider === "") {
    const result = {
      provider,
      prefix: "",
      name
    };
    return validate && !validateIconName(result, allowSimpleName) ? null : result;
  }
  return null;
};
const validateIconName = (icon, allowSimpleName) => {
  if (!icon) {
    return false;
  }
  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled
  // Check name: cannot be empty
  ((allowSimpleName && icon.prefix === "" || !!icon.prefix) && !!icon.name);
};
const defaultIconDimensions = Object.freeze(
  {
    left: 0,
    top: 0,
    width: 16,
    height: 16
  }
);
const defaultIconTransformations = Object.freeze({
  rotate: 0,
  vFlip: false,
  hFlip: false
});
const defaultIconProps = Object.freeze({
  ...defaultIconDimensions,
  ...defaultIconTransformations
});
const defaultExtendedIconProps = Object.freeze({
  ...defaultIconProps,
  body: "",
  hidden: false
});
function mergeIconTransformations(obj1, obj2) {
  const result = {};
  if (!obj1.hFlip !== !obj2.hFlip) {
    result.hFlip = true;
  }
  if (!obj1.vFlip !== !obj2.vFlip) {
    result.vFlip = true;
  }
  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;
  if (rotate) {
    result.rotate = rotate;
  }
  return result;
}
function mergeIconData(parent, child) {
  const result = mergeIconTransformations(parent, child);
  for (const key in defaultExtendedIconProps) {
    if (key in defaultIconTransformations) {
      if (key in parent && !(key in result)) {
        result[key] = defaultIconTransformations[key];
      }
    } else if (key in child) {
      result[key] = child[key];
    } else if (key in parent) {
      result[key] = parent[key];
    }
  }
  return result;
}
function getIconsTree(data, names) {
  const icons = data.icons;
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  const resolved = /* @__PURE__ */ Object.create(null);
  function resolve(name) {
    if (icons[name]) {
      return resolved[name] = [];
    }
    if (!(name in resolved)) {
      resolved[name] = null;
      const parent = aliases[name] && aliases[name].parent;
      const value = parent && resolve(parent);
      if (value) {
        resolved[name] = [parent].concat(value);
      }
    }
    return resolved[name];
  }
  Object.keys(icons).concat(Object.keys(aliases)).forEach(resolve);
  return resolved;
}
function internalGetIconData(data, name, tree) {
  const icons = data.icons;
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  let currentProps = {};
  function parse(name2) {
    currentProps = mergeIconData(
      icons[name2] || aliases[name2],
      currentProps
    );
  }
  parse(name);
  tree.forEach(parse);
  return mergeIconData(data, currentProps);
}
function parseIconSet(data, callback) {
  const names = [];
  if (typeof data !== "object" || typeof data.icons !== "object") {
    return names;
  }
  if (data.not_found instanceof Array) {
    data.not_found.forEach((name) => {
      callback(name, null);
      names.push(name);
    });
  }
  const tree = getIconsTree(data);
  for (const name in tree) {
    const item = tree[name];
    if (item) {
      callback(name, internalGetIconData(data, name, item));
      names.push(name);
    }
  }
  return names;
}
const optionalPropertyDefaults = {
  provider: "",
  aliases: {},
  not_found: {},
  ...defaultIconDimensions
};
function checkOptionalProps(item, defaults) {
  for (const prop in defaults) {
    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {
      return false;
    }
  }
  return true;
}
function quicklyValidateIconSet(obj) {
  if (typeof obj !== "object" || obj === null) {
    return null;
  }
  const data = obj;
  if (typeof data.prefix !== "string" || !obj.icons || typeof obj.icons !== "object") {
    return null;
  }
  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {
    return null;
  }
  const icons = data.icons;
  for (const name in icons) {
    const icon = icons[name];
    if (
      // Name cannot be empty
      !name || // Must have body
      typeof icon.body !== "string" || // Check other props
      !checkOptionalProps(
        icon,
        defaultExtendedIconProps
      )
    ) {
      return null;
    }
  }
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  for (const name in aliases) {
    const icon = aliases[name];
    const parent = icon.parent;
    if (
      // Name cannot be empty
      !name || // Parent must be set and point to existing icon
      typeof parent !== "string" || !icons[parent] && !aliases[parent] || // Check other props
      !checkOptionalProps(
        icon,
        defaultExtendedIconProps
      )
    ) {
      return null;
    }
  }
  return data;
}
const dataStorage = /* @__PURE__ */ Object.create(null);
function newStorage(provider, prefix) {
  return {
    provider,
    prefix,
    icons: /* @__PURE__ */ Object.create(null),
    missing: /* @__PURE__ */ new Set()
  };
}
function getStorage(provider, prefix) {
  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));
  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));
}
function addIconSet(storage2, data) {
  if (!quicklyValidateIconSet(data)) {
    return [];
  }
  return parseIconSet(data, (name, icon) => {
    if (icon) {
      storage2.icons[name] = icon;
    } else {
      storage2.missing.add(name);
    }
  });
}
function addIconToStorage(storage2, name, icon) {
  try {
    if (typeof icon.body === "string") {
      storage2.icons[name] = { ...icon };
      return true;
    }
  } catch (err) {
  }
  return false;
}
let simpleNames = false;
function allowSimpleNames(allow) {
  if (typeof allow === "boolean") {
    simpleNames = allow;
  }
  return simpleNames;
}
function getIconData(name) {
  const icon = typeof name === "string" ? stringToIcon(name, true, simpleNames) : name;
  if (icon) {
    const storage2 = getStorage(icon.provider, icon.prefix);
    const iconName = icon.name;
    return storage2.icons[iconName] || (storage2.missing.has(iconName) ? null : void 0);
  }
}
function addIcon(name, data) {
  const icon = stringToIcon(name, true, simpleNames);
  if (!icon) {
    return false;
  }
  const storage2 = getStorage(icon.provider, icon.prefix);
  if (data) {
    return addIconToStorage(storage2, icon.name, data);
  } else {
    storage2.missing.add(icon.name);
    return true;
  }
}
function addCollection(data, provider) {
  if (typeof data !== "object") {
    return false;
  }
  if (typeof provider !== "string") {
    provider = data.provider || "";
  }
  if (simpleNames && !provider && !data.prefix) {
    let added = false;
    if (quicklyValidateIconSet(data)) {
      data.prefix = "";
      parseIconSet(data, (name, icon) => {
        if (addIcon(name, icon)) {
          added = true;
        }
      });
    }
    return added;
  }
  const prefix = data.prefix;
  if (!validateIconName({
    prefix,
    name: "a"
  })) {
    return false;
  }
  const storage2 = getStorage(provider, prefix);
  return !!addIconSet(storage2, data);
}
const defaultIconSizeCustomisations = Object.freeze({
  width: null,
  height: null
});
const defaultIconCustomisations = Object.freeze({
  // Dimensions
  ...defaultIconSizeCustomisations,
  // Transformations
  ...defaultIconTransformations
});
const unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;
const unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;
function calculateSize(size, ratio, precision) {
  if (ratio === 1) {
    return size;
  }
  precision = precision || 100;
  if (typeof size === "number") {
    return Math.ceil(size * ratio * precision) / precision;
  }
  if (typeof size !== "string") {
    return size;
  }
  const oldParts = size.split(unitsSplit);
  if (oldParts === null || !oldParts.length) {
    return size;
  }
  const newParts = [];
  let code = oldParts.shift();
  let isNumber = unitsTest.test(code);
  while (true) {
    if (isNumber) {
      const num = parseFloat(code);
      if (isNaN(num)) {
        newParts.push(code);
      } else {
        newParts.push(Math.ceil(num * ratio * precision) / precision);
      }
    } else {
      newParts.push(code);
    }
    code = oldParts.shift();
    if (code === void 0) {
      return newParts.join("");
    }
    isNumber = !isNumber;
  }
}
function splitSVGDefs(content, tag = "defs") {
  let defs = "";
  const index = content.indexOf("<" + tag);
  while (index >= 0) {
    const start = content.indexOf(">", index);
    const end = content.indexOf("</" + tag);
    if (start === -1 || end === -1) {
      break;
    }
    const endEnd = content.indexOf(">", end);
    if (endEnd === -1) {
      break;
    }
    defs += content.slice(start + 1, end).trim();
    content = content.slice(0, index).trim() + content.slice(endEnd + 1);
  }
  return {
    defs,
    content
  };
}
function mergeDefsAndContent(defs, content) {
  return defs ? "<defs>" + defs + "</defs>" + content : content;
}
function wrapSVGContent(body, start, end) {
  const split = splitSVGDefs(body);
  return mergeDefsAndContent(split.defs, start + split.content + end);
}
const isUnsetKeyword = (value) => value === "unset" || value === "undefined" || value === "none";
function iconToSVG(icon, customisations) {
  const fullIcon = {
    ...defaultIconProps,
    ...icon
  };
  const fullCustomisations = {
    ...defaultIconCustomisations,
    ...customisations
  };
  const box = {
    left: fullIcon.left,
    top: fullIcon.top,
    width: fullIcon.width,
    height: fullIcon.height
  };
  let body = fullIcon.body;
  [fullIcon, fullCustomisations].forEach((props) => {
    const transformations = [];
    const hFlip = props.hFlip;
    const vFlip = props.vFlip;
    let rotation = props.rotate;
    if (hFlip) {
      if (vFlip) {
        rotation += 2;
      } else {
        transformations.push(
          "translate(" + (box.width + box.left).toString() + " " + (0 - box.top).toString() + ")"
        );
        transformations.push("scale(-1 1)");
        box.top = box.left = 0;
      }
    } else if (vFlip) {
      transformations.push(
        "translate(" + (0 - box.left).toString() + " " + (box.height + box.top).toString() + ")"
      );
      transformations.push("scale(1 -1)");
      box.top = box.left = 0;
    }
    let tempValue;
    if (rotation < 0) {
      rotation -= Math.floor(rotation / 4) * 4;
    }
    rotation = rotation % 4;
    switch (rotation) {
      case 1:
        tempValue = box.height / 2 + box.top;
        transformations.unshift(
          "rotate(90 " + tempValue.toString() + " " + tempValue.toString() + ")"
        );
        break;
      case 2:
        transformations.unshift(
          "rotate(180 " + (box.width / 2 + box.left).toString() + " " + (box.height / 2 + box.top).toString() + ")"
        );
        break;
      case 3:
        tempValue = box.width / 2 + box.left;
        transformations.unshift(
          "rotate(-90 " + tempValue.toString() + " " + tempValue.toString() + ")"
        );
        break;
    }
    if (rotation % 2 === 1) {
      if (box.left !== box.top) {
        tempValue = box.left;
        box.left = box.top;
        box.top = tempValue;
      }
      if (box.width !== box.height) {
        tempValue = box.width;
        box.width = box.height;
        box.height = tempValue;
      }
    }
    if (transformations.length) {
      body = wrapSVGContent(
        body,
        '<g transform="' + transformations.join(" ") + '">',
        "</g>"
      );
    }
  });
  const customisationsWidth = fullCustomisations.width;
  const customisationsHeight = fullCustomisations.height;
  const boxWidth = box.width;
  const boxHeight = box.height;
  let width;
  let height;
  if (customisationsWidth === null) {
    height = customisationsHeight === null ? "1em" : customisationsHeight === "auto" ? boxHeight : customisationsHeight;
    width = calculateSize(height, boxWidth / boxHeight);
  } else {
    width = customisationsWidth === "auto" ? boxWidth : customisationsWidth;
    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === "auto" ? boxHeight : customisationsHeight;
  }
  const attributes = {};
  const setAttr = (prop, value) => {
    if (!isUnsetKeyword(value)) {
      attributes[prop] = value.toString();
    }
  };
  setAttr("width", width);
  setAttr("height", height);
  const viewBox = [box.left, box.top, boxWidth, boxHeight];
  attributes.viewBox = viewBox.join(" ");
  return {
    attributes,
    viewBox,
    body
  };
}
const regex = /\sid="(\S+)"/g;
const randomPrefix = "IconifyId" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);
let counter = 0;
function replaceIDs(body, prefix = randomPrefix) {
  const ids = [];
  let match;
  while (match = regex.exec(body)) {
    ids.push(match[1]);
  }
  if (!ids.length) {
    return body;
  }
  const suffix = "suffix" + (Math.random() * 16777216 | Date.now()).toString(16);
  ids.forEach((id) => {
    const newID = typeof prefix === "function" ? prefix(id) : prefix + (counter++).toString();
    const escapedID = id.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    body = body.replace(
      // Allowed characters before id: [#;"]
      // Allowed characters after id: [)"], .[a-z]
      new RegExp('([#;"])(' + escapedID + ')([")]|\\.[a-z])', "g"),
      "$1" + newID + suffix + "$3"
    );
  });
  body = body.replace(new RegExp(suffix, "g"), "");
  return body;
}
const storage = /* @__PURE__ */ Object.create(null);
function setAPIModule(provider, item) {
  storage[provider] = item;
}
function getAPIModule(provider) {
  return storage[provider] || storage[""];
}
function createAPIConfig(source) {
  let resources;
  if (typeof source.resources === "string") {
    resources = [source.resources];
  } else {
    resources = source.resources;
    if (!(resources instanceof Array) || !resources.length) {
      return null;
    }
  }
  const result = {
    // API hosts
    resources,
    // Root path
    path: source.path || "/",
    // URL length limit
    maxURL: source.maxURL || 500,
    // Timeout before next host is used.
    rotate: source.rotate || 750,
    // Timeout before failing query.
    timeout: source.timeout || 5e3,
    // Randomise default API end point.
    random: source.random === true,
    // Start index
    index: source.index || 0,
    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).
    dataAfterTimeout: source.dataAfterTimeout !== false
  };
  return result;
}
const configStorage = /* @__PURE__ */ Object.create(null);
const fallBackAPISources = [
  "https://api.simplesvg.com",
  "https://api.unisvg.com"
];
const fallBackAPI = [];
while (fallBackAPISources.length > 0) {
  if (fallBackAPISources.length === 1) {
    fallBackAPI.push(fallBackAPISources.shift());
  } else {
    if (Math.random() > 0.5) {
      fallBackAPI.push(fallBackAPISources.shift());
    } else {
      fallBackAPI.push(fallBackAPISources.pop());
    }
  }
}
configStorage[""] = createAPIConfig({
  resources: ["https://api.iconify.design"].concat(fallBackAPI)
});
function addAPIProvider(provider, customConfig) {
  const config = createAPIConfig(customConfig);
  if (config === null) {
    return false;
  }
  configStorage[provider] = config;
  return true;
}
function getAPIConfig(provider) {
  return configStorage[provider];
}
const detectFetch = () => {
  let callback;
  try {
    callback = fetch;
    if (typeof callback === "function") {
      return callback;
    }
  } catch (err) {
  }
};
let fetchModule = detectFetch();
function calculateMaxLength(provider, prefix) {
  const config = getAPIConfig(provider);
  if (!config) {
    return 0;
  }
  let result;
  if (!config.maxURL) {
    result = 0;
  } else {
    let maxHostLength = 0;
    config.resources.forEach((item) => {
      const host = item;
      maxHostLength = Math.max(maxHostLength, host.length);
    });
    const url = prefix + ".json?icons=";
    result = config.maxURL - maxHostLength - config.path.length - url.length;
  }
  return result;
}
function shouldAbort(status) {
  return status === 404;
}
const prepare = (provider, prefix, icons) => {
  const results = [];
  const maxLength = calculateMaxLength(provider, prefix);
  const type = "icons";
  let item = {
    type,
    provider,
    prefix,
    icons: []
  };
  let length = 0;
  icons.forEach((name, index) => {
    length += name.length + 1;
    if (length >= maxLength && index > 0) {
      results.push(item);
      item = {
        type,
        provider,
        prefix,
        icons: []
      };
      length = name.length;
    }
    item.icons.push(name);
  });
  results.push(item);
  return results;
};
function getPath(provider) {
  if (typeof provider === "string") {
    const config = getAPIConfig(provider);
    if (config) {
      return config.path;
    }
  }
  return "/";
}
const send = (host, params, callback) => {
  if (!fetchModule) {
    callback("abort", 424);
    return;
  }
  let path = getPath(params.provider);
  switch (params.type) {
    case "icons": {
      const prefix = params.prefix;
      const icons = params.icons;
      const iconsList = icons.join(",");
      const urlParams = new URLSearchParams({
        icons: iconsList
      });
      path += prefix + ".json?" + urlParams.toString();
      break;
    }
    case "custom": {
      const uri = params.uri;
      path += uri.slice(0, 1) === "/" ? uri.slice(1) : uri;
      break;
    }
    default:
      callback("abort", 400);
      return;
  }
  let defaultError = 503;
  fetchModule(host + path).then((response) => {
    const status = response.status;
    if (status !== 200) {
      setTimeout(() => {
        callback(shouldAbort(status) ? "abort" : "next", status);
      });
      return;
    }
    defaultError = 501;
    return response.json();
  }).then((data) => {
    if (typeof data !== "object" || data === null) {
      setTimeout(() => {
        if (data === 404) {
          callback("abort", data);
        } else {
          callback("next", defaultError);
        }
      });
      return;
    }
    setTimeout(() => {
      callback("success", data);
    });
  }).catch(() => {
    callback("next", defaultError);
  });
};
const fetchAPIModule = {
  prepare,
  send
};
function sortIcons(icons) {
  const result = {
    loaded: [],
    missing: [],
    pending: []
  };
  const storage2 = /* @__PURE__ */ Object.create(null);
  icons.sort((a, b) => {
    if (a.provider !== b.provider) {
      return a.provider.localeCompare(b.provider);
    }
    if (a.prefix !== b.prefix) {
      return a.prefix.localeCompare(b.prefix);
    }
    return a.name.localeCompare(b.name);
  });
  let lastIcon = {
    provider: "",
    prefix: "",
    name: ""
  };
  icons.forEach((icon) => {
    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {
      return;
    }
    lastIcon = icon;
    const provider = icon.provider;
    const prefix = icon.prefix;
    const name = icon.name;
    const providerStorage = storage2[provider] || (storage2[provider] = /* @__PURE__ */ Object.create(null));
    const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));
    let list;
    if (name in localStorage.icons) {
      list = result.loaded;
    } else if (prefix === "" || localStorage.missing.has(name)) {
      list = result.missing;
    } else {
      list = result.pending;
    }
    const item = {
      provider,
      prefix,
      name
    };
    list.push(item);
  });
  return result;
}
function removeCallback(storages, id) {
  storages.forEach((storage2) => {
    const items = storage2.loaderCallbacks;
    if (items) {
      storage2.loaderCallbacks = items.filter((row) => row.id !== id);
    }
  });
}
function updateCallbacks(storage2) {
  if (!storage2.pendingCallbacksFlag) {
    storage2.pendingCallbacksFlag = true;
    setTimeout(() => {
      storage2.pendingCallbacksFlag = false;
      const items = storage2.loaderCallbacks ? storage2.loaderCallbacks.slice(0) : [];
      if (!items.length) {
        return;
      }
      let hasPending = false;
      const provider = storage2.provider;
      const prefix = storage2.prefix;
      items.forEach((item) => {
        const icons = item.icons;
        const oldLength = icons.pending.length;
        icons.pending = icons.pending.filter((icon) => {
          if (icon.prefix !== prefix) {
            return true;
          }
          const name = icon.name;
          if (storage2.icons[name]) {
            icons.loaded.push({
              provider,
              prefix,
              name
            });
          } else if (storage2.missing.has(name)) {
            icons.missing.push({
              provider,
              prefix,
              name
            });
          } else {
            hasPending = true;
            return true;
          }
          return false;
        });
        if (icons.pending.length !== oldLength) {
          if (!hasPending) {
            removeCallback([storage2], item.id);
          }
          item.callback(
            icons.loaded.slice(0),
            icons.missing.slice(0),
            icons.pending.slice(0),
            item.abort
          );
        }
      });
    });
  }
}
let idCounter = 0;
function storeCallback(callback, icons, pendingSources) {
  const id = idCounter++;
  const abort = removeCallback.bind(null, pendingSources, id);
  if (!icons.pending.length) {
    return abort;
  }
  const item = {
    id,
    icons,
    callback,
    abort
  };
  pendingSources.forEach((storage2) => {
    (storage2.loaderCallbacks || (storage2.loaderCallbacks = [])).push(item);
  });
  return abort;
}
function listToIcons(list, validate = true, simpleNames2 = false) {
  const result = [];
  list.forEach((item) => {
    const icon = typeof item === "string" ? stringToIcon(item, validate, simpleNames2) : item;
    if (icon) {
      result.push(icon);
    }
  });
  return result;
}
var defaultConfig = {
  resources: [],
  index: 0,
  timeout: 2e3,
  rotate: 750,
  random: false,
  dataAfterTimeout: false
};
function sendQuery(config, payload, query, done) {
  const resourcesCount = config.resources.length;
  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;
  let resources;
  if (config.random) {
    let list = config.resources.slice(0);
    resources = [];
    while (list.length > 1) {
      const nextIndex = Math.floor(Math.random() * list.length);
      resources.push(list[nextIndex]);
      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));
    }
    resources = resources.concat(list);
  } else {
    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));
  }
  const startTime = Date.now();
  let status = "pending";
  let queriesSent = 0;
  let lastError;
  let timer = null;
  let queue = [];
  let doneCallbacks = [];
  if (typeof done === "function") {
    doneCallbacks.push(done);
  }
  function resetTimer() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  }
  function abort() {
    if (status === "pending") {
      status = "aborted";
    }
    resetTimer();
    queue.forEach((item) => {
      if (item.status === "pending") {
        item.status = "aborted";
      }
    });
    queue = [];
  }
  function subscribe(callback, overwrite) {
    if (overwrite) {
      doneCallbacks = [];
    }
    if (typeof callback === "function") {
      doneCallbacks.push(callback);
    }
  }
  function getQueryStatus() {
    return {
      startTime,
      payload,
      status,
      queriesSent,
      queriesPending: queue.length,
      subscribe,
      abort
    };
  }
  function failQuery() {
    status = "failed";
    doneCallbacks.forEach((callback) => {
      callback(void 0, lastError);
    });
  }
  function clearQueue() {
    queue.forEach((item) => {
      if (item.status === "pending") {
        item.status = "aborted";
      }
    });
    queue = [];
  }
  function moduleResponse(item, response, data) {
    const isError = response !== "success";
    queue = queue.filter((queued) => queued !== item);
    switch (status) {
      case "pending":
        break;
      case "failed":
        if (isError || !config.dataAfterTimeout) {
          return;
        }
        break;
      default:
        return;
    }
    if (response === "abort") {
      lastError = data;
      failQuery();
      return;
    }
    if (isError) {
      lastError = data;
      if (!queue.length) {
        if (!resources.length) {
          failQuery();
        } else {
          execNext();
        }
      }
      return;
    }
    resetTimer();
    clearQueue();
    if (!config.random) {
      const index = config.resources.indexOf(item.resource);
      if (index !== -1 && index !== config.index) {
        config.index = index;
      }
    }
    status = "completed";
    doneCallbacks.forEach((callback) => {
      callback(data);
    });
  }
  function execNext() {
    if (status !== "pending") {
      return;
    }
    resetTimer();
    const resource = resources.shift();
    if (resource === void 0) {
      if (queue.length) {
        timer = setTimeout(() => {
          resetTimer();
          if (status === "pending") {
            clearQueue();
            failQuery();
          }
        }, config.timeout);
        return;
      }
      failQuery();
      return;
    }
    const item = {
      status: "pending",
      resource,
      callback: (status2, data) => {
        moduleResponse(item, status2, data);
      }
    };
    queue.push(item);
    queriesSent++;
    timer = setTimeout(execNext, config.rotate);
    query(resource, payload, item.callback);
  }
  setTimeout(execNext);
  return getQueryStatus;
}
function initRedundancy(cfg) {
  const config = {
    ...defaultConfig,
    ...cfg
  };
  let queries = [];
  function cleanup() {
    queries = queries.filter((item) => item().status === "pending");
  }
  function query(payload, queryCallback, doneCallback) {
    const query2 = sendQuery(
      config,
      payload,
      queryCallback,
      (data, error) => {
        cleanup();
        if (doneCallback) {
          doneCallback(data, error);
        }
      }
    );
    queries.push(query2);
    return query2;
  }
  function find(callback) {
    return queries.find((value) => {
      return callback(value);
    }) || null;
  }
  const instance = {
    query,
    find,
    setIndex: (index) => {
      config.index = index;
    },
    getIndex: () => config.index,
    cleanup
  };
  return instance;
}
function emptyCallback$1() {
}
const redundancyCache = /* @__PURE__ */ Object.create(null);
function getRedundancyCache(provider) {
  if (!redundancyCache[provider]) {
    const config = getAPIConfig(provider);
    if (!config) {
      return;
    }
    const redundancy = initRedundancy(config);
    const cachedReundancy = {
      config,
      redundancy
    };
    redundancyCache[provider] = cachedReundancy;
  }
  return redundancyCache[provider];
}
function sendAPIQuery(target, query, callback) {
  let redundancy;
  let send2;
  if (typeof target === "string") {
    const api = getAPIModule(target);
    if (!api) {
      callback(void 0, 424);
      return emptyCallback$1;
    }
    send2 = api.send;
    const cached = getRedundancyCache(target);
    if (cached) {
      redundancy = cached.redundancy;
    }
  } else {
    const config = createAPIConfig(target);
    if (config) {
      redundancy = initRedundancy(config);
      const moduleKey = target.resources ? target.resources[0] : "";
      const api = getAPIModule(moduleKey);
      if (api) {
        send2 = api.send;
      }
    }
  }
  if (!redundancy || !send2) {
    callback(void 0, 424);
    return emptyCallback$1;
  }
  return redundancy.query(query, send2, callback)().abort;
}
function emptyCallback() {
}
function loadedNewIcons(storage2) {
  if (!storage2.iconsLoaderFlag) {
    storage2.iconsLoaderFlag = true;
    setTimeout(() => {
      storage2.iconsLoaderFlag = false;
      updateCallbacks(storage2);
    });
  }
}
function checkIconNamesForAPI(icons) {
  const valid = [];
  const invalid = [];
  icons.forEach((name) => {
    (name.match(matchIconName) ? valid : invalid).push(name);
  });
  return {
    valid,
    invalid
  };
}
function parseLoaderResponse(storage2, icons, data) {
  function checkMissing() {
    const pending = storage2.pendingIcons;
    icons.forEach((name) => {
      if (pending) {
        pending.delete(name);
      }
      if (!storage2.icons[name]) {
        storage2.missing.add(name);
      }
    });
  }
  if (data && typeof data === "object") {
    try {
      const parsed = addIconSet(storage2, data);
      if (!parsed.length) {
        checkMissing();
        return;
      }
    } catch (err) {
      console.error(err);
    }
  }
  checkMissing();
  loadedNewIcons(storage2);
}
function parsePossiblyAsyncResponse(response, callback) {
  if (response instanceof Promise) {
    response.then((data) => {
      callback(data);
    }).catch(() => {
      callback(null);
    });
  } else {
    callback(response);
  }
}
function loadNewIcons(storage2, icons) {
  if (!storage2.iconsToLoad) {
    storage2.iconsToLoad = icons;
  } else {
    storage2.iconsToLoad = storage2.iconsToLoad.concat(icons).sort();
  }
  if (!storage2.iconsQueueFlag) {
    storage2.iconsQueueFlag = true;
    setTimeout(() => {
      storage2.iconsQueueFlag = false;
      const { provider, prefix } = storage2;
      const icons2 = storage2.iconsToLoad;
      delete storage2.iconsToLoad;
      if (!icons2 || !icons2.length) {
        return;
      }
      const customIconLoader = storage2.loadIcon;
      if (storage2.loadIcons && (icons2.length > 1 || !customIconLoader)) {
        parsePossiblyAsyncResponse(
          storage2.loadIcons(icons2, prefix, provider),
          (data) => {
            parseLoaderResponse(storage2, icons2, data);
          }
        );
        return;
      }
      if (customIconLoader) {
        icons2.forEach((name) => {
          const response = customIconLoader(name, prefix, provider);
          parsePossiblyAsyncResponse(response, (data) => {
            const iconSet = data ? {
              prefix,
              icons: {
                [name]: data
              }
            } : null;
            parseLoaderResponse(storage2, [name], iconSet);
          });
        });
        return;
      }
      const { valid, invalid } = checkIconNamesForAPI(icons2);
      if (invalid.length) {
        parseLoaderResponse(storage2, invalid, null);
      }
      if (!valid.length) {
        return;
      }
      const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;
      if (!api) {
        parseLoaderResponse(storage2, valid, null);
        return;
      }
      const params = api.prepare(provider, prefix, valid);
      params.forEach((item) => {
        sendAPIQuery(provider, item, (data) => {
          parseLoaderResponse(storage2, item.icons, data);
        });
      });
    });
  }
}
const loadIcons = (icons, callback) => {
  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());
  const sortedIcons = sortIcons(cleanedIcons);
  if (!sortedIcons.pending.length) {
    let callCallback = true;
    if (callback) {
      setTimeout(() => {
        if (callCallback) {
          callback(
            sortedIcons.loaded,
            sortedIcons.missing,
            sortedIcons.pending,
            emptyCallback
          );
        }
      });
    }
    return () => {
      callCallback = false;
    };
  }
  const newIcons = /* @__PURE__ */ Object.create(null);
  const sources = [];
  let lastProvider, lastPrefix;
  sortedIcons.pending.forEach((icon) => {
    const { provider, prefix } = icon;
    if (prefix === lastPrefix && provider === lastProvider) {
      return;
    }
    lastProvider = provider;
    lastPrefix = prefix;
    sources.push(getStorage(provider, prefix));
    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));
    if (!providerNewIcons[prefix]) {
      providerNewIcons[prefix] = [];
    }
  });
  sortedIcons.pending.forEach((icon) => {
    const { provider, prefix, name } = icon;
    const storage2 = getStorage(provider, prefix);
    const pendingQueue = storage2.pendingIcons || (storage2.pendingIcons = /* @__PURE__ */ new Set());
    if (!pendingQueue.has(name)) {
      pendingQueue.add(name);
      newIcons[provider][prefix].push(name);
    }
  });
  sources.forEach((storage2) => {
    const list = newIcons[storage2.provider][storage2.prefix];
    if (list.length) {
      loadNewIcons(storage2, list);
    }
  });
  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;
};
function mergeCustomisations(defaults, item) {
  const result = {
    ...defaults
  };
  for (const key in item) {
    const value = item[key];
    const valueType = typeof value;
    if (key in defaultIconSizeCustomisations) {
      if (value === null || value && (valueType === "string" || valueType === "number")) {
        result[key] = value;
      }
    } else if (valueType === typeof result[key]) {
      result[key] = key === "rotate" ? value % 4 : value;
    }
  }
  return result;
}
const separator = /[\s,]+/;
function flipFromString(custom, flip) {
  flip.split(separator).forEach((str) => {
    const value = str.trim();
    switch (value) {
      case "horizontal":
        custom.hFlip = true;
        break;
      case "vertical":
        custom.vFlip = true;
        break;
    }
  });
}
function rotateFromString(value, defaultValue = 0) {
  const units = value.replace(/^-?[0-9.]*/, "");
  function cleanup(value2) {
    while (value2 < 0) {
      value2 += 4;
    }
    return value2 % 4;
  }
  if (units === "") {
    const num = parseInt(value);
    return isNaN(num) ? 0 : cleanup(num);
  } else if (units !== value) {
    let split = 0;
    switch (units) {
      case "%":
        split = 25;
        break;
      case "deg":
        split = 90;
    }
    if (split) {
      let num = parseFloat(value.slice(0, value.length - units.length));
      if (isNaN(num)) {
        return 0;
      }
      num = num / split;
      return num % 1 === 0 ? cleanup(num) : 0;
    }
  }
  return defaultValue;
}
function iconToHTML(body, attributes) {
  let renderAttribsHTML = body.indexOf("xlink:") === -1 ? "" : ' xmlns:xlink="http://www.w3.org/1999/xlink"';
  for (const attr in attributes) {
    renderAttribsHTML += " " + attr + '="' + attributes[attr] + '"';
  }
  return '<svg xmlns="http://www.w3.org/2000/svg"' + renderAttribsHTML + ">" + body + "</svg>";
}
function encodeSVGforURL(svg) {
  return svg.replace(/"/g, "'").replace(/%/g, "%25").replace(/#/g, "%23").replace(/</g, "%3C").replace(/>/g, "%3E").replace(/\s+/g, " ");
}
function svgToData(svg) {
  return "data:image/svg+xml," + encodeSVGforURL(svg);
}
function svgToURL(svg) {
  return 'url("' + svgToData(svg) + '")';
}
const defaultExtendedIconCustomisations = {
  ...defaultIconCustomisations,
  inline: false
};
const svgDefaults = {
  "xmlns": "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink",
  "aria-hidden": true,
  "role": "img"
};
const commonProps = {
  display: "inline-block"
};
const monotoneProps = {
  "background-color": "currentColor"
};
const coloredProps = {
  "background-color": "transparent"
};
const propsToAdd = {
  image: "var(--svg)",
  repeat: "no-repeat",
  size: "100% 100%"
};
const propsToAddTo = {
  "-webkit-mask": monotoneProps,
  "mask": monotoneProps,
  "background": coloredProps
};
for (const prefix in propsToAddTo) {
  const list = propsToAddTo[prefix];
  for (const prop in propsToAdd) {
    list[prefix + "-" + prop] = propsToAdd[prop];
  }
}
function fixSize(value) {
  return value + (value.match(/^[-0-9.]+$/) ? "px" : "");
}
function render(icon, props) {
  const customisations = mergeCustomisations(defaultExtendedIconCustomisations, props);
  const mode = props.mode || "svg";
  const componentProps = mode === "svg" ? { ...svgDefaults } : {};
  if (icon.body.indexOf("xlink:") === -1) {
    delete componentProps["xmlns:xlink"];
  }
  let style = typeof props.style === "string" ? props.style : "";
  for (let key in props) {
    const value = props[key];
    if (value === void 0) {
      continue;
    }
    switch (key) {
      // Properties to ignore
      case "icon":
      case "style":
      case "onLoad":
      case "mode":
      case "ssr":
        break;
      // Boolean attributes
      case "inline":
      case "hFlip":
      case "vFlip":
        customisations[key] = value === true || value === "true" || value === 1;
        break;
      // Flip as string: 'horizontal,vertical'
      case "flip":
        if (typeof value === "string") {
          flipFromString(customisations, value);
        }
        break;
      // Color: copy to style, add extra ';' in case style is missing it
      case "color":
        style = style + (style.length > 0 && style.trim().slice(-1) !== ";" ? ";" : "") + "color: " + value + "; ";
        break;
      // Rotation as string
      case "rotate":
        if (typeof value === "string") {
          customisations[key] = rotateFromString(value);
        } else if (typeof value === "number") {
          customisations[key] = value;
        }
        break;
      // Remove aria-hidden
      case "ariaHidden":
      case "aria-hidden":
        if (value !== true && value !== "true") {
          delete componentProps["aria-hidden"];
        }
        break;
      default:
        if (key.slice(0, 3) === "on:") {
          break;
        }
        if (defaultExtendedIconCustomisations[key] === void 0) {
          componentProps[key] = value;
        }
    }
  }
  const item = iconToSVG(icon, customisations);
  const renderAttribs = item.attributes;
  if (customisations.inline) {
    style = "vertical-align: -0.125em; " + style;
  }
  if (mode === "svg") {
    Object.assign(componentProps, renderAttribs);
    if (style !== "") {
      componentProps.style = style;
    }
    let localCounter = 0;
    let id = props.id;
    if (typeof id === "string") {
      id = id.replace(/-/g, "_");
    }
    return {
      svg: true,
      attributes: componentProps,
      body: replaceIDs(item.body, id ? () => id + "ID" + localCounter++ : "iconifySvelte")
    };
  }
  const { body, width, height } = icon;
  const useMask = mode === "mask" || (mode === "bg" ? false : body.indexOf("currentColor") !== -1);
  const html = iconToHTML(body, {
    ...renderAttribs,
    width: width + "",
    height: height + ""
  });
  const url = svgToURL(html);
  const styles = {
    "--svg": url
  };
  const size = (prop) => {
    const value = renderAttribs[prop];
    if (value) {
      styles[prop] = fixSize(value);
    }
  };
  size("width");
  size("height");
  Object.assign(styles, commonProps, useMask ? monotoneProps : coloredProps);
  let customStyle = "";
  for (const key in styles) {
    customStyle += key + ": " + styles[key] + ";";
  }
  componentProps.style = customStyle + style;
  return {
    svg: false,
    attributes: componentProps
  };
}
allowSimpleNames(true);
setAPIModule("", fetchAPIModule);
if (typeof document !== "undefined" && typeof window !== "undefined") {
  const _window = window;
  if (_window.IconifyPreload !== void 0) {
    const preload = _window.IconifyPreload;
    const err = "Invalid IconifyPreload syntax.";
    if (typeof preload === "object" && preload !== null) {
      (preload instanceof Array ? preload : [preload]).forEach((item) => {
        try {
          if (
            // Check if item is an object and not null/array
            typeof item !== "object" || item === null || item instanceof Array || // Check for 'icons' and 'prefix'
            typeof item.icons !== "object" || typeof item.prefix !== "string" || // Add icon set
            !addCollection(item)
          ) {
            console.error(err);
          }
        } catch (e) {
          console.error(err);
        }
      });
    }
  }
  if (_window.IconifyProviders !== void 0) {
    const providers = _window.IconifyProviders;
    if (typeof providers === "object" && providers !== null) {
      for (let key in providers) {
        const err = "IconifyProviders[" + key + "] is invalid.";
        try {
          const value = providers[key];
          if (typeof value !== "object" || !value || value.resources === void 0) {
            continue;
          }
          if (!addAPIProvider(key, value)) {
            console.error(err);
          }
        } catch (e) {
          console.error(err);
        }
      }
    }
  }
}
function checkIconState(icon, state, mounted, callback, onload) {
  function abortLoading() {
    if (state.loading) {
      state.loading.abort();
      state.loading = null;
    }
  }
  if (typeof icon === "object" && icon !== null && typeof icon.body === "string") {
    state.name = "";
    abortLoading();
    return { data: { ...defaultIconProps, ...icon } };
  }
  let iconName;
  if (typeof icon !== "string" || (iconName = stringToIcon(icon, false, true)) === null) {
    abortLoading();
    return null;
  }
  const data = getIconData(iconName);
  if (!data) {
    if (mounted && (!state.loading || state.loading.name !== icon)) {
      abortLoading();
      state.name = "";
      state.loading = {
        name: icon,
        abort: loadIcons([iconName], callback)
      };
    }
    return null;
  }
  abortLoading();
  if (state.name !== icon) {
    state.name = icon;
    if (onload && !state.destroyed) {
      setTimeout(() => {
        onload(icon);
      });
    }
  }
  const classes = ["iconify"];
  if (iconName.prefix !== "") {
    classes.push("iconify--" + iconName.prefix);
  }
  if (iconName.provider !== "") {
    classes.push("iconify--" + iconName.provider);
  }
  return { data, classes };
}
function generateIcon(icon, props) {
  return icon ? render({
    ...defaultIconProps,
    ...icon
  }, props) : null;
}
var RoomStatus = /* @__PURE__ */ ((RoomStatus2) => {
  RoomStatus2["NotInRoom"] = "not_in_room";
  RoomStatus2["Joining"] = "joining";
  RoomStatus2["InRoom"] = "in_room";
  RoomStatus2["Starting"] = "starting";
  RoomStatus2["GameActive"] = "game_active";
  RoomStatus2["Leaving"] = "leaving";
  RoomStatus2["Error"] = "error";
  return RoomStatus2;
})(RoomStatus || {});
const initialState$2 = {
  status: "not_in_room",
  roomId: null,
  roomCode: null,
  players: [],
  currentUserId: null,
  isHost: false,
  hostUserId: null,
  maxPlayers: 8,
  // Default max players
  gameId: null,
  isConnected: false,
  error: null,
  isLoading: false,
  loadingMessage: null
};
const roomState = writable(initialState$2);
const roomActions = {
  // Reset room state to initial state
  reset: () => {
    console.log("[RoomState] Resetting room state");
    roomState.set(initialState$2);
  },
  // Set loading state
  setLoading: (isLoading, message) => {
    console.log("[RoomState] Setting loading state:", isLoading, message);
    roomState.update((state) => ({
      ...state,
      isLoading,
      loadingMessage: message || null
    }));
  },
  // Set error state
  setError: (error) => {
    console.log("[RoomState] Setting error:", error);
    roomState.update((state) => ({
      ...state,
      error,
      status: error ? "error" : state.status,
      isLoading: false,
      loadingMessage: null
    }));
  },
  // Clear error
  clearError: () => {
    console.log("[RoomState] Clearing error");
    roomState.update((state) => ({
      ...state,
      error: null,
      status: state.status === "error" ? "not_in_room" : state.status
    }));
  },
  // Set room connection status
  setConnected: (isConnected) => {
    console.log("[RoomState] Setting connection status:", isConnected);
    roomState.update((state) => ({
      ...state,
      isConnected
    }));
  },
  // Start joining a room
  startJoining: (roomCode) => {
    console.log("[RoomState] Starting to join room:", roomCode);
    roomState.update((state) => ({
      ...state,
      status: "joining",
      roomCode,
      error: null,
      isLoading: true,
      loadingMessage: "Joining room..."
    }));
  },
  // Successfully joined a room
  joinedRoom: (roomData) => {
    console.log("[RoomState] Successfully joined room:", roomData);
    const isHost = roomData.currentUserId === roomData.hostUserId;
    roomState.update((state) => ({
      ...state,
      status: "in_room",
      roomId: roomData.roomId,
      roomCode: roomData.roomCode,
      players: roomData.players,
      currentUserId: roomData.currentUserId,
      isHost,
      hostUserId: roomData.hostUserId,
      gameId: roomData.gameId,
      maxPlayers: roomData.maxPlayers || state.maxPlayers,
      isConnected: true,
      error: null,
      isLoading: false,
      loadingMessage: null
    }));
  },
  // Update player list
  updatePlayers: (players) => {
    console.log("[RoomState] Updating player list:", players);
    roomState.update((state) => ({
      ...state,
      players
    }));
  },
  // Add a player to the room
  addPlayer: (player) => {
    console.log("[RoomState] Adding player:", player);
    roomState.update((state) => ({
      ...state,
      players: [...state.players.filter((p) => p.userId !== player.userId), player]
    }));
  },
  // Remove a player from the room
  removePlayer: (userId) => {
    console.log("[RoomState] Removing player:", userId);
    roomState.update((state) => ({
      ...state,
      players: state.players.filter((p) => p.userId !== userId)
    }));
  },
  // Update host
  updateHost: (hostUserId, currentUserId) => {
    console.log("[RoomState] Updating host:", hostUserId);
    roomState.update((state) => ({
      ...state,
      hostUserId,
      isHost: currentUserId === hostUserId,
      players: state.players.map((player) => ({
        ...player,
        isHost: player.userId === hostUserId
      }))
    }));
  },
  // Update player ready status
  updatePlayerReady: (userId, isReady) => {
    console.log("[RoomState] Updating player ready status:", userId, isReady);
    roomState.update((state) => ({
      ...state,
      players: state.players.map(
        (player) => player.userId === userId ? { ...player, isReady } : player
      )
    }));
  },
  // Update player connection status
  updatePlayerConnection: (userId, isConnected) => {
    console.log("[RoomState] Updating player connection:", userId, isConnected);
    roomState.update((state) => ({
      ...state,
      players: state.players.map(
        (player) => player.userId === userId ? { ...player, isConnected } : player
      )
    }));
  },
  // Start leaving room
  startLeaving: () => {
    console.log("[RoomState] Starting to leave room");
    roomState.update((state) => ({
      ...state,
      status: "leaving",
      isLoading: true,
      loadingMessage: "Leaving room..."
    }));
  },
  // Successfully left room
  leftRoom: () => {
    console.log("[RoomState] Successfully left room");
    roomState.set({
      ...initialState$2,
      isConnected: true
      // Maintain socket connection
    });
  },
  // Start game (host action)
  startGame: () => {
    console.log("[RoomState] Starting game");
    roomState.update((state) => ({
      ...state,
      status: "starting",
      isLoading: true,
      loadingMessage: "Starting game..."
    }));
  },
  // Game started successfully
  gameStarted: () => {
    console.log("[RoomState] Game started");
    roomState.update((state) => ({
      ...state,
      status: "game_active",
      isLoading: false,
      loadingMessage: null
    }));
  },
  // Set current user ID
  setCurrentUserId: (userId) => {
    console.log("[RoomState] Setting current user ID:", userId);
    roomState.update((state) => ({
      ...state,
      currentUserId: userId
    }));
  },
  // Set game ID
  setGameId: (gameId) => {
    console.log("[RoomState] Setting game ID:", gameId);
    roomState.update((state) => ({
      ...state,
      gameId
    }));
  }
};
var GameStatus = /* @__PURE__ */ ((GameStatus2) => {
  GameStatus2["Waiting"] = "waiting";
  GameStatus2["Loading"] = "loading";
  GameStatus2["Starting"] = "starting";
  GameStatus2["Countdown"] = "countdown";
  GameStatus2["Active"] = "active";
  GameStatus2["Paused"] = "paused";
  GameStatus2["Ended"] = "ended";
  return GameStatus2;
})(GameStatus || {});
const GAME_TYPES = {
  FINGER_FRENZY: "finger-frenzy",
  BINGO: "bingo",
  MATCHING_MAYHEM: "matching-mayhem",
  NUMBER_SEQUENCE: "numbers",
  MUMS_NUMBERS: "mums-numbers"
};
const DEFAULT_GAME_DURATION = {
  [GAME_TYPES.FINGER_FRENZY]: 30,
  // 30 seconds
  [GAME_TYPES.BINGO]: 60,
  // 60 seconds
  [GAME_TYPES.MATCHING_MAYHEM]: 20,
  // 20 seconds
  [GAME_TYPES.NUMBER_SEQUENCE]: 30,
  // 30 seconds
  [GAME_TYPES.MUMS_NUMBERS]: 60
  // 60 seconds
};
const initialState$1 = {
  score: 0,
  time: 30,
  totalTime: 30,
  maxLives: 3,
  lives: 3,
  status: "waiting",
  // isLoading: true,
  loadingProgress: 0,
  // isCountdown: false,
  // isPlaying: false,
  // isPaused: false,
  // gameOver: false,
  roomId: null,
  authToken: null,
  submitScoreId: null,
  gameId: null
};
const gameState = writable(initialState$1);
const gameActions = {
  updateLoadingProgress: (progress) => {
    console.log("[GameState] Updating loading progress:", progress);
    gameState.update((state) => ({ ...state, loadingProgress: progress }));
    console.log("[GameState] New state:", get(gameState));
  },
  updateScore: (score) => {
    console.log("[GameState] Updating score:", score);
    gameState.update((state) => ({ ...state, score }));
    console.log("[GameState] New state:", get(gameState));
  },
  updateTime: (time) => {
    console.log("[GameState] Updating time:", time);
    gameState.update((state) => ({ ...state, time }));
    console.log("[GameState] New state:", get(gameState));
  },
  updateLives: (lives) => {
    console.log("[GameState] Updating lives:", lives);
    gameState.update((state) => ({ ...state, lives }));
    console.log("[GameState] New state:", get(gameState));
  },
  preloadComplete: () => {
    console.log("[GameState] Preload complete");
    gameState.update((state) => ({
      ...state,
      status: "waiting"
      /* Waiting */
      // isLoading: false 
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  initGame: () => {
    console.log("[GameState] Initializing game");
    gameState.update((state) => ({
      ...state,
      status: "countdown"
      /* Countdown */
      // isCountdown: true,
      // isPlaying: false,
      // isPaused: false,
      // gameOver: false  
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  startGame: () => {
    console.log("[GameState] Starting game");
    gameState.update((state) => ({
      ...state,
      status: "active"
      /* Active */
      // isPlaying: true,
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  pauseGame: () => {
    console.log("[GameState] Pausing game");
    gameState.update((state) => ({
      ...state,
      status: "paused"
      /* Paused */
      // isPaused: true 
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  resumeGame: () => {
    console.log("[GameState] Resuming game");
    gameState.update((state) => ({
      ...state,
      status: "active"
      /* Active */
      // isPaused: false 
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  endGame: () => {
    console.log("[GameState] Ending game");
    gameState.update((state) => ({
      ...state,
      status: "ended"
      /* Ended */
      // isPlaying: false,
      // gameOver: true
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  resetGame: () => {
    console.log("[GameState] Resetting game");
    gameState.set(initialState$1);
    console.log("[GameState] New state:", get(gameState));
  },
  setRoomData: (roomId, authToken, submitScoreId) => {
    console.log("[GameState] Setting room data:", { roomId, authToken, submitScoreId });
    gameState.update((state) => ({
      ...state,
      roomId,
      authToken,
      submitScoreId
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  setAuthToken: (authToken) => {
    console.log("[GameState] Setting auth token");
    gameState.update((state) => ({
      ...state,
      authToken
    }));
    console.log("[GameState] New state:", get(gameState));
  },
  setGameId: (gameId) => {
    console.log("[GameState] Setting game ID:", gameId);
    const duration = DEFAULT_GAME_DURATION[gameId] || 30;
    gameState.update((state) => ({
      ...state,
      gameId,
      time: duration,
      totalTime: duration
    }));
    console.log("[GameState] New state:", get(gameState));
  }
};
const initialState = {
  hasOpponent: false,
  waiting: true,
  opponent: null
};
const opponentState = writable(initialState);
const opponentActions = {
  reset: () => opponentState.set(initialState),
  setOpponent: (info) => {
    opponentState.set({
      hasOpponent: !!info,
      waiting: !info,
      opponent: info
    });
  }
};
class SocketClient {
  socket = null;
  serverUrl;
  constructor() {
    this.serverUrl = public_env.PUBLIC_SOCKET_SERVER_URL;
    console.log(this.serverUrl);
  }
  // Helper method to get current game state
  getCurrentGameState() {
    return get(gameState);
  }
  async connect(authToken, callbacks) {
    return new Promise((resolve, reject) => {
      console.log("[SocketClient] Connecting with JWT token for server-side authentication");
      this.socket = io(this.serverUrl, {
        auth: {
          token: authToken
        }
      });
      this.socket.on("connect", () => {
        console.log("[SocketClient] Connected to game server with authenticated session");
        this.setupEventListeners(callbacks);
        resolve();
      });
      this.socket.on("connect_error", (error) => {
        console.error("[SocketClient] Connection error:", error);
        if (error.message?.includes("Authentication")) {
          console.error("[SocketClient] Authentication failed - invalid or expired JWT token");
        }
        reject(error);
      });
    });
  }
  setupEventListeners(callbacks) {
    if (!this.socket) return;
    this.socket.on("gameUpdate", (data) => {
      console.log("Game update received:", data);
    });
    this.socket.on("roomJoined", (roomData) => {
      console.log("Joined room:", roomData);
    });
    this.socket.on("playerJoined", (playerData) => {
      console.log("Player joined:", playerData);
    });
    this.socket.on("playerLeft", (playerData) => {
      console.log("Player left:", playerData);
    });
    this.socket.on("gameStarted", () => {
      gameActions.startGame();
    });
    this.socket.on("gameEnded", (results) => {
      gameActions.endGame();
      console.log("Game ended:", results);
    });
    this.socket.on("initialized", (data) => {
      console.log("Game initialized:", data);
      gameActions.initGame();
    });
    this.socket.on("started", (data) => {
      console.log("Game started:", data);
      gameActions.startGame();
    });
    this.socket.on("ended", (data) => {
      console.log("Game ended:", data);
      callbacks.onGameComplete(data.finalScore);
    });
    this.socket.on("action_result", (data) => {
      console.log("Game action result:", data);
      if (data?.data) {
        if (typeof data.data.newLives === "number") {
          gameActions.updateLives(data.data.newLives);
        }
        if (typeof data.data.newScore === "number") {
          gameActions.updateScore(data.data.newScore);
        }
      }
      if (data.actionType === "tile_tap") {
        console.log("Tile tap result:", data.data);
        callbacks.onScoreUpdate(data.data.newScore);
      }
      if (data.actionType === "cell_mark") {
        console.log("Cell mark result:", data.data);
        callbacks.onScoreUpdate(data.data.newScore);
      }
    });
    this.socket.on("score", (data) => {
      console.log("Score update from server:", data);
      gameActions.updateScore(data.score);
    });
    this.socket.on("timer_tick", (data) => {
      console.log("Timer update from server:", data);
      gameActions.updateTime(data.duration);
    });
    this.socket.on("opponent_update", (data) => {
      console.log("Opponent update from server:", data);
      if (data && data.opponent) {
        const { userId, name, score, lives, status } = data.opponent;
        opponentActions.setOpponent({ userId, name, score, lives, status });
      } else {
        opponentActions.setOpponent(null);
      }
    });
    this.socket.on("error", (data) => {
      console.error("Game error:", data);
    });
    this.socket.on("game_fatal_error", (data) => {
      console.error("Fatal game error:", data);
    });
    this.setupRoomEventListeners();
  }
  setupRoomEventListeners() {
    if (!this.socket) return;
    this.socket.on("room_joined", (data) => {
      console.log("[SocketClient] Room joined:", data);
      const { roomId, userId, gameId, players, hostUserId, roomCode } = data;
      roomActions.joinedRoom({
        roomId,
        roomCode: roomCode || roomId,
        // Fallback to roomId if no code provided
        players: players || [],
        hostUserId: hostUserId || userId,
        // First player becomes host
        currentUserId: userId,
        gameId,
        maxPlayers: data.maxPlayers || 8
      });
    });
    this.socket.on("player_joined", (data) => {
      console.log("[SocketClient] Player joined:", data);
      const { userId, gameId, name, displayName } = data;
      const player = {
        userId,
        name: name || displayName || `Player ${userId.slice(-4)}`,
        displayName: displayName || name,
        isHost: false,
        // Will be updated by host_changed if needed
        isReady: false,
        isConnected: true,
        joinedAt: /* @__PURE__ */ new Date()
      };
      roomActions.addPlayer(player);
    });
    this.socket.on("player_left", (data) => {
      console.log("[SocketClient] Player left:", data);
      const { userId } = data;
      roomActions.removePlayer(userId);
    });
    this.socket.on("player_disconnected", (data) => {
      console.log("[SocketClient] Player disconnected:", data);
      const { userId } = data;
      roomActions.updatePlayerConnection(userId, false);
    });
    this.socket.on("host_changed", (data) => {
      console.log("[SocketClient] Host changed:", data);
      const { hostUserId } = data;
      const currentState = get(roomState);
      if (currentState.currentUserId) {
        roomActions.updateHost(hostUserId, currentState.currentUserId);
      }
    });
    this.socket.on("player_ready_update", (data) => {
      console.log("[SocketClient] Player ready update:", data);
      const { playerId, ready } = data;
      roomActions.updatePlayerReady(playerId, ready);
    });
    this.socket.on("room_players_update", (data) => {
      console.log("[SocketClient] Room players update:", data);
      const { players, hostUserId } = data;
      if (players) {
        const clientPlayers = players.map((player) => ({
          userId: player.userId,
          name: player.name || player.displayName || `Player ${player.userId.slice(-4)}`,
          displayName: player.displayName || player.name,
          isHost: player.isHost,
          isReady: player.isReady || false,
          isConnected: player.isConnected !== false,
          joinedAt: new Date(player.joinedAt)
        }));
        roomActions.updatePlayers(clientPlayers);
      }
      if (hostUserId) {
        const currentState = get(roomState);
        if (currentState.currentUserId) {
          roomActions.updateHost(hostUserId, currentState.currentUserId);
        }
      }
    });
    this.socket.on("room_list_update", (data) => {
      console.log("[SocketClient] Room list update:", data);
    });
    this.socket.on("game_start", (data) => {
      console.log("[SocketClient] Game start initiated:", data);
      roomActions.gameStarted();
    });
    this.socket.on("room_error", (data) => {
      console.error("[SocketClient] Room error:", data);
      const { message } = data;
      roomActions.setError(message || "Room operation failed");
    });
    this.socket.on("room_left", (data) => {
      console.log("[SocketClient] Room left:", data);
      roomActions.leftRoom();
    });
  }
  // Legacy room methods (keeping for backward compatibility)
  joinRoom(roomId) {
    if (this.socket) {
      this.socket.emit("joinRoom", { roomId });
    }
  }
  createRoom(gameId) {
    if (this.socket) {
      this.socket.emit("createRoom", { gameId });
    }
  }
  // New room management methods
  joinRoomByCode(roomCode, gameId) {
    if (!this.socket) {
      roomActions.setError("Not connected to server");
      return;
    }
    console.log("[SocketClient] Joining room by code:", roomCode);
    roomActions.startJoining(roomCode);
    this.socket.emit("join_room", {
      roomId: roomCode,
      // Server expects roomId, but we're using it as room code
      gameId
    });
  }
  leaveRoom() {
    if (!this.socket) {
      roomActions.setError("Not connected to server");
      return;
    }
    const currentRoomState = get(roomState);
    if (!currentRoomState.roomId) {
      roomActions.setError("Not in a room");
      return;
    }
    console.log("[SocketClient] Leaving room:", currentRoomState.roomId);
    roomActions.startLeaving();
    this.socket.emit("leave_room", {
      roomId: currentRoomState.roomId
    });
  }
  startGameAsHost() {
    if (!this.socket) {
      roomActions.setError("Not connected to server");
      return;
    }
    const currentRoomState = get(roomState);
    if (!currentRoomState.isHost) {
      roomActions.setError("Only the host can start the game");
      return;
    }
    if (!currentRoomState.roomId || !currentRoomState.gameId) {
      roomActions.setError("Room or game information missing");
      return;
    }
    console.log("[SocketClient] Starting game as host");
    roomActions.startGame();
    this.socket.emit("start_game", {
      roomId: currentRoomState.roomId,
      gameType: currentRoomState.gameId
    });
  }
  setPlayerReady(ready) {
    if (!this.socket) {
      roomActions.setError("Not connected to server");
      return;
    }
    const currentRoomState = get(roomState);
    if (!currentRoomState.roomId || !currentRoomState.currentUserId) {
      roomActions.setError("Not in a room");
      return;
    }
    console.log("[SocketClient] Setting player ready status:", ready);
    this.socket.emit("player_ready", {
      roomId: currentRoomState.roomId,
      playerId: currentRoomState.currentUserId,
      ready
    });
    roomActions.updatePlayerReady(currentRoomState.currentUserId, ready);
  }
  // Utility method to get current room info
  getCurrentRoomInfo() {
    const currentRoomState = get(roomState);
    return {
      roomId: currentRoomState.roomId,
      roomCode: currentRoomState.roomCode,
      isHost: currentRoomState.isHost,
      playerCount: currentRoomState.players.length,
      isInRoom: currentRoomState.roomId !== null
    };
  }
  // Utility method to check if user can start game
  canStartGame() {
    const currentRoomState = get(roomState);
    return currentRoomState.isHost && currentRoomState.players.length > 1 && currentRoomState.players.filter((p) => p.isConnected).every((p) => p.isReady);
  }
  sendScore(score) {
    if (this.socket) {
      const currentState = this.getCurrentGameState();
      const gameId = currentState.gameId || "default-game";
      const roomId = currentState.roomId || "default-room";
      const submitScoreId = currentState.submitScoreId;
      console.log("Submitting score with state data:", {
        score,
        gameId,
        roomId,
        submitScoreId: submitScoreId ? "present" : "missing"
      });
      this.socket.emit("submit_score", {
        score,
        gameId,
        roomId,
        submitScoreId,
        playerId: "player-1"
        // TODO: Get actual player ID
      });
    }
  }
  sendGameEvent(eventType, data) {
    if (this.socket) {
      const currentState = this.getCurrentGameState();
      const gameId = currentState.gameId || "default-game";
      const roomId = currentState.roomId || "default-room";
      this.socket.emit("game_action", {
        action: eventType,
        gameData: data,
        gameId,
        roomId,
        playerId: "player-1"
        // TODO: Get actual player ID
      });
    }
  }
  // Generic game methods
  initGame() {
    if (this.socket) {
      const currentState = this.getCurrentGameState();
      console.log("Initializing game with state:", currentState);
      const gameId = currentState.gameId || "default-game";
      const roomId = currentState.roomId || "default-room";
      const submitScoreId = currentState.submitScoreId;
      console.log("Initializing game with state data:", {
        gameId,
        roomId,
        submitScoreId: submitScoreId ? "present" : "missing"
      });
      this.socket.emit("init", {
        gameId,
        roomId,
        submitScoreId
      });
    }
  }
  startGame() {
    if (this.socket) {
      const currentState = this.getCurrentGameState();
      console.log("Starting game with state:", currentState);
      const gameId = currentState.gameId || "default-game";
      const roomId = currentState.roomId || "default-room";
      console.log("Starting game with state data:", {
        gameId,
        roomId
      });
      this.socket.emit("start", {
        gameId,
        roomId
      });
    }
  }
  endGame(reason = "manual") {
    if (this.socket) {
      const currentState = this.getCurrentGameState();
      const gameId = currentState.gameId || "default-game";
      const roomId = currentState.roomId || "default-room";
      const submitScoreId = currentState.submitScoreId;
      console.log("Ending game with state data:", {
        gameId,
        roomId,
        submitScoreId: submitScoreId ? "present" : "missing",
        reason
      });
      this.socket.emit("end", {
        gameId,
        roomId,
        submitScoreId,
        reason
      });
    }
  }
  sendGameAction(actionType, actionData) {
    if (this.socket) {
      const currentState = this.getCurrentGameState();
      const gameId = currentState.gameId || "default-game";
      const roomId = currentState.roomId || "default-room";
      this.socket.emit("action", {
        gameId,
        roomId,
        action: {
          type: actionType,
          data: actionData
        }
      });
    }
  }
  // Convenience methods for specific actions
  sendTileTap(tileId, reactionTime) {
    this.sendGameAction("tile_tap", {
      tileId,
      reactionTime: reactionTime || 0,
      clickTime: Date.now()
    });
  }
  sendCardSelect(cardId, reactionTime) {
    this.sendGameAction("card_select", {
      cardId,
      reactionTime: reactionTime || 0,
      clickTime: Date.now()
    });
  }
  sendNumberSelect(numberValue, reactionTime) {
    this.sendGameAction("number_select", {
      numberValue,
      reactionTime: reactionTime || 0,
      clickTime: Date.now()
    });
  }
  sendPathMove(newCell, moveType) {
    this.sendGameAction("path_move", {
      newCell,
      moveType,
      timestamp: Date.now()
    });
  }
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
  isConnected() {
    return this.socket?.connected ?? false;
  }
  // Add custom event listener
  addCustomEventListener(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }
  // Remove custom event listener
  removeCustomEventListener(event, callback) {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}
new SocketClient();
class PostMessageHandler {
  listeners = /* @__PURE__ */ new Map();
  constructor() {
    this.setupMessageListener();
  }
  setupMessageListener() {
    if (typeof window !== "undefined") {
      window.addEventListener("message", (event) => {
        const message = event.data;
        this.handleMessage(message);
      });
    }
  }
  handleMessage(message) {
    const listeners = this.listeners.get(message.type) || [];
    listeners.forEach((listener) => listener(message.data));
  }
  // Listen for specific message types
  on(messageType, callback) {
    if (!this.listeners.has(messageType)) {
      this.listeners.set(messageType, []);
    }
    this.listeners.get(messageType).push(callback);
  }
  // Remove listener
  off(messageType, callback) {
    const listeners = this.listeners.get(messageType) || [];
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
  // Send message to parent PWA
  sendToParent(type, data) {
    if (typeof window !== "undefined" && window.parent) {
      const message = { type, data };
      window.parent.postMessage(message, "*");
    }
  }
  // Common message handlers
  onGameInit(callback) {
    this.on("gameInit", callback);
  }
  onPause(callback) {
    this.on("pause", callback);
  }
  onResume(callback) {
    this.on("resume", callback);
  }
  // Common message senders
  sendScoreUpdate(score) {
    this.sendToParent("scoreUpdate", { score });
  }
  sendGameComplete(finalScore, submitScoreId) {
    this.sendToParent("gameComplete", { finalScore, submitScoreId });
  }
  sendGameReady() {
    this.sendToParent("gameReady");
  }
  sendError(error) {
    this.sendToParent("error", { error });
  }
}
new PostMessageHandler();
let GameConfig$1 = class GameConfig {
  // Game dimensions
  static GAME_WIDTH = 540;
  static GAME_HEIGHT = 960;
  static BACKGROUND_COLOR = "#0E0F1E";
  // Grid configuration
  static GRID_SIZE = 4;
  static INITIAL_ACTIVE_BLOCKS = 3;
  static BLOCK_CORNER_RADIUS = 10;
  // Timer settings
  static GAME_DURATION = 30;
  // in seconds
  static COOLDOWN_DURATION = 0.5;
  // in seconds
  static TMER_INCREASE = 0.2;
  // in seconds
  static TMER_PUNISHMENT = 1;
  // in seconds
  static COUNTDOWN_DURATION = 3;
  // in seconds
  static TRANSITION_DURATION = 300;
  // in milliseconds
  // Scoring configuration
  static SCORE_TIERS = {
    FAST: 5,
    // < 500ms
    MEDIUM_FAST: 4,
    // < 1000ms
    MEDIUM: 3,
    // < 1500ms
    MEDIUM_SLOW: 2,
    // < 2000ms
    SLOW: 1
    // >= 2000ms
  };
  static SCORE_TIER_THRESHOLDS = {
    FAST: 500,
    MEDIUM_FAST: 1e3,
    MEDIUM: 1500,
    MEDIUM_SLOW: 2e3
  };
  static WRONG_CLICK_PENALTY = 5;
  // Animation settings
  static BLOCK_ANIMATION_DURATION = 50;
  static WRONG_EFFECT_DURATION = 200;
  static SCORE_ANIMATION_DURATION = 400;
  static FLASH_DURATION = 100;
  // UI settings
  static TIMER_BAR_WIDTH_PERCENT = 0.8;
  // 80% of screen width
  static TIMER_BAR_HEIGHT = 35;
  static TIMER_BAR_Y_PERCENT = 0.07;
  // 7% from top
  static GRID_CONTAINER_WIDTH_PERCENT = 0.8;
  // 80% of screen width
  static GRID_CONTAINER_HEIGHT_RATIO = 1.4;
  // height = width * 1.4
  static GRID_CONTAINER_Y_PERCENT = 0.63;
  // 63% from top
  static GRID_GAP_PERCENT = 0.03;
  // 3% gap between cells
  // Input settings
  static MAX_ACTIVE_POINTERS = 1;
  // Single touch only to prevent multi-touch conflicts
  // Colors
  static TIMER_GRADIENT_COLORS = ["#33DDFF", "#664DFF"];
  // Cyan to purple
  static SCORE_GRADIENT_COLORS = ["#4cffae", "#32c4ff", "#5c67ff"];
  // Green to blue
  static BUTTON_GRADIENT_COLORS = ["#32c4ff", "#7f54ff", "#b63efc"];
  // Cyan to purple to pink
  static WARNING_COLOR = "#ff0000";
  // Red for warnings/errors
  // Sound settings
  static SOUND_VOLUME = 0.7;
  // Asset keys
  static ASSETS = {
    IMAGES: {
      BLOCK_ACTIVE: "block_active",
      BLOCK_INACTIVE: "block_inactive",
      GAME_START: "game_start",
      GAME_NAME: "game_name",
      TIMER_BG: "timer_bg",
      TIMER_ICON: "timer_icon",
      TIMER_COUNTDOWN_BG: "timer_countdown_bg",
      COUNTDOWN_3: "countdown-3",
      COUNTDOWN_2: "countdown-2",
      COUNTDOWN_1: "countdown-1",
      COUNTDOWN_GO: "countdown-go",
      BUTTON_BG: "button_bg",
      GAME_OVER: "game_over",
      BACK_TO_LOBBY: "back_to_lobby",
      GAME_BACKGROUND: "game_bg"
    },
    SOUNDS: {
      TAP: "tap",
      RIGHT: "right",
      WRONG: "wrong",
      TIMEOUT: "timeout",
      CLICK: "click",
      COUNTDOWN: "countdown",
      GO: "go"
    }
  };
  // Debug settings
  static DEBUG_MODE = true;
  static SHOW_FPS = false;
};
class GridBlock extends Phaser.GameObjects.Container {
  tileId;
  blockActiveImage;
  blockInactiveImage;
  isBlockActive = false;
  activationTime = 0;
  row;
  col;
  isShowingWrongState = false;
  constructor(scene, x, y, width, height, row, col) {
    super(scene, x, y);
    this.tileId = `block_${row}_${col}`;
    this.row = row;
    this.col = col;
    this.blockInactiveImage = scene.add.image(0, 0, GameConfig$1.ASSETS.IMAGES.BLOCK_INACTIVE);
    this.blockInactiveImage.setDisplaySize(width, height);
    this.blockInactiveImage.setOrigin(0.5, 0.5);
    this.add(this.blockInactiveImage);
    this.blockActiveImage = scene.add.image(0, 0, GameConfig$1.ASSETS.IMAGES.BLOCK_ACTIVE);
    this.blockActiveImage.setDisplaySize(width * 0.6, height * 0.85);
    this.blockActiveImage.setOrigin(0.5, 0.5);
    this.blockActiveImage.setVisible(false);
    this.add(this.blockActiveImage);
    scene.add.existing(this);
    this.setSize(width, height);
    this.setInteractive({ useHandCursor: true });
  }
  getBlockActive() {
    return this.isBlockActive;
  }
  getActivationTime() {
    return this.activationTime;
  }
  getTileId() {
    return this.tileId;
  }
  setBlockActive(active) {
    if (this.isBlockActive === active) return;
    this.isBlockActive = active;
    this.blockActiveImage.setVisible(active);
    this.blockInactiveImage.setVisible(!active);
    if (active) {
      this.activationTime = Date.now();
      this.blockActiveImage.setScale(0.95);
      this.scene.tweens.add({
        targets: this.blockActiveImage,
        scale: 1,
        duration: GameConfig$1.BLOCK_ANIMATION_DURATION,
        ease: "Back.easeOut"
      });
    }
  }
  setBlockWrong() {
    if (this.isShowingWrongState) return;
    this.isShowingWrongState = true;
    const image = this.isBlockActive ? this.blockActiveImage : this.blockInactiveImage;
    image.setTint(16729156);
    this.scene.time.delayedCall(400, () => {
      image.setTint(16777215);
      this.isShowingWrongState = false;
    });
  }
  reset() {
    this.isBlockActive = false;
    this.isShowingWrongState = false;
    this.blockActiveImage.setVisible(false);
    this.blockActiveImage.setTint(16777215);
    this.blockActiveImage.setScale(1);
    this.blockInactiveImage.setVisible(true);
    this.blockInactiveImage.setTint(16777215);
  }
}
class BingoCell extends Phaser.GameObjects.Container {
  letter;
  number;
  marked;
  isFree;
  blinkTween;
  background;
  markedBg;
  numberText;
  letterText;
  isBingoHeader;
  constructor(scene, x, y, letter, number, isFree = false) {
    super(scene, x, y);
    this.scene = scene;
    this.letter = letter;
    this.number = number;
    this.isFree = isFree;
    this.name = isFree ? "FREE" : `${letter}${number}`;
    this.marked = isFree;
    this.blinkTween = null;
    this.createVisuals();
    if (!isFree) {
      this.setupInteractivity();
    }
    scene.add.existing(this);
  }
  createVisuals() {
    const cellSize = 80;
    const bgGraphics = this.scene.add.graphics();
    bgGraphics.fillStyle(1118498, 1);
    bgGraphics.lineStyle(3, 58798, 1);
    bgGraphics.fillRoundedRect(-cellSize / 2, -cellSize / 2, cellSize, cellSize, 16);
    bgGraphics.strokeRoundedRect(-cellSize / 2, -cellSize / 2, cellSize, cellSize, 16);
    this.add(bgGraphics);
    this.background = bgGraphics;
    const markedGraphics = this.scene.add.graphics();
    markedGraphics.fillGradientStyle(
      3172095,
      // Top-left: blue
      4674303,
      // Top-right: blue-purple
      6180351,
      // Bottom-right: more purple
      2187007,
      // Bottom-left: blue
      1
    );
    markedGraphics.fillRoundedRect(-cellSize / 2, -cellSize / 2, cellSize, cellSize, 16);
    markedGraphics.alpha = this.isFree ? 1 : 0;
    this.add(markedGraphics);
    this.markedBg = markedGraphics;
    const displayText = this.isFree ? "FREE" : this.number.toString();
    const fontSize = this.isFree ? "24px" : "36px";
    this.numberText = this.scene.add.text(
      0,
      0,
      displayText,
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize,
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 2,
        shadow: { offsetX: 1, offsetY: 1, color: "#000000", blur: 2, fill: true }
      }
    ).setOrigin(0.5);
    this.add(this.numberText);
    this.letterText = this.scene.add.text(
      -28,
      -28,
      this.letter,
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "16px",
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold"
      }
    ).setOrigin(0.5);
    this.add(this.letterText);
    if (!this.isBingoHeader) {
      this.letterText.alpha = 0;
    }
  }
  setupInteractivity() {
    const cellSize = 80;
    const hitArea = new Phaser.Geom.Rectangle(-cellSize / 2, -cellSize / 2, cellSize, cellSize);
    this.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);
    this.on("pointerdown", () => {
      const gameScene = this.scene;
      if (!this.marked && !gameScene.gameEnd) {
        gameScene.checkForMatch(this);
      }
    });
    this.on("pointerover", () => {
      const gameScene = this.scene;
      if (!this.marked && !gameScene.gameEnd) {
        this.background.alpha = 0.8;
        this.scene.game.canvas.style.cursor = "pointer";
      }
    });
    this.on("pointerout", () => {
      const gameScene = this.scene;
      if (!this.marked && !gameScene.gameEnd) {
        this.background.alpha = 1;
        this.scene.game.canvas.style.cursor = "default";
      }
    });
  }
  /**
   * Blink the number to indicate a match
   * Equivalent to blink() in Unity's TableButtonController
   */
  blink() {
    if (this.blinkTween) {
      this.blinkTween.stop();
    }
    this.blinkTween = this.scene.tweens.add({
      targets: this.markedBg,
      alpha: { from: 0, to: 0.5 },
      duration: 240,
      yoyo: true,
      repeat: 3,
      onComplete: () => {
        this.markedBg.alpha = 0;
        this.blinkTween = null;
      }
    });
  }
  /**
   * Mark the cell as clicked
   * Equivalent to PlayPickUpStar() in Unity's TableButtonController
   */
  mark() {
    if (this.marked) return;
    this.marked = true;
    this.scene.tweens.add({
      targets: this.markedBg,
      alpha: 1,
      duration: 200,
      ease: "Power2"
    });
    this.scene.tweens.add({
      targets: this,
      scale: { from: 1.1, to: 1 },
      duration: 300,
      ease: "Back.Out"
    });
    if (this.blinkTween) {
      this.blinkTween.stop();
      this.blinkTween = null;
    }
    this.createMarkParticles();
    this.disableInteractive();
  }
  /**
   * Create particle effect for marking
   * Equivalent to particle effect in Unity
   */
  createMarkParticles() {
    const particles = this.scene.add.particles(this.x, this.y, "particle-star", {
      speed: { min: 70, max: 180 },
      scale: { start: 0.8, end: 0 },
      lifespan: 800,
      quantity: 20,
      tint: 58798,
      // Aqua color like the cell borders
      rotate: { min: 0, max: 360 },
      emitting: false,
      blendMode: "ADD"
    });
    particles.explode();
    const flash = this.scene.add.circle(0, 0, 45, 58798, 0.8);
    this.add(flash);
    this.scene.tweens.add({
      targets: flash,
      alpha: 0,
      scale: 1.8,
      duration: 500,
      ease: "Power2",
      onComplete: () => {
        flash.destroy();
      }
    });
    this.scene.time.delayedCall(1e3, () => {
      particles.destroy();
    });
  }
  /**
   * Create celebratory particle effect for win
   * Equivalent to PlayPickUpStar() when game ends
   */
  createWinParticles() {
    const particles = this.scene.add.particles(this.x, this.y, "particle-glow", {
      speed: { min: 30, max: 80 },
      scale: { start: 0.5, end: 0 },
      lifespan: 1500,
      quantity: 5,
      frequency: 200,
      tint: 58798,
      // Match the aqua color scheme
      blendMode: "ADD",
      rotate: { min: 0, max: 360 },
      emitting: true
    });
    this.scene.time.delayedCall(3e3, () => {
      particles.destroy();
    });
  }
}
class RightNumber extends Phaser.GameObjects.Container {
  rightIndex;
  letter;
  number;
  timerValue;
  timerTween;
  blinkTimer;
  timerFill;
  numberText;
  letterText;
  gameScene;
  // Store the GameScene reference
  constructor(scene, x, y, index, letter, number) {
    super(scene, x, y);
    this.scene = scene;
    this.gameScene = scene;
    this.rightIndex = index;
    this.letter = letter;
    this.number = number;
    this.name = `${letter}${number}`;
    this.timerValue = 1;
    this.timerTween = null;
    this.blinkTimer = null;
    this.createVisuals();
    this.startTimer();
    scene.add.existing(this);
    this.setScale(0);
    this.scene.tweens.add({
      targets: this,
      scale: 1,
      duration: 400,
      ease: "Back.Out",
      delay: 100
    });
  }
  createVisuals() {
    const cellSize = 85;
    const bgGraphics = this.scene.add.graphics();
    bgGraphics.fillGradientStyle(
      3172095,
      // Top-left: blue
      4674303,
      // Top-right: blue-purple
      6180351,
      // Bottom-right: more purple
      2187007,
      // Bottom-left: blue
      1
    );
    bgGraphics.fillCircle(0, 0, cellSize / 2);
    bgGraphics.lineStyle(3, 58798, 1);
    this.add(bgGraphics);
    this.timerFill = this.scene.add.graphics();
    this.add(this.timerFill);
    this.numberText = this.scene.add.text(
      0,
      5,
      // Slight offset for better visual balance
      this.number.toString(),
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "38px",
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 2,
        shadow: { offsetX: 1, offsetY: 1, color: "#000000", blur: 2, fill: true }
      }
    ).setOrigin(0.5);
    this.add(this.numberText);
    this.letterText = this.scene.add.text(
      0,
      -22,
      this.letter,
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: "22px",
        color: "#FFFFFF",
        align: "center",
        fontStyle: "bold",
        stroke: "#000000",
        strokeThickness: 1
      }
    ).setOrigin(0.5);
    this.add(this.letterText);
    this.updateTimerVisual();
  }
  /**
   * Start countdown timer
   * Equivalent to timer() in Unity's RightNumberController
   */
  startTimer() {
    this.timerValue = 1;
    this.updateTimerVisual();
    this.timerTween = this.scene.tweens.add({
      targets: this,
      timerValue: 0,
      duration: 8500,
      onUpdate: () => {
        this.updateTimerVisual();
      },
      onComplete: () => {
        this.startBlinking();
      }
    });
  }
  /**
   * Update timer visual based on current timer value - enhanced to match Unity
   */
  updateTimerVisual() {
    this.timerFill.clear();
    if (this.timerValue <= 0) return;
    this.timerFill.lineStyle(4, 16777215, 0.3);
    this.timerFill.strokeCircle(0, 0, 42);
    const startAngle = -90 * (Math.PI / 180);
    const endAngle = startAngle + 360 * this.timerValue * (Math.PI / 180);
    this.timerFill.lineStyle(4, 58798, 1);
    this.timerFill.beginPath();
    this.timerFill.arc(0, 0, 42, startAngle, endAngle, false);
    this.timerFill.strokePath();
  }
  /**
   * Calculate score based on timer value (DEPRECATED - server handles scoring)
   * This method is kept for backward compatibility with local fallback mode only
   * In production, all scoring is handled by the server based on timing
   */
  getScore() {
    if (this.timerValue >= 1) return 100;
    return Math.max(10, 100 - Math.floor((1 - this.timerValue) * 100));
  }
  /**
   * Start blinking matching bingo cell
   * Equivalent to startBlinking() in Unity's RightNumberController
   */
  startBlinking() {
    if (!this.gameScene || this.gameScene.gameEnd) return;
    try {
      const matchingCell = this.gameScene.findBingoCellByName(this.name);
      if (matchingCell && !matchingCell.marked) {
        matchingCell.blink();
        this.blinkTimer = this.scene.time.addEvent({
          delay: 1e3,
          callback: () => {
            try {
              if (!this.gameScene || this.gameScene.gameEnd) {
                if (this.blinkTimer) {
                  this.blinkTimer.remove();
                  this.blinkTimer = null;
                }
                return;
              }
              const cell = this.gameScene.findBingoCellByName(this.name);
              if (cell && !cell.marked) {
                cell.blink();
              } else {
                if (this.blinkTimer) {
                  this.blinkTimer.remove();
                  this.blinkTimer = null;
                }
              }
            } catch (error) {
              console.log("Error in blink timer callback:", error);
              if (this.blinkTimer) {
                this.blinkTimer.remove();
                this.blinkTimer = null;
              }
            }
          },
          loop: true
        });
      }
    } catch (error) {
      console.log("Error in startBlinking:", error);
    }
  }
  /**
   * Move this number down to a new position index
   * @param {number} newIndex - The new position index
   */
  moveToPosition(newIndex) {
    const oldIndex = this.rightIndex;
    this.rightIndex = newIndex;
    if (this.rightIndex < 0) {
      this.scene.tweens.add({
        targets: this,
        scale: 0,
        duration: 300,
        ease: "Back.In",
        onComplete: () => {
          this.destroy();
        }
      });
      return;
    }
    if (!this.gameScene) {
      this.destroy();
      return;
    }
    const position = this.gameScene.getRightPosition(this.rightIndex);
    this.scene.tweens.add({
      targets: this,
      x: position.x,
      duration: 400,
      ease: "Back.Out",
      easeParams: [1.5],
      onComplete: () => {
        if (this.rightIndex === 2 && oldIndex !== 2) {
          if (!this.gameScene) return;
          const matchingCell = this.gameScene.findBingoCellByName(this.name);
          if (matchingCell && !matchingCell.marked) {
            this.highlightMatchingCell(matchingCell);
          }
        }
      }
    });
  }
  /**
   * Create a highlight effect for the center position helper
   * @param {any} cell - The cell to highlight
   */
  highlightMatchingCell(cell) {
    try {
      if (!this.gameScene || !cell || cell.marked || this.gameScene.gameEnd) return;
      const highlight = this.scene.add.graphics();
      highlight.lineStyle(5, 16776960, 1);
      highlight.strokeRoundedRect(
        cell.x - 40,
        cell.y - 40,
        80,
        80,
        16
      );
      this.scene.tweens.add({
        targets: highlight,
        alpha: { from: 1, to: 0 },
        duration: 800,
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          try {
            highlight.destroy();
          } catch (error) {
            console.log("Error destroying highlight:", error);
          }
        }
      });
      try {
        cell.blink();
      } catch (error) {
        console.log("Error blinking cell:", error);
      }
    } catch (error) {
      console.log("Error in highlightMatchingCell:", error);
    }
  }
  /**
   * Destroy this number properly, stopping all timers
   */
  destroy() {
    try {
      if (this.timerTween) {
        this.timerTween.stop();
        this.timerTween = null;
      }
      if (this.blinkTimer) {
        this.blinkTimer.remove();
        this.blinkTimer = null;
      }
      this.gameScene = null;
      super.destroy();
    } catch (error) {
      console.log("Error in RightNumber destroy:", error);
      try {
        super.destroy();
      } catch (e) {
        console.log("Error in super.destroy():", e);
      }
    }
  }
}
class MatchingCard extends Phaser.GameObjects.Container {
  cardImage;
  cardBackground;
  isSelected = false;
  cardSize;
  cardId = "";
  constructor(scene, x, y, size = 120) {
    super(scene, x, y);
    this.cardSize = size;
    this.cardBackground = scene.add.image(0, 0, "card_bg").setOrigin(0.5);
    this.cardBackground.displayWidth = size;
    this.cardBackground.displayHeight = size;
    this.add(this.cardBackground);
    this.cardImage = scene.add.image(0, 0, "");
    this.cardImage.setScale(0);
    this.add(this.cardImage);
    this.setSize(size, size);
    this.setInteractive({ useHandCursor: true });
    this.on("pointerover", this.onPointerOver, this);
    this.on("pointerout", this.onPointerOut, this);
    scene.add.existing(this);
  }
  /**
   * Set the card ID
   */
  setCardId(id) {
    this.cardId = id;
  }
  /**
   * Get the card ID
   */
  getCardId() {
    return this.cardId;
  }
  /**
   * Set the tint color for the card image
   */
  setTint(color) {
    this.cardImage.setTint(color);
  }
  /**
   * Set the card's image
   */
  setCardImage(imageKey) {
    this.setData("imageKey", imageKey);
    console.log(`MatchingCard.setCardImage called with imageKey: ${imageKey}`);
    if (!imageKey || imageKey === "") {
      console.log("Empty imageKey provided, hiding card image");
      this.cardImage.setTexture("");
      this.cardImage.setVisible(false);
      return;
    }
    try {
      this.cardImage.setTexture(imageKey);
      this.cardImage.setVisible(true);
      this.cardImage.setScale(0);
      console.log(`Successfully set texture ${imageKey}, card image visible: ${this.cardImage.visible}`);
      this.scene.time.delayedCall(10, () => {
        const cardWidth = this.cardSize * 0.7;
        const cardHeight = this.cardSize * 0.7;
        const imgWidth = this.cardImage.width;
        const imgHeight = this.cardImage.height;
        const cardRatio = cardWidth / cardHeight;
        const imgRatio = imgWidth / imgHeight;
        let scale = 1;
        if (imgRatio > cardRatio) {
          scale = cardWidth / imgWidth;
        } else {
          scale = cardHeight / imgHeight;
        }
        this.cardImage.setData("targetScale", scale);
      });
    } catch (error) {
      console.error(`Failed to set texture for ${imageKey}`, error);
      console.error("Available textures:", this.scene.textures.list);
      this.cardImage.setTexture("");
      this.cardImage.setVisible(false);
    }
  }
  /**
   * Animate the card image appearing
   */
  animateCardImage(delay = 0) {
    this.scene.time.delayedCall(20, () => {
      const targetScale = this.cardImage.getData("targetScale") || 1;
      this.scene.tweens.add({
        targets: this.cardImage,
        scaleX: targetScale,
        scaleY: targetScale,
        duration: 300,
        ease: "Sine.easeInOut",
        delay
      });
    });
  }
  /**
   * Mark the card as selected (right or wrong)
   */
  markSelected(isRight) {
    if (this.isSelected) return;
    this.isSelected = true;
    if (isRight) {
      this.cardBackground.setTexture("card_correct_bg");
      this.cardBackground.displayWidth = this.cardSize;
      this.cardBackground.displayHeight = this.cardSize;
      this.cardImage.setTint(65280);
    } else {
      this.cardBackground.setTexture("card_incorrect_bg");
      this.cardBackground.displayWidth = this.cardSize;
      this.cardBackground.displayHeight = this.cardSize;
      this.cardImage.setTint(16711680);
      this.scene.tweens.add({
        targets: this,
        x: this.x + 10,
        duration: 40,
        yoyo: true,
        repeat: 5
      });
    }
  }
  /**
   * Reset the card's selection state
   */
  resetSelection() {
    this.isSelected = false;
    this.cardBackground.setTexture("card_bg");
    this.cardBackground.displayWidth = this.cardSize;
    this.cardBackground.displayHeight = this.cardSize;
  }
  /**
   * Handle pointer over event
   */
  onPointerOver() {
    if (this.isSelected) return;
    this.scene.tweens.add({
      targets: this,
      scaleX: 1.05,
      scaleY: 1.05,
      duration: 100,
      ease: "Sine.easeOut"
    });
  }
  /**
   * Handle pointer out event
   */
  onPointerOut() {
    if (this.isSelected) return;
    this.scene.tweens.add({
      targets: this,
      scaleX: 1,
      scaleY: 1,
      duration: 100,
      ease: "Sine.easeOut"
    });
  }
  /**
   * Clean up resources when destroying the card
   */
  destroy(fromScene) {
    super.destroy(fromScene);
  }
  /**
   * Get the card background image
   */
  getCardBackground() {
    return this.cardBackground;
  }
}
class GameConfig2 {
  static GAME_DURATION = 30;
  // in seconds
  static SOUND_VOLUME = 0.7;
  static CIRCLE_RADIUS = 38.5;
  // 35 * 1.1 = 38.5 (10% larger)
  // Animation settings
  static FLASH_DURATION = 100;
  // milliseconds
  static TRANSITION_DELAY = 50;
  // milliseconds
  static TRANSITION_FADE_DURATION = 250;
  // milliseconds
}
class NumberObject extends Phaser.GameObjects.Container {
  number;
  circle;
  text;
  glowEffect;
  innerStroke;
  originalX;
  originalY;
  radius;
  objectState = "active";
  constructor(scene, x, y, number) {
    super(scene, x, y);
    this.number = number;
    this.originalX = x;
    this.originalY = y;
    this.radius = GameConfig2.CIRCLE_RADIUS;
    this.glowEffect = scene.add.graphics();
    this.createGlowEffect();
    this.add(this.glowEffect);
    this.circle = scene.add.sprite(0, 0, "circle");
    this.circle.setScale(this.radius * 2 / 160);
    this.circle.setInteractive();
    this.circle.on("pointerdown", () => {
      this.onPointerDown();
    });
    this.add(this.circle);
    this.createInnerStroke();
    this.text = scene.add.text(0, 0, (number + 1).toString(), {
      fontSize: "46px",
      // Increased proportionally (42 * 1.1 = 46.2)
      fontFamily: "Arial",
      color: "#ffffff",
      stroke: "#003366",
      strokeThickness: 2
    });
    this.text.setOrigin(0.5);
    this.add(this.text);
    this.scene.add.existing(this);
    this.setSize(this.radius * 2, this.radius * 2);
    this.setInteractive(new Phaser.Geom.Circle(0, 0, this.radius), Phaser.Geom.Circle.Contains);
    this.on("pointerdown", () => {
      this.onPointerDown();
    });
    this.playSpawnAnimation();
  }
  get numberValue() {
    return this.number;
  }
  get currentState() {
    return this.objectState;
  }
  setObjectState(newState) {
    this.objectState = newState;
  }
  // No need for createCircle method as we're using an SVG image
  createGlowEffect() {
    this.glowEffect.clear();
    for (let i = this.radius + 15; i >= this.radius; i -= 2) {
      const opacity = (this.radius + 16 - i) / 16;
      this.glowEffect.lineStyle(3, 48127, opacity * 0.6);
      this.glowEffect.strokeCircle(0, 0, i);
    }
    this.glowEffect.fillStyle(13158, 0.4);
    this.glowEffect.fillCircle(0, 0, this.radius);
  }
  setActive(active) {
    if (active) {
      this.glowEffect.clear();
      this.glowEffect.fillStyle(35071, 0.9);
      this.glowEffect.fillCircle(0, 0, this.radius);
      this.glowEffect.lineStyle(4, 65535, 1);
      this.glowEffect.strokeCircle(0, 0, this.radius);
      this.circle.setAlpha(1);
      this.setObjectState("active");
    } else {
      this.createGlowEffect();
      this.circle.setAlpha(0.9);
      this.setObjectState("inactive");
    }
    return this;
  }
  createInnerStroke() {
    this.innerStroke = this.scene.add.graphics();
    this.innerStroke.lineStyle(2, 16777215, 0.8);
    this.innerStroke.strokeCircle(0, 0, this.radius - 3);
    this.add(this.innerStroke);
  }
  setError() {
    this.setObjectState("error");
    this.glowEffect.clear();
    this.glowEffect.fillStyle(16711680, 0.7);
    this.glowEffect.fillCircle(0, 0, this.radius);
    this.glowEffect.lineStyle(4, 16711680, 1);
    this.glowEffect.strokeCircle(0, 0, this.radius);
    this.circle.setTint(16737894);
    this.scene.tweens.add({
      targets: this,
      x: this.x + 3,
      duration: 50,
      //MORE TIME PUNISHED FOR WRONG SELECTION
      yoyo: true,
      repeat: 5,
      ease: "Power1",
      onComplete: () => {
        this.x = this.originalX;
        this.y = this.originalY;
        this.clearError();
      }
    });
  }
  clearError() {
    this.createGlowEffect();
    this.circle.clearTint();
    this.setObjectState("active");
  }
  reset() {
    this.setActive(false);
    this.clearError();
    this.setVisible(true);
    this.alpha = 1;
  }
  onPointerDown() {
    const gameScene = this.scene;
    if (gameScene.handleNumberClick) {
      gameScene.handleNumberClick(this);
    }
  }
  startRotation() {
    this.scene.tweens.add({
      targets: this,
      angle: -360,
      duration: 2e3,
      repeat: -1,
      ease: "Linear"
    });
  }
  stopRotation() {
    this.scene.tweens.killTweensOf(this);
    this.angle = 0;
  }
  playSpawnAnimation() {
    this.setScale(0);
    this.scene.tweens.add({
      targets: this,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 250,
      // Half the total duration to reach 110%
      ease: "Back.easeOut",
      onComplete: () => {
        this.scene.tweens.add({
          targets: this,
          scaleX: 1,
          scaleY: 1,
          duration: 250,
          // Remaining duration to reach 100%
          ease: "Back.easeIn"
        });
      }
    });
  }
  playDisappearAnimation(onComplete) {
    this.scene.tweens.add({
      targets: this,
      scaleX: 0,
      scaleY: 0,
      duration: 300,
      // Quick disappear animation
      ease: "Back.easeIn",
      onComplete: () => {
        this.setVisible(false);
        onComplete();
      }
    });
  }
}
export {
  GameStatus as G,
  RoomStatus as R,
  gameState as a,
  checkIconState as c,
  generateIcon as g,
  opponentState as o,
  roomState as r
};
