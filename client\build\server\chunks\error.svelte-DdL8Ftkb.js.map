{"version": 3, "file": "error.svelte-DdL8Ftkb.js", "sources": ["../../../.svelte-kit/adapter-node/entries/fallbacks/error.svelte.js"], "sourcesContent": ["import { F as escape_html, E as pop, A as push } from \"../../chunks/index.js\";\nimport { p as page } from \"../../chunks/index2.js\";\nfunction Error($$payload, $$props) {\n  push();\n  $$payload.out.push(`<h1>${escape_html(page.status)}</h1> <p>${escape_html(page.error?.message)}</p>`);\n  pop();\n}\nexport {\n  Error as default\n};\n"], "names": [], "mappings": ";;;;AAEA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;AACvG,EAAE,GAAG,EAAE;AACP;;;;"}