import { Socket } from 'socket.io';
import { GameService } from '../../services/gameService.js';
import { getAuthenticatedUser } from '../../middleware/auth.js';

import { logger } from '../../utils/logger.js';
import { GAME_TYPES } from '../../utils/constants.js';
import { LevelGenerator as NumberConnectLevelGenerator } from './NumberConnectLevelGenerator.js';
import { sessionService } from '../../services/sessionService.js';


// Game configuration
const NUMBER_CONNECT_CONFIG = {
  GRID_SIZE: 5,
  MAX_NUMBER: 5,
  INITIAL_LIVES: 3,
  SCORING: {
    NUMBER_REACHED: 100,
    TIME_BONUS_MULTIPLIER: 2,
    MOVE_PENALTY: 5,
    COMPLETION_BONUS: 1000
  },
  PENALTIES: {
    INVALID_MOVE: 1, // Lives deducted for invalid moves
    WRONG_PATH: 1
  }
};

// Interfaces
export interface Position {
  row: number;
  col: number;
}

export interface GridCell {
  visited: boolean;
  number: number | null;
  hasPath: boolean;
}

export interface GeneratedLevel {
  grid: GridCell[][];
  solutionPath: Position[];
  numberPositions: { [key: number]: Position };
  maxNumber: number;
}

export interface NumberConnectGameState {
  grid: GridCell[][];
  solutionPath: Position[];
  numberPositions: { [key: number]: Position };
  playerPath: Position[];
  currentNumber: number;
  moveCount: number;
  gameStarted: boolean;
  gameEnded: boolean;
  startTime: number | null; // overall game start
  currentLevel: number;
  levelsCompleted: number;
  totalScore: number;
  // Per-board timing
  boardStartTime: number | null;
  boardTimes: number[]; // ms per completed board
}

export interface PathMoveData {
  roomId: string;
  gameId: string;
  action: {
    type: 'path_move';
    data: {
      newCell: Position;
      isDrawing: boolean;
      moveType: 'start' | 'continue' | 'end';
      timestamp: number;
    };
  };
}

export interface GameInitResult {
  success: boolean;
  message?: string;
  gameState?: any;
}

export interface PathMoveResult {
  success: boolean;
  isValid: boolean;
  newPath: Position[];
  currentNumber: number;
  points: number;
  newScore: number;
  newLives: number;
  gameEnded: boolean;
  gameWon?: boolean;
  invalidReason?: string;
  levelCompleted?: boolean;
  newLevel?: boolean;
  currentLevel?: number;
  levelsCompleted?: number;
  boardTimeMs?: number; // per-board time for completed boards
  newPuzzle?: {
    grid: GridCell[][];
    numberPositions: { [key: number]: Position };
    solutionPath: Position[];
    maxNumber?: number;
  };
}

/**
 * NumberConnect Game Controller
 * Handles server-side game logic for the NumberConnect/MumsNumbers game
 */
export class NumberConnectController {
  private gameService: GameService;
  private gameStates = new Map<string, NumberConnectGameState>();
  private levelGen: NumberConnectLevelGenerator;

  constructor(gameService: GameService) {
    this.gameService = gameService;
    this.levelGen = new NumberConnectLevelGenerator(
      NUMBER_CONNECT_CONFIG.GRID_SIZE,
      NUMBER_CONNECT_CONFIG.MAX_NUMBER
    );
  }

  /**
   * Initialize a new NumberConnect game
   */
  initializeGame(roomId: string, _socket: Socket, userId?: string): GameInitResult {
    try {
      // Create game state using GameService
      const gameState = this.gameService.createGameState(
        roomId,
        GAME_TYPES.MUMS_NUMBERS,
        NUMBER_CONNECT_CONFIG.INITIAL_LIVES,
        userId
      );

      if (!gameState) {
        return { success: false, message: 'Failed to create game state' };
      }

      // Generate puzzle using room/user-scoped PRNG
      const rngInt = (max: number) => sessionService.getPrngInt(roomId, max, userId);
      const levelGen = new NumberConnectLevelGenerator(
        NUMBER_CONNECT_CONFIG.GRID_SIZE,
        NUMBER_CONNECT_CONFIG.MAX_NUMBER,
        rngInt
      );
      const generatedLevel = levelGen.generateSolvablePuzzle();

      // Create NumberConnect-specific game state
      const numberConnectState: NumberConnectGameState = {
        grid: generatedLevel.grid,
        solutionPath: generatedLevel.solutionPath,
        numberPositions: generatedLevel.numberPositions,
        playerPath: [],
        currentNumber: 1,
        moveCount: 0,
        gameStarted: false,
        gameEnded: false,
        startTime: null,
        currentLevel: 1,
        levelsCompleted: 0,
        totalScore: 0,
        boardStartTime: null,
        boardTimes: []
      };

      // Store the game state
      const key = `${roomId}:${userId ?? ''}`;
      this.gameStates.set(key, numberConnectState);

      logger.info(`NumberConnect game initialized for room ${roomId}`, {
        key,
        userId,
        hasState: !!numberConnectState,
        totalStates: this.gameStates.size,
        numberPositions: numberConnectState.numberPositions,
        gridHasNumbers: this.validateGridHasNumbers(numberConnectState.grid)
      });
      return { success: true, gameState };

    } catch (error) {
      logger.error(`Error initializing NumberConnect game in room ${roomId}:`, error);
      return { success: false, message: 'Internal server error during initialization' };
    }
  }

  /**
   * Start the NumberConnect game
   */
  startGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    const gameState = this.gameService.getGameState(roomId, userId);
    if (!gameState) {
      return { success: false, message: 'Game not initialized' };
    }

    if (gameState.status === 'ended') {
      this.cleanupGame(roomId, userId);
      return { success: false, message: 'Game ended, please reinitialize' };
    }

    if (gameState.status !== 'waiting') {
      return { success: false, message: 'Game not in waiting state' };
    }

    // Start the game using GameService
    const started = this.gameService.startGame(roomId, socket, userId);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Update NumberConnect-specific state
    const key = `${roomId}:${userId ?? ''}`;
    const numberConnectState = this.gameStates.get(key);
    if (numberConnectState) {
      numberConnectState.gameStarted = true;
      numberConnectState.startTime = Date.now();
      numberConnectState.boardStartTime = Date.now();
      this.gameStates.set(key, numberConnectState);

      // Register a timeout hook to award partial board progress when overall timer expires
      this.gameService.registerTimeoutHook(roomId, userId, () => {
        this.awardPartialOnTimeout(roomId, userId);
      });
    }

    logger.info(`NumberConnect game started for room ${roomId}`, {
      key,
      userId,
      hasNumberConnectState: !!numberConnectState,
      gameStatus: gameState.status,
      totalStates: this.gameStates.size
    });
    return { success: true, gameState };
  }

  /**
   * Handle path move action
   */
  handlePathMove(roomId: string, newCell: Position, moveType: 'start' | 'continue' | 'end', userId?: string): PathMoveResult {
    const key = `${roomId}:${userId ?? ''}`;
    const gameState = this.gameService.getGameState(roomId, userId);
    const numberConnectState = this.gameStates.get(key);

    // Check if game and state are valid
    if (!gameState || !numberConnectState || gameState.status !== 'active') {
      logger.warn(`Path move ignored for room ${roomId}:`, {
        hasGameState: !!gameState,
        hasNumberConnectState: !!numberConnectState,
        gameStatus: gameState?.status,
        userId,
        key
      });
      return {
        success: false,
        isValid: false,
        newPath: [],
        currentNumber: 1,
        points: 0,
        newScore: gameState?.score || 0,
        newLives: gameState?.lives || 0,
        gameEnded: true,
        invalidReason: 'Game not active'
      };
    }

    // Validate the move
    const validationResult = this.validateMove(numberConnectState, newCell, moveType);

    if (!validationResult.isValid) {
      // Invalid move - deduct life
      const livesResult = this.gameService.deductLife(roomId, userId);

      // Check if game should end due to no lives
      if (livesResult.gameEnded) {
        this.endGame(roomId, 'no_lives', userId);
      }

      return {
        success: true,
        isValid: false,
        newPath: numberConnectState.playerPath,
        currentNumber: numberConnectState.currentNumber,
        points: 0,
        newScore: gameState.score,
        newLives: livesResult.newLives,
        gameEnded: livesResult.gameEnded,
        invalidReason: validationResult.reason
      };
    }

    // Valid move - update path
    const updatedPath = this.updatePlayerPath(numberConnectState, newCell, moveType);
    const previousNumber = numberConnectState.currentNumber;

    // Update current number and check for points
    this.updateCurrentNumber(numberConnectState);

    // Award points for reaching new numbers
    let points = 0;
    if (numberConnectState.currentNumber > previousNumber) {
      points = (numberConnectState.currentNumber - previousNumber) * NUMBER_CONNECT_CONFIG.SCORING.NUMBER_REACHED;
      this.gameService.updateScore(roomId, points, "add", userId);
    }

    // Check for game completion
    const isComplete = this.checkGameCompletion(numberConnectState);
    if (isComplete) {
      // Board completed: compute per-board time and time-based score (placeholder formula)
      const now = Date.now();
      const boardStart = numberConnectState.boardStartTime ?? now;
      const elapsedMs = Math.max(0, now - boardStart);
      numberConnectState.boardTimes.push(elapsedMs);

      const elapsedSec = Math.floor(elapsedMs / 1000);
      // Placeholder scoring: higher score for faster completion
      const timeBasedPoints = Math.max(0, 1000 - elapsedSec * 50);
      this.gameService.updateScore(roomId, timeBasedPoints, "add", userId);
      numberConnectState.totalScore += timeBasedPoints;
      numberConnectState.levelsCompleted++;

      // Generate a new level and continue (continuous boards)
      this.generateNewLevel(roomId, userId, numberConnectState);
      numberConnectState.boardStartTime = Date.now();

      return {
        success: true,
        isValid: true,
        newPath: updatedPath,
        currentNumber: numberConnectState.currentNumber,
        points: points + timeBasedPoints,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        gameWon: false,
        levelCompleted: true,
        newLevel: true,
        currentLevel: numberConnectState.currentLevel,
        levelsCompleted: numberConnectState.levelsCompleted,
        boardTimeMs: elapsedMs,
        newPuzzle: {
          grid: numberConnectState.grid,
          numberPositions: numberConnectState.numberPositions,
          solutionPath: numberConnectState.solutionPath,
          maxNumber: NUMBER_CONNECT_CONFIG.MAX_NUMBER
        }
      };
    }

    return {
      success: true,
      isValid: true,
      newPath: updatedPath,
      currentNumber: numberConnectState.currentNumber,
      points,
      newScore: gameState.score,
      newLives: gameState.lives,
      gameEnded: false
    };
  }

  /**
   * Generate a solvable puzzle using the proven algorithm with enhanced randomization
   */


  /**
   * Initialize empty grid
   */


  /**
   * Populate grid with numbers at specified positions
   */


  /**
   * Validate that grid has all required numbers
   */
  private validateGridHasNumbers(grid: GridCell[][]): boolean {
    const foundNumbers = new Set<number>();

    for (let row = 0; row < grid.length; row++) {
      for (let col = 0; col < grid[row].length; col++) {
        const cell = grid[row][col];
        if (cell.number !== null) {
          foundNumbers.add(cell.number);
        }
      }
    }

    // Check that we have all numbers 1-5
    for (let number = 1; number <= NUMBER_CONNECT_CONFIG.MAX_NUMBER; number++) {
      if (!foundNumbers.has(number)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Find a complete path that visits all cells exactly once
   * Uses backtracking algorithm to ensure solvability
   */


  /**
   * Get valid neighboring cells (adjacent only, no diagonals)
   */


  /**
   * Shuffle array using Fisher-Yates algorithm
   */


  /**
   * Check if position is valid
   */
  private isValidPosition(row: number, col: number): boolean {
    return row >= 0 && row < NUMBER_CONNECT_CONFIG.GRID_SIZE &&
           col >= 0 && col < NUMBER_CONNECT_CONFIG.GRID_SIZE;
  }

  /**
   * Place numbers along the solution path with randomized spacing
   */


  /**
   * Validate that a given path is actually solvable
   * Uses the same validation logic as the client for consistency
   */


  /**
   * Generate a fallback puzzle with varied simple patterns
   */


  /**
   * Generate a snake-like path
   */


  /**
   * Generate a spiral path from outside to inside
   */


  /**
   * Generate a zigzag path
   */


  /**
   * Validate a move
   */
  private validateMove(state: NumberConnectGameState, newCell: Position, moveType: 'start' | 'continue' | 'end'): { isValid: boolean; reason?: string } {
    // Check if position is valid
    if (!this.isValidPosition(newCell.row, newCell.col)) {
      return { isValid: false, reason: 'Position out of bounds' };
    }

    if (moveType === 'start') {
      // Starting move validation
      if (state.playerPath.length === 0) {
        // Must start from number 1
        const cellData = state.grid[newCell.row][newCell.col];
        if (cellData.number !== 1) {
          return { isValid: false, reason: 'Must start from number 1' };
        }
      } else {
        // Can start from any cell adjacent to the last cell in path, or from the last cell itself
        const lastCell = state.playerPath[state.playerPath.length - 1];
        const isLastCell = newCell.row === lastCell.row && newCell.col === lastCell.col;
        const isAdjacent = Math.abs(newCell.row - lastCell.row) + Math.abs(newCell.col - lastCell.col) === 1;

        if (!isLastCell && !isAdjacent) {
          return { isValid: false, reason: 'Must start adjacent to current path' };
        }
      }
    } else {
      // Continue/end move validation
      if (state.playerPath.length === 0) {
        return { isValid: false, reason: 'No path started' };
      }

      const lastCell = state.playerPath[state.playerPath.length - 1];
      const isAdjacent = Math.abs(newCell.row - lastCell.row) + Math.abs(newCell.col - lastCell.col) === 1;

      if (!isAdjacent) {
        return { isValid: false, reason: 'Move must be adjacent' };
      }

      // Check if this move would cause the path to cross itself
      if (this.wouldCrossPath(state.playerPath, newCell)) {
        return { isValid: false, reason: 'Path cannot cross itself' };
      }

      // Check if we're trying to move to a numbered cell out of sequence
      const cellData = state.grid[newCell.row][newCell.col];
      if (cellData.number) {
        if (cellData.number !== state.currentNumber) {
          return { isValid: false, reason: `Must visit number ${state.currentNumber} next` };
        }
      }
    }

    return { isValid: true };
  }

  /**
   * Check if adding a new cell would cause path crossing
   */
  private wouldCrossPath(currentPath: Position[], newCell: Position): boolean {
    if (currentPath.length < 2) return false;

    const lastCell = currentPath[currentPath.length - 1];

    // Check if the new line segment crosses any existing line segments
    for (let i = 0; i < currentPath.length - 2; i++) {
      const segmentStart = currentPath[i];
      const segmentEnd = currentPath[i + 1];

      if (this.linesIntersect(lastCell, newCell, segmentStart, segmentEnd)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if two line segments intersect
   */
  private linesIntersect(p1: Position, p2: Position, p3: Position, p4: Position): boolean {
    // If any endpoints are the same, they don't cross (they connect)
    if (this.cellsEqual(p1, p3) || this.cellsEqual(p1, p4) ||
        this.cellsEqual(p2, p3) || this.cellsEqual(p2, p4)) {
      return false;
    }

    // Use traditional line intersection for actual crossing detection
    const denominator = (p1.row - p2.row) * (p3.col - p4.col) - (p1.col - p2.col) * (p3.row - p4.row);

    if (denominator === 0) return false; // Lines are parallel

    const t = ((p1.row - p3.row) * (p3.col - p4.col) - (p1.col - p3.col) * (p3.row - p4.row)) / denominator;
    const u = -((p1.row - p2.row) * (p1.col - p3.col) - (p1.col - p2.col) * (p1.row - p3.row)) / denominator;

    // Use slightly more inclusive bounds to account for grid movement
    return t > 0.001 && t < 0.999 && u > 0.001 && u < 0.999;
  }

  /**
   * Check if two cells are equal
   */
  private cellsEqual(cell1: Position, cell2: Position): boolean {
    return cell1.row === cell2.row && cell1.col === cell2.col;
  }

  /**
   * Update player path with new cell
   */
  private updatePlayerPath(state: NumberConnectGameState, newCell: Position, _moveType: 'start' | 'continue' | 'end'): Position[] {
    // Check if cell is already in current path (backtracking)
    const existingIndex = state.playerPath.findIndex(c => this.cellsEqual(c, newCell));

    if (existingIndex !== -1) {
      // Backtrack to this cell
      state.playerPath = state.playerPath.slice(0, existingIndex + 1);
    } else {
      // Add new cell
      state.playerPath.push(newCell);
      state.moveCount++;
    }

    return state.playerPath;
  }

  /**
   * Update current number based on path
   */
  private updateCurrentNumber(state: NumberConnectGameState): void {
    let expectedNumber = 1;

    for (const cell of state.playerPath) {
      const cellData = state.grid[cell.row][cell.col];
      if (cellData.number) {
        if (cellData.number === expectedNumber) {
          expectedNumber++;
        }
      }
    }

    state.currentNumber = expectedNumber;
  }

  /**
   * Check if game is completed
   */
  private checkGameCompletion(state: NumberConnectGameState): boolean {
    // Must cover ALL cells AND have all numbers in sequence
    const allCellsCovered = state.playerPath.length === NUMBER_CONNECT_CONFIG.GRID_SIZE * NUMBER_CONNECT_CONFIG.GRID_SIZE;
    const allNumbersFound = state.currentNumber > NUMBER_CONNECT_CONFIG.MAX_NUMBER;

    return allCellsCovered && allNumbersFound;
  }

  /**
   * End the game
   */
  private endGame(roomId: string, reason: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    const numberConnectState = this.gameStates.get(key);

    if (numberConnectState) {
      numberConnectState.gameEnded = true;
      this.gameStates.set(key, numberConnectState);
    }

    // End game using GameService
    this.gameService.endGame(roomId, reason as any, userId);
  }

  /**
   * Clean up game state
   */
  private cleanupGame(roomId: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    this.gameStates.delete(key);
    logger.info(`Cleaned up NumberConnect game state for room ${roomId}`);
  }

  /**
   * Get current game state for client
   */
  getGameState(roomId: string, userId?: string): NumberConnectGameState | null {
    const key = `${roomId}:${userId ?? ''}`;
    let state = this.gameStates.get(key);

    // If not found with userId, try without userId (fallback for unauthenticated users)
    if (!state && userId) {
      const fallbackKey = `${roomId}:`;
      state = this.gameStates.get(fallbackKey);
    }

    return state || null;
  }

  /**
   * Setup socket event handlers for NumberConnect
   */
  public setupSocketHandlers(socket: Socket): void {
    // Get authenticated user to check if this is a NumberConnect game
    const user = getAuthenticatedUser(socket);
    if (!user || user.gameId !== GAME_TYPES.MUMS_NUMBERS) {
      return; // Only handle events for NumberConnect games
    }

    // Game initialization event (called at client load)
    socket.on('init', (data) => {
      this.handleGameInit(socket, data);
    });

    // Game start event (called after countdown)
    socket.on('start', (data) => {
      this.handleGameStart(socket, data);
    });

    // Generic game end event
    socket.on('end', (data) => {
      this.handleGameEnd(socket, data);
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      this.handleGameAction(socket, data);
    });
  }

  /**
   * Handle game initialization
   */
  private handleGameInit(socket: Socket, data: any): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId or gameId', 'initialization');
      return;
    }

    try {
      // Initialize the game (but don't start it yet)
      const user = getAuthenticatedUser(socket);
      const userId = user?.userId;
      const result = this.initializeGame(roomId, socket, userId);

      if (result.success && result.gameState) {
        // Get current puzzle state for the client
        const numberConnectState = this.getGameState(roomId, userId);

        socket.emit('initialized', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          puzzleState: numberConnectState ? {
            grid: numberConnectState.grid,
            numberPositions: numberConnectState.numberPositions,
            currentNumber: numberConnectState.currentNumber,
            playerPath: numberConnectState.playerPath
          } : null,
          message: 'Game initialized!'
        });

        logger.info(`${gameId} game initialized in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to initialize game', 'initialization');
      }
    } catch (error) {
      logger.error(`Error initializing ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error', 'initialization');
    }
  }

  /**
   * Handle game start
   */
  private handleGameStart(socket: Socket, data: any): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId or gameId', 'start');
      return;
    }

    try {
      // Start the game (game should already be initialized)
      const user = getAuthenticatedUser(socket);
      const userId = user?.userId;
      const result = this.startGame(roomId, socket, userId);

      if (result.success && result.gameState) {
        // Get current puzzle state
        const numberConnectState = this.getGameState(roomId, userId);

        socket.emit('started', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          puzzleState: numberConnectState ? {
            grid: numberConnectState.grid,
            numberPositions: numberConnectState.numberPositions,
            currentNumber: numberConnectState.currentNumber,
            playerPath: numberConnectState.playerPath
          } : null,
          message: 'Game started!'
        });

        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to start game', 'start');
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error', 'start');
    }
  }

  /**
   * Handle game end
   */
  private handleGameEnd(socket: Socket, data: any): void {
    const { roomId } = data;

    if (!roomId) {
      socket.emit('error', { message: 'Missing roomId' });
      return;
    }

    try {
      const user = getAuthenticatedUser(socket);
      const userId = user?.userId;

      // End the game
      this.endGame(roomId, 'manual_end', userId);

      socket.emit('ended', {
        reason: 'manual_end',
        message: 'Game ended by player'
      });

      logger.info(`NumberConnect game manually ended in room ${roomId}`);
    } catch (error) {
      logger.error(`Error ending NumberConnect game in room ${roomId}:`, error);
      socket.emit('error', { message: 'Failed to end game' });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: PathMoveData): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      switch (action.type) {
        case 'path_move':
          this.handlePathMoveAction(socket, data);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle path move action
   */
  private handlePathMoveAction(socket: Socket, data: any): void {
    const { roomId, action } = data;

    // Check if this is a complete path submission or individual move
    if (action.data.path) {
      // Handle complete path validation
      this.handleCompletePathMove(socket, data);
    } else {
      // Handle individual move (legacy support)
      const { newCell, moveType } = action.data;

      if (!newCell || !moveType) {
        socket.emit('error', {
          message: 'Missing move data'
        });
        return;
      }

      try {
        // Process the path move
        const user = getAuthenticatedUser(socket);
        const userId = user?.userId;
        const result = this.handlePathMove(roomId, newCell, moveType, userId);

      if (result.success) {
        // Get updated puzzle state
        const numberConnectState = this.getGameState(roomId, userId);

        socket.emit('action_result', {
          actionType: 'path_move',
          data: {
            isValid: result.isValid,
            newPath: result.newPath,
            currentNumber: result.currentNumber,
            points: result.points,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            gameWon: result.gameWon,
            invalidReason: result.invalidReason,
            // Critical completion fields expected by client
            levelCompleted: result.levelCompleted,
            newLevel: result.newLevel,
            currentLevel: result.currentLevel,
            levelsCompleted: result.levelsCompleted,
            boardTimeMs: result.boardTimeMs,
            newPuzzle: result.newPuzzle,
            // Current puzzle snapshot for UI
            puzzleState: numberConnectState ? {
              grid: numberConnectState.grid,
              numberPositions: numberConnectState.numberPositions,
              currentNumber: numberConnectState.currentNumber,
              playerPath: numberConnectState.playerPath
            } : null
          }
        });

        // If game ended, broadcast game end event
        if (result.gameEnded) {
          socket.emit('ended', {
            reason: result.gameWon ? 'completed' : 'no_lives',
            finalScore: result.newScore,
            gameWon: result.gameWon
          });
        }

        logger.info(`Path move processed in room ${roomId}: valid=${result.isValid}, score=${result.newScore}`);
      } else {
        logger.error(`Failed to process path move in room ${roomId}:`, {
          gameState: !!this.gameService.getGameState(roomId, userId),
          numberConnectState: !!this.getGameState(roomId, userId),
          moveType,
          newCell
        });
        socket.emit('error', {
          message: 'Failed to process path move'
        });
      }
      } catch (error) {
        logger.error(`Error processing path move in room ${roomId}:`, error);
        socket.emit('error', {
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * Handle complete path validation
   */
  private handleCompletePathMove(socket: Socket, data: any): void {
    const { roomId, action } = data;
    const { path } = action.data;

    if (!path || !Array.isArray(path)) {
      socket.emit('error', {
        message: 'Missing or invalid path data'
      });
      return;
    }

    try {
      const user = getAuthenticatedUser(socket);
      const userId = user?.userId;
      const result = this.validateCompletePath(roomId, path, userId);

      if (result.success) {
        // Get updated puzzle state
        const numberConnectState = this.getGameState(roomId, userId);

        socket.emit('action_result', {
          actionType: 'path_move',
          data: {
            isValid: result.isValid,
            newPath: result.newPath,
            currentNumber: result.currentNumber,
            points: result.points,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            gameWon: result.gameWon,
            invalidReason: result.invalidReason,
            // Critical completion fields expected by client
            levelCompleted: result.levelCompleted,
            newLevel: result.newLevel,
            currentLevel: result.currentLevel,
            levelsCompleted: result.levelsCompleted,
            boardTimeMs: result.boardTimeMs,
            newPuzzle: result.newPuzzle,
            // Current puzzle snapshot for UI
            puzzleState: numberConnectState ? {
              grid: numberConnectState.grid,
              numberPositions: numberConnectState.numberPositions,
              currentNumber: numberConnectState.currentNumber,
              playerPath: numberConnectState.playerPath
            } : null
          }
        });

        // If game ended, broadcast game end event
        if (result.gameEnded) {
          socket.emit('ended', {
            reason: result.gameWon ? 'completed' : 'no_lives',
            finalScore: result.newScore,
            gameWon: result.gameWon
          });
        }

        logger.info(`Complete path processed in room ${roomId}: valid=${result.isValid}, score=${result.newScore}`);
      } else {
        logger.error(`Failed to process complete path in room ${roomId}:`, {
          gameState: !!this.gameService.getGameState(roomId, userId),
          numberConnectState: !!this.getGameState(roomId, userId),
          pathLength: path.length
        });
        socket.emit('error', {
          message: 'Failed to process path'
        });
      }
    } catch (error) {
      logger.error(`Error processing complete path in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Validate a complete path submission
   */
  validateCompletePath(roomId: string, path: Position[], userId?: string): PathMoveResult {
    const key = `${roomId}:${userId ?? ''}`;
    const gameState = this.gameService.getGameState(roomId, userId);
    let numberConnectState = this.gameStates.get(key);

    // If not found with userId, try without userId (fallback for unauthenticated users)
    if (!numberConnectState && userId) {
      const fallbackKey = `${roomId}:`;
      numberConnectState = this.gameStates.get(fallbackKey);
      logger.info(`Trying fallback key for NumberConnect state: ${fallbackKey}`);
    }

    // Check if game and state are valid
    if (!gameState || !numberConnectState) {
      logger.warn(`Complete path validation failed - missing state for room ${roomId}:`, {
        hasGameState: !!gameState,
        hasNumberConnectState: !!numberConnectState,
        gameStatus: gameState?.status,
        userId,
        key,
        availableKeys: Array.from(this.gameStates.keys())
      });
      return {
        success: false,
        isValid: false,
        newPath: [],
        currentNumber: 1,
        points: 0,
        newScore: gameState?.score || 0,
        newLives: gameState?.lives || 0,
        gameEnded: true,
        invalidReason: 'Game state not found'
      };
    }

    // Check if game is active (allow both 'active' and 'starting' states)
    if (gameState.status !== 'active' && gameState.status !== 'starting') {
      logger.warn(`Complete path validation ignored - game not active for room ${roomId}:`, {
        gameStatus: gameState.status,
        userId,
        key
      });
      return {
        success: false,
        isValid: false,
        newPath: [],
        currentNumber: 1,
        points: 0,
        newScore: gameState?.score || 0,
        newLives: gameState?.lives || 0,
        gameEnded: true,
        invalidReason: 'Game not active'
      };
    }

    // Validate the complete path
    const validationResult = this.validatePathSequence(path, numberConnectState);

    if (validationResult.isValid) {
      // Update game state with new path
      numberConnectState.playerPath = [...path];
      numberConnectState.currentNumber = validationResult.nextNumber;

      // Calculate score
      const points = this.calculatePathScore(path, numberConnectState);
      this.gameService.updateScore(roomId, points, 'add', userId);
      numberConnectState.totalScore += points;

      // Check for level completion (must cover all cells and numbers 1-5)
      const levelCompleted = this.checkGameCompletion(numberConnectState);

      if (levelCompleted) {
        // Per-board timing and time-based scoring (placeholder formula)
        const now = Date.now();
        const boardStart = numberConnectState.boardStartTime ?? now;
        const elapsedMs = Math.max(0, now - boardStart);
        numberConnectState.boardTimes.push(elapsedMs);

        const elapsedSec = Math.floor(elapsedMs / 1000);
        const timeBasedPoints = Math.max(0, 1000 - elapsedSec * 50);
        this.gameService.updateScore(roomId, timeBasedPoints, 'add', userId);
        numberConnectState.totalScore += timeBasedPoints;
        numberConnectState.levelsCompleted++;

        // Continue to next level (continuous boards) unless game ended (timer/lives)
        const gameEnded = this.checkGameEndCondition(gameState);

        if (gameEnded) {
          gameState.status = 'ended';
          return {
            success: true,
            isValid: true,
            newPath: [...path],
            currentNumber: numberConnectState.currentNumber,
            points: points + timeBasedPoints,
            newScore: gameState.score,
            newLives: gameState.lives,
            gameEnded: true,
            gameWon: false,
            levelCompleted: true,
            currentLevel: numberConnectState.currentLevel,
            levelsCompleted: numberConnectState.levelsCompleted,
            boardTimeMs: elapsedMs
          };
        } else {
          // Generate new level and continue
          this.generateNewLevel(roomId, userId, numberConnectState);
          numberConnectState.boardStartTime = Date.now();

          return {
            success: true,
            isValid: true,
            newPath: [...numberConnectState.playerPath], // Empty path for new level
            currentNumber: numberConnectState.currentNumber,
            points: points + timeBasedPoints,
            newScore: gameState.score,
            newLives: gameState.lives,
            gameEnded: false,
            gameWon: false,
            levelCompleted: true,
            newLevel: true,
            currentLevel: numberConnectState.currentLevel,
            levelsCompleted: numberConnectState.levelsCompleted,
            boardTimeMs: elapsedMs,
            newPuzzle: {
              grid: numberConnectState.grid,
              numberPositions: numberConnectState.numberPositions,
              solutionPath: numberConnectState.solutionPath,
              maxNumber: NUMBER_CONNECT_CONFIG.MAX_NUMBER
            }
          };
        }
      } else {
        // Level not completed yet, continue current level
        return {
          success: true,
          isValid: true,
          newPath: [...path],
          currentNumber: numberConnectState.currentNumber,
          points,
          newScore: gameState.score,
          newLives: gameState.lives,
          gameEnded: false,
          gameWon: false,
          levelCompleted: false,
          currentLevel: numberConnectState.currentLevel,
          levelsCompleted: numberConnectState.levelsCompleted
        };
      }
    } else {
      // Invalid path - deduct life via GameService (emits leaderboard/opponent updates)
      const livesResult = this.gameService.deductLife(roomId, userId);
      const gameEnded = livesResult.gameEnded;

      return {
        success: true,
        isValid: false,
        newPath: numberConnectState.playerPath,
        currentNumber: numberConnectState.currentNumber,
        points: 0,
        newScore: gameState.score,
        newLives: livesResult.newLives,
        gameEnded,
        gameWon: false,
        invalidReason: validationResult.reason
      };
    }
  }

  /**
   * Validate a path sequence for correctness
   */
  private validatePathSequence(path: Position[], state: NumberConnectGameState): { isValid: boolean; nextNumber: number; reason?: string } {
    if (path.length === 0) {
      return { isValid: false, nextNumber: 1, reason: 'Empty path' };
    }

    // Check if path is within grid bounds
    for (const cell of path) {
      if (cell.row < 0 || cell.row >= 5 || cell.col < 0 || cell.col >= 5) {
        return { isValid: false, nextNumber: state.currentNumber, reason: 'Path goes outside grid' };
      }
    }

    // Check path connectivity (each cell must be adjacent to the next)
    for (let i = 1; i < path.length; i++) {
      const prev = path[i - 1];
      const curr = path[i];
      const distance = Math.abs(prev.row - curr.row) + Math.abs(prev.col - curr.col);

      if (distance !== 1) {
        return { isValid: false, nextNumber: state.currentNumber, reason: 'Path not connected - cells must be adjacent' };
      }
    }

    // Check for path crossings (no cell can be visited twice)
    if (this.pathCrossesItself(path)) {
      return { isValid: false, nextNumber: state.currentNumber, reason: 'Path crosses itself - each cell can only be visited once' };
    }

    // Check that path starts with number 1
    const firstCell = path[0];
    const firstCellData = state.grid[firstCell.row][firstCell.col];
    if (!firstCellData.number || firstCellData.number !== 1) {
      return { isValid: false, nextNumber: state.currentNumber, reason: 'Path must start with number 1' };
    }

    // Check number sequence - must visit numbers 1, 2, 3, 4, 5 in order
    let expectedNumber = 1;
    let numbersFound = 0;

    for (const cell of path) {
      const cellData = state.grid[cell.row][cell.col];
      if (cellData.number) {
        if (cellData.number !== expectedNumber) {
          return { isValid: false, nextNumber: state.currentNumber, reason: `Numbers must be visited in order. Expected ${expectedNumber}, found ${cellData.number}` };
        }
        expectedNumber++;
        numbersFound++;
      }
    }

    // Check that all numbers 1-5 are included in the path
    if (numbersFound < NUMBER_CONNECT_CONFIG.MAX_NUMBER) {
      return { isValid: false, nextNumber: state.currentNumber, reason: `Path must include all numbers 1-5. Only found ${numbersFound} numbers` };
    }

    // If all numbers are connected but the path does not cover every cell, invalidate
    const totalCells = NUMBER_CONNECT_CONFIG.GRID_SIZE * NUMBER_CONNECT_CONFIG.GRID_SIZE;
    if (path.length < totalCells) {
      return { isValid: false, nextNumber: state.currentNumber, reason: 'empty spaces not allowed' };
    }

    return { isValid: true, nextNumber: expectedNumber };
  }

  /**
   * Calculate score for a path
   */
  private calculatePathScore(path: Position[], state: NumberConnectGameState): number {
    let score = 0;
    let numbersFound = 0;

    for (const cell of path) {
      const cellData = state.grid[cell.row][cell.col];
      if (cellData.number) {
        numbersFound++;
        score += cellData.number * 100; // 100 points per number
      }
    }

    // Bonus for path length efficiency
    const efficiency = Math.max(0, 50 - path.length);
    score += efficiency * 10;

    return score;
  }

  /**
   * Check if the path crosses itself
   */
  private pathCrossesItself(path: Position[]): boolean {
    const visited = new Set<string>();

    for (const cell of path) {
      const key = `${cell.row},${cell.col}`;
      if (visited.has(key)) {
        return true; // Path crosses itself
      }
      visited.add(key);
    }

    return false;
  }

  /**
   * Check if the current level is completed (all numbers connected in sequence)
   */



  /**
   * Award partial points for current board on overall timer timeout
   * Uses placeholder time-based scoring scaled by board progress
   */
  private awardPartialOnTimeout(roomId: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    const gameState = this.gameService.getGameState(roomId, userId);
    const numberConnectState = this.gameStates.get(key);

    if (!gameState || !numberConnectState) return;
    if (gameState.status === 'ended') return; // already ended

    const now = Date.now();
    const boardStart = numberConnectState.boardStartTime ?? now;
    const elapsedMs = Math.max(0, now - boardStart);
    const elapsedSec = Math.floor(elapsedMs / 1000);

    const timeBase = Math.max(0, 1000 - elapsedSec * 50);
    const numbersProgress = Math.max(0, numberConnectState.currentNumber - 1);
    const fraction = Math.min(1, numbersProgress / NUMBER_CONNECT_CONFIG.MAX_NUMBER);
    const partialPoints = Math.floor(timeBase * fraction);

    if (partialPoints > 0) {
      this.gameService.updateScore(roomId, partialPoints, 'add', userId);
      numberConnectState.totalScore += partialPoints;
      // Do not push to boardTimes to keep it for completed boards only
      this.gameStates.set(key, numberConnectState);
      logger.info(`Awarded partial timeout points in room ${roomId}: ${partialPoints} (fraction=${fraction.toFixed(2)}, elapsed=${elapsedSec}s)`);
    }
  }

   private checkGameEndCondition(gameState: any): boolean {
    // Game ends if no lives left
    if (gameState.lives <= 0) {
      return true;
    }

    // Game ends if timer expired (handled by GameService)
    if (gameState.status === 'ended') {
      return true;
    }

    return false;
  }

  /**
   * Generate a new level and update game state
   */
  private generateNewLevel(roomId: string, userId: string | undefined, state: NumberConnectGameState): void {
    // Generate a new puzzle using room/user-scoped PRNG
    const rngInt = (max: number) => sessionService.getPrngInt(roomId, max, userId);
    const levelGen = new NumberConnectLevelGenerator(
      NUMBER_CONNECT_CONFIG.GRID_SIZE,
      NUMBER_CONNECT_CONFIG.MAX_NUMBER,
      rngInt
    );
    const generatedLevel = levelGen.generateSolvablePuzzle();

    // Update state with new level (grid already has numbers populated)
    state.grid = generatedLevel.grid;
    state.solutionPath = generatedLevel.solutionPath;
    state.numberPositions = generatedLevel.numberPositions;
    state.playerPath = [];
    state.currentNumber = 1;
    state.moveCount = 0;
    state.currentLevel++;

    logger.info(`Generated new level ${state.currentLevel} for NumberConnect game with numbers:`, {
      numberPositions: generatedLevel.numberPositions,
      gridHasNumbers: this.validateGridHasNumbers(state.grid)
    });
  }

  /**
   * Emit fatal error to client
   */
  private emitFatalError(socket: Socket, roomId: string, message: string, errorType: string): void {
    socket.emit('game_fatal_error', {
      message,
      errorType,
      roomId,
      timestamp: Date.now()
    });
  }
}
