import { Server, Socket } from 'socket.io';
import { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';
import { GameState, GameAction, GameInitResult, GameStartData } from '../../types/game';
import {
  NumberSequenceGameState,
  NumberSequenceGameStartedData
} from '../../types/numberSequence';
import { submitGameScore, ScoreSubmissionData } from '../../utils/externalApi';

export default class NumberSequenceController {
  private gameService: GameService;
  private gameStates: Map<string, NumberSequenceGameState> = new Map();
  private socketMap: Map<string, Socket> = new Map();

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize a new Number Sequence game session (called at client load)
   */
  initializeGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    // Check if room already has a game state (per-user)
    let gameState = this.gameService.getGameState(roomId, userId);

    if (gameState) {
      // Game state exists, check if we can initialize
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      // If game ended, clean up and create new state to allow restart
      if (gameState.status === 'ended') {
        this.cleanup(roomId, userId);
        gameState = this.gameService.createGameState(roomId, GAME_TYPES.NUMBER_SEQUENCE, 3, userId);
      }
    } else {
      // Create new game state (per-user)
      gameState = this.gameService.createGameState(roomId, GAME_TYPES.NUMBER_SEQUENCE, 3, userId);
    }

    // Initialize Number Sequence specific game state
    const initialState: NumberSequenceGameState = {
      gameType: 'numbers',
      status: 'waiting',
      score: 0,
      scoreAry: [],
      lives: 3,
      currentSequence: this.generateSequenceForRound(1),
      currentIndex: 0,
      streak: 0,
      currentLevel: 1,
      isRoundActive: false,
      roundStartTime: 0,
      roundsCompleted: 0
    };

    const key = `${roomId}:${userId ?? ''}`;
    this.gameStates.set(key, initialState);
    this.socketMap.set(key, socket);

    logger.info(`Number Sequence game initialized for room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Start the game (called after countdown ends)
   */
  startGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    const gameState = this.gameService.getGameState(roomId, userId);
    const key = `${roomId}:${userId ?? ''}`;
    const numberSequenceState = this.gameStates.get(key);

    if (!gameState) {
      return { success: false, message: 'Game not initialized. Call initializeGame first.' };
    }

    if (!numberSequenceState) {
      return { success: false, message: 'Game data not found. Call initializeGame first.' };
    }

    if (gameState.status === 'active') {
      return { success: false, message: 'Game is already active' };
    }

    // If game ended, clean up and allow restart
    if (gameState.status === 'ended') {
      this.cleanup(roomId, userId);
      return { success: false, message: 'Game ended, please reinitialize' };
    }

    // Start the game using GameService
    const started = this.gameService.startGame(roomId, socket, userId);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Update Number Sequence state to active
    numberSequenceState.status = 'active';
    numberSequenceState.isRoundActive = true;
    numberSequenceState.roundStartTime = Date.now();

    logger.info(`Number Sequence game started for room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Initialize and start a new Number Sequence game session (legacy method for backward compatibility)
   * @deprecated Use initializeGame() followed by startGame() instead
   */
  private initializeAndStartGame(roomId: string, socket: Socket): GameInitResult {
    const userId = (socket as any).user?.userId;
    const initResult = this.initializeGame(roomId, socket, userId);
    if (!initResult.success) {
      return initResult;
    }

    return this.startGame(roomId, socket, userId);
  }

  /**
   * Generate a sequential [0..N-1] sequence for the given round
   */
  private generateSequenceForRound(round: number): number[] {
    const roundLengths: Record<number, number> = { 1: 5, 2: 7, 3: 9, 4: 12 };
    const length = roundLengths[round] ?? 5;
    return Array.from({ length }, (_, i) => i);
  }

  /**
   * Handle game initialization event (called at client load)
   */
  handleGameInit(socket: Socket, data: GameStartData): void {
    const { roomId, gameId, submitScoreId } = data;
    const userId = (socket as any).user?.userId;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId or gameId', 'initialization');
      return;
    }

    // Store submitScoreId in game session for later use
    if (submitScoreId) {
      console.log(`Number Sequence game session ${roomId} - submitScoreId: ${submitScoreId}`);
      // TODO: Store submitScoreId in game session data for score submission
    }

    try {
      // Initialize the game (but don't start it yet)
      const result = this.initializeGame(roomId, socket, userId);

      if (result.success && result.gameState) {
        // Get current sequence for the client
        const key = `${roomId}:${userId ?? ''}`;
        const gameState = this.gameStates.get(key);
        const currentSequence = gameState?.currentSequence || [];

        socket.emit('initialized', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          currentSequence,
          currentLevel: gameState?.currentLevel || 1,
          message: 'Game initialized!'
        });

        logger.info(`${gameId} game initialized in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to initialize game', 'initialization');
      }
    } catch (error) {
      logger.error(`Error initializing ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error', 'initialization');
    }
  }

  /**
   * Handle game start event (called after countdown)
   */
  private handleGameStart(socket: Socket, data: GameStartData): void {
    const { roomId, gameId } = data;
    const userId = (socket as any).user?.userId;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId or gameId', 'start');
      return;
    }

    try {
      // Start the game (game should already be initialized)
      const result = this.startGame(roomId, socket, userId);

      if (result.success && result.gameState) {
        // Get current sequence for the client
        const key = `${roomId}:${userId ?? ''}`;
        const gameState = this.gameStates.get(key);

        const gameStartedData: NumberSequenceGameStartedData = {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime || Date.now()
          },
          currentSequence: gameState?.currentSequence || [],
          currentLevel: gameState?.currentLevel || 1,
          message: 'Game started!'
        };

        socket.emit('started', gameStartedData);
        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to start game', 'start');
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error', 'start');
    }
  }

  /**
   * Emit a fatal error that should stop the game
   */
  private emitFatalError(socket: Socket, roomId: string, message: string, errorType: string): void {
    socket.emit('game_fatal_error', {
      message,
      errorType,
      roomId,
      timestamp: Date.now()
    });
  }

  /**
   * Handle number selection action (validated on server)
   */
  private async handleNumberSelectAction(socket: Socket, data: any): Promise<void> {
    const { roomId } = data;
    const { numberValue } = data.action?.data || {};

    if (!roomId) {
      socket.emit('error', { message: 'Missing roomId for number select action' });
      return;
    }

    const userId = (socket as any).user?.userId;

    try {
      const key = `${roomId}:${userId ?? ''}`;
      const numberSequenceState = this.gameStates.get(key);
      if (!numberSequenceState) {
        socket.emit('error', { message: 'No game state found for this room' });
        return;
      }

      // Validate expected number: must equal currentIndex (sequence is [0..N-1])
      const isCorrect = numberValue === numberSequenceState.currentIndex;

      if (isCorrect) {
        numberSequenceState.currentIndex++;
        // Update score via GameService to keep leaderboard consistent
        this.gameService.updateScore(roomId, numberSequenceState.roundsCompleted + 1, 'add', userId);

        let nextSequence: number[] | undefined;
        if (numberSequenceState.currentIndex >= numberSequenceState.currentSequence.length) {
          // Round complete
          numberSequenceState.roundsCompleted++;

          if (numberSequenceState.roundsCompleted >= 4) {
            // All rounds complete - end game
            const results = this.gameService.endGame(roomId, 'completed', userId);
            const svcStateFinal = this.gameService.getGameState(roomId, userId);
            await this.submitScore(roomId, svcStateFinal, 'completed');
            if (results) {
              socket.emit('ended', results);
            }
            return;
          } else {
            // Advance to next round
            numberSequenceState.currentLevel++;
            numberSequenceState.currentSequence = this.generateSequenceForRound(numberSequenceState.currentLevel);
            numberSequenceState.currentIndex = 0;
            nextSequence = numberSequenceState.currentSequence;
          }
        }

        const svcState = this.gameService.getGameState(roomId, userId);
        socket.emit('action_result', {
          actionType: 'number_select',
          data: {
            numberValue,
            isCorrect: true,
            newScore: svcState?.score ?? 0,
            newLives: svcState?.lives ?? 0,
            currentIndex: numberSequenceState.currentIndex,
            gameEnded: false,
            nextSequence,
            currentLevel: numberSequenceState.currentLevel
          }
        });
      } else {
        // Wrong selection - deduct a life via GameService
        const livesResult = this.gameService.deductLife(roomId, userId);
        const svcState = this.gameService.getGameState(roomId, userId);

        socket.emit('action_result', {
          actionType: 'number_select',
          data: {
            numberValue,
            isCorrect: false,
            newScore: svcState?.score ?? 0,
            newLives: livesResult.newLives,
            currentIndex: numberSequenceState.currentIndex,
            gameEnded: livesResult.gameEnded
          }
        });

        if (livesResult.gameEnded) {
          await this.submitScore(roomId, svcState, 'no_lives');
          return;
        }
      }

      this.gameStates.set(key, numberSequenceState);
    } catch (error) {
      logger.error(`Error processing number select action in room ${roomId}:`, error);
      socket.emit('error', { message: 'Internal server error' });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: any): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      switch (action.type) {
        case 'number_select':
          this.handleNumberSelectAction(socket, data);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Submit score to external API
   */
  private async submitScore(roomId: string, gameState: any, reason: string, submitScoreId?: string, authToken?: string): Promise<void> {
    try {
      const scoreData: ScoreSubmissionData = {
        roomId,
        score: gameState.score,
        scoreArray: gameState.scoreAry || [],
        submitScoreId,
        authToken
      };

      const result = await submitGameScore(scoreData);

      if (result.success) {
        logger.info(`Score submitted successfully for Number Sequence game in room ${roomId}`);
      } else {
        logger.warn(`Score submission failed for Number Sequence game in room ${roomId}: ${result.error}`);
      }
    } catch (error) {
      logger.error(`Error submitting score for Number Sequence game in room ${roomId}:`, error);
    }
  }

  /**
   * Handle game end
   */
  private async handleGameEnd(socket: Socket, data: any): Promise<void> {
    const { roomId } = data;

    if (!roomId) {
      socket.emit('error', {
        message: 'Missing roomId for game end'
      });
      return;
    }

    try {
      const userId = (socket as any).user?.userId;
      const key = `${roomId}:${userId ?? ''}`;
      const gameState = this.gameStates.get(key);
      const results = this.gameService.endGame(roomId, 'manual', userId);

      // Submit score to external API
      if (gameState) {
        await this.submitScore(roomId, gameState, 'manual');
      }

      if (results) {
        socket.emit('ended', results);
      }

      // Cleanup
      this.cleanup(roomId, userId);
    } catch (error) {
      logger.error(`Error ending game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Setup socket event handlers for Number Sequence
   */
  public setupSocketHandlers(socket: Socket): void {
    // Generic game init event
    socket.on('init', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameInit(socket, data);
      }
    });

    // Generic game start event
    socket.on('start', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameEnd(socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameAction(socket, data);
      }
    });
  }

  /**
   * Clean up game resources
   */
  public cleanup(roomId: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    this.gameStates.delete(key);
    this.socketMap.delete(key);

    // Delete game state from GameService to allow restarts
    this.gameService.deleteGameState(roomId, userId);
  }
}
