import"../chunks/DsnmJJEf.js";import{i as j}from"../chunks/C1M19Mmo.js";import{p as N,g as E,v as $,f as x,t as F,k as r,b,c as J,s as d,d as o,w as z,r as a,$ as W,x as A,y as G}from"../chunks/4UAai7vz.js";import{e as U,s as g,h as B}from"../chunks/DJNDnN69.js";import{b as O,i as P}from"../chunks/DMnCbMI3.js";import{b as q,s as H,r as L,e as K,i as Q,a as V,g as X}from"../chunks/BwME0dYm.js";import{g as Y}from"../chunks/D6Z45t_z.js";var Z=x('<div class="space-y-4 p-4 bg-white/10 rounded-lg backdrop-blur-sm"><div><label class="block text-sm font-medium text-white mb-2">Generated Room ID:</label> <div class="flex items-center gap-2"><input type="text" readonly="" class="flex-1 px-3 py-2 bg-black/20 border border-white/20 rounded text-white text-sm font-mono"/> <button class="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm transition-colors"> </button></div></div> <div><label class="block text-sm font-medium text-white mb-2">Or enter custom Room ID (optional):</label> <input type="text" placeholder="Leave empty to use generated ID" class="w-full px-3 py-2 bg-black/20 border border-white/20 rounded text-white placeholder-white/50"/></div> <p class="text-xs text-white/70"> </p></div>');function ee(M,T){N(T,!0);let c=z(""),m=z(""),f=z(!1);E(()=>{D()});function D(){const s=Date.now(),l=Math.random().toString(36).substring(2,11);$(c,`room-${s}-${l}`)}async function h(){try{await navigator.clipboard.writeText(r(c)),$(f,!0),setTimeout(()=>$(f,!1),2e3)}catch(s){console.error("Failed to copy:",s)}}function w(){return r(m).trim()||r(c)}var v=Z(),y=o(v),_=d(o(y),2),p=o(_);L(p);var t=d(p,2),e=o(t,!0);a(t),a(_),a(y);var n=d(y,2),i=d(o(n),2);L(i),a(n);var I=d(n,2),R=o(I,!0);return a(I),a(v),F(s=>{H(p,r(c)),g(e,r(f)?"Copied!":"Copy"),g(R,s)},[()=>r(m).trim()?`Using custom room: ${r(m).trim()}`:`Using generated room: ${r(c)}`]),U("click",t,h),q(i,()=>r(m),s=>$(m,s)),b(M,v),J({getRoomId:w})}var te=x('<meta name="description" content="Select from our collection of exciting mini-games"/>'),ae=x('<button><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white">Available</div></div> <h2 class="text-2xl font-bold text-white mb-2 group-hover:text-yellow-200 transition-colors"> </h2> <p class="text-white/80 text-sm leading-relaxed mb-4"> </p> <div class="flex items-center text-white/60 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg> Play Now</div></button>'),re=x('<div class="block h-full p-6 rounded-xl bg-gradient-to-br from-gray-600 to-gray-700 shadow-lg border border-white/10 opacity-60 cursor-not-allowed"><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-gray-500/50 rounded-full text-sm font-medium text-gray-300">Coming Soon</div></div> <h2 class="text-2xl font-bold text-gray-300 mb-2"> </h2> <p class="text-gray-400 text-sm leading-relaxed mb-4"> </p> <div class="flex items-center text-gray-500 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg> Not Available</div></div>'),oe=x('<div class="game-card group svelte-um5wpz"><!></div>'),ie=x('<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"><header class="text-center py-12 px-4"><h1 class="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">TicTaps Games</h1></header> <div class="container mx-auto px-4 mb-8"><div class="max-w-md mx-auto"><!></div></div> <main class="container mx-auto px-4 pb-12"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto"></div></main></div>');function ve(M,T){N(T,!1);const c=[{id:"finger-frenzy",name:"Finger Frenzy",description:"Fast-paced tapping game",color:"from-red-500 to-orange-500",available:!0},{id:"bingo",name:"Bingo",description:"Classic bingo game",color:"from-blue-500 to-purple-500",available:!0},{id:"matching-mayhem",name:"Matching Mayhem",description:"Memory matching game",color:"from-green-500 to-teal-500",available:!0},{id:"numbers",name:"Number Sequence",description:"Number pattern recognition",color:"from-purple-500 to-pink-500",available:!0},{id:"mums-numbers",name:"Mums Numbers!",description:"Draw a continuous path through numbers 1-5 covering all grid cells",color:"from-cyan-500 to-blue-500",available:!0}];let m=A();async function f(t){try{const n=await fetch("https://ws.games.tictaps.dev/api/generate-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({gameId:t,roomId:r(m)?.getRoomId(),clientSeed:"1234567890",username:`Player-${Math.floor(Math.random()*1e3)}`,email:`player${Math.floor(Math.random()*1e3)}@example.com`})});if(!n.ok)throw new Error(`Failed to generate token: ${n.statusText}`);const i=await n.json();if(!i.success||!i.token)throw new Error("Invalid response from token generation endpoint");return console.log("Generated JWT token data:",{gameId:i.userData.gameId,roomId:i.userData.roomId,userId:i.userData.userId,username:i.userData.username}),X.setRoomData(i.userData.roomId,i.token,i.submitScoreId),i.token}catch(e){throw console.error("Error generating JWT token:",e),e}}async function D(t){if(!c.find(e=>e.id===t)?.available){console.warn(`Game ${t} is not available`);return}try{const e=await f(t);console.log(`Generated JWT for ${t}:`,e),Y(`/game/${t}?token=${e}`)}catch(e){console.error(`Failed to start game ${t}:`,e),alert(`Failed to start game: ${e instanceof Error?e.message:"Unknown error"}`)}}j();var h=ie();B(t=>{var e=te();W.title="TicTaps Games",b(t,e)});var w=d(o(h),2),v=o(w),y=o(v);O(ee(y,{$$legacy:!0}),t=>$(m,t),()=>r(m)),a(v),a(w);var _=d(w,2),p=o(_);K(p,5,()=>c,Q,(t,e)=>{var n=oe(),i=o(n);{var I=s=>{var l=ae(),u=d(o(l),2),S=o(u,!0);a(u);var k=d(u,2),C=o(k,!0);a(k),G(2),a(l),F(()=>{V(l,1,`block h-full w-full p-6 rounded-xl bg-gradient-to-br ${r(e).color??""} shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/10 text-left cursor-pointer`,"svelte-um5wpz"),g(S,r(e).name),g(C,r(e).description)}),U("click",l,()=>D(r(e).id)),b(s,l)},R=s=>{var l=re(),u=d(o(l),2),S=o(u,!0);a(u);var k=d(u,2),C=o(k,!0);a(k),G(2),a(l),F(()=>{g(S,r(e).name),g(C,r(e).description)}),b(s,l)};P(i,s=>{r(e).available?s(I):s(R,!1)})}a(n),b(t,n)}),a(p),a(_),a(h),b(M,h),J()}export{ve as component};
