<script lang="ts">
  import { roomState, roomActions, RoomStatus } from "$lib/stores/roomState";
  import { <PERSON>List, JoinRoomForm, RoomControls, RoomInfo } from "./room";
  import { socketClient } from "$lib/socket";

  interface Props {
    handleStartClick: any;
    gameId: string;
  }
  let { handleStartClick, gameId }: Props = $props();

  let isStarting = $state(false);
  let gameTitle: HTMLElement;
  let showJoinForm = $state(false);
  let showMultiplayerOptions = $state(false);

  // Set the game ID in room state when component loads
  $effect(() => {
    roomActions.setGameId(gameId);
  });

  // Handle room state changes
  $effect(() => {
    if ($roomState.status === RoomStatus.GameActive) {
      // Game started by host, trigger the original start flow
      handleStartClick();
    }
  });

  function handleSoloPlay() {
    // Reset room state and start solo game
    roomActions.reset();
    handleStartClick();
  }

  function handleMultiplayerClick() {
    showMultiplayerOptions = true;
  }

  function handleJoinRoom() {
    showJoinForm = true;
  }

  function handleCreateRoom() {
    // For now, we'll use a simple room code generation
    // In a real implementation, this would call the server to create a room
    const roomCode = generateRoomCode();
    socketClient.joinRoomByCode(roomCode, gameId);
  }

  function handleCancelJoin() {
    showJoinForm = false;
  }

  function handleBackToMenu() {
    showMultiplayerOptions = false;
    showJoinForm = false;
    if ($roomState.status !== RoomStatus.NotInRoom) {
      roomActions.reset();
    }
  }

  function generateRoomCode(): string {
    // Generate a 6-character room code
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Determine which UI to show based on room state
  const showRoomInterface = $derived(
    $roomState.status === RoomStatus.InRoom ||
      $roomState.status === RoomStatus.Starting ||
      $roomState.status === RoomStatus.Joining
  );

  const showMainMenu = $derived(
    !showRoomInterface &&
      !showJoinForm &&
      !showMultiplayerOptions &&
      $roomState.status === RoomStatus.NotInRoom
  );
</script>

<div class="game-start-container">
  <!-- Background -->
  <div class="background"></div>

  <!-- Game Title -->
  <img
    bind:this={gameTitle}
    class="game-title pulse"
    src="/assets-{gameId}/images/game_name.png"
    alt="Game Title"
  />

  <!-- Main Menu (Solo/Multiplayer choice) -->
  {#if showMainMenu}
    <div class="menu-container">
      <button
        class="menu-button solo-button"
        onclick={handleSoloPlay}
        disabled={isStarting}
      >
        <div class="btn-border"></div>
        <div class="btn-background"></div>
        <span class="btn-text">PLAY SOLO</span>
      </button>

      <button
        class="menu-button multiplayer-button"
        onclick={handleMultiplayerClick}
        disabled={isStarting}
      >
        <div class="btn-border"></div>
        <div class="btn-background"></div>
        <span class="btn-text">MULTIPLAYER</span>
      </button>
    </div>
  {/if}

  <!-- Multiplayer Options -->
  {#if showMultiplayerOptions && !showJoinForm}
    <div class="multiplayer-options">
      <div class="options-header">
        <h3>Multiplayer Options</h3>
        <button class="back-button" onclick={handleBackToMenu}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
              clip-rule="evenodd"
            />
          </svg>
          Back
        </button>
      </div>

      <div class="options-buttons">
        <button class="option-button create-button" onclick={handleCreateRoom}>
          <div class="option-icon">🏠</div>
          <div class="option-text">
            <div class="option-title">Create Room</div>
            <div class="option-subtitle">Start a new multiplayer game</div>
          </div>
        </button>

        <button class="option-button join-button" onclick={handleJoinRoom}>
          <div class="option-icon">🚪</div>
          <div class="option-text">
            <div class="option-title">Join Room</div>
            <div class="option-subtitle">Enter a room code to join</div>
          </div>
        </button>
      </div>
    </div>
  {/if}

  <!-- Join Room Form -->
  {#if showJoinForm}
    <div class="join-form-container">
      <button class="back-button" onclick={handleCancelJoin}>
        <svg viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"
          />
        </svg>
        Back
      </button>
      <JoinRoomForm {gameId} onCancel={handleCancelJoin} />
    </div>
  {/if}

  <!-- Room Interface -->
  {#if showRoomInterface}
    <div class="room-interface">
      <div class="room-header">
        <RoomInfo roomState={$roomState} />
        <button class="back-button" onclick={handleBackToMenu}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
              clip-rule="evenodd"
            />
          </svg>
          Leave
        </button>
      </div>

      <div class="room-content">
        <div class="room-left">
          <PlayerList
            players={$roomState.players}
            currentUserId={$roomState.currentUserId}
            maxPlayers={$roomState.maxPlayers}
          />
        </div>

        <div class="room-right">
          <RoomControls roomState={$roomState} onLeaveRoom={handleBackToMenu} />
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .game-start-container {
    position: absolute;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    z-index: 100;
  }

  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("/assets/images/game_bg.png") no-repeat center center;
    background-size: cover;
    z-index: 0;
  }

  .game-title {
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 70vw;
    max-height: 20vh;
    width: auto;
    height: auto;
    object-fit: contain;
    z-index: 1;
  }

  .game-title.pulse {
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      transform: translate(-50%, -50%) scale(1.02);
    }
  }

  /* Menu Container */
  .menu-container {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 70%;
    max-width: 350px;
  }

  .menu-button {
    width: 100%;
    height: 80px;
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.1s ease;
    position: relative;
  }

  .menu-button:hover {
    transform: scale(1.05);
  }

  .menu-button:focus {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  .menu-button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* Multiplayer Options */
  .multiplayer-options {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.4);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 400px;
  }

  .options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .options-header h3 {
    color: white;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
  }

  .back-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .back-button svg {
    width: 16px;
    height: 16px;
  }

  .options-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .option-button {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    color: white;
  }

  .option-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  .option-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
  }

  .option-text {
    flex: 1;
  }

  .option-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .option-subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }

  /* Join Form Container */
  .join-form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
  }

  /* Room Interface */
  .room-interface {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 95%;
    max-width: 800px;
    height: 70%;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
  }

  .room-content {
    display: flex;
    gap: 20px;
    flex: 1;
    min-height: 0;
  }

  .room-left {
    flex: 1;
    min-width: 0;
  }

  .room-right {
    flex: 1;
    min-width: 0;
  }

  /* Button styles */
  .btn-border,
  .btn-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 12px;
  }

  .btn-border {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .btn-background {
    background: linear-gradient(135deg, #1e40af, #7c3aed);
    margin: 2px;
    border-radius: 10px;
  }

  .btn-text {
    position: relative;
    z-index: 1;
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  @keyframes gradient-shift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .game-title {
      max-width: 80vw;
      max-height: 15vh;
    }

    .menu-container {
      width: 80%;
    }

    .room-interface {
      width: 98%;
      height: 80%;
    }

    .room-content {
      flex-direction: column;
    }

    .multiplayer-options,
    .join-form-container {
      width: 95%;
    }
  }

  @media (max-height: 600px) {
    .game-title {
      top: 20%;
      max-height: 15vh;
    }

    .menu-container {
      top: 55%;
    }

    .room-interface {
      height: 85%;
    }
  }
</style>
