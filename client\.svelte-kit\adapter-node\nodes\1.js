

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.BGymq15E.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/C1M19Mmo.js","_app/immutable/chunks/4UAai7vz.js","_app/immutable/chunks/DJNDnN69.js","_app/immutable/chunks/mXOxeudE.js","_app/immutable/chunks/D6Z45t_z.js"];
export const stylesheets = [];
export const fonts = [];
