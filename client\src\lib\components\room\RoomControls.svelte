<script lang="ts">
  import { socketClient } from "$lib/socket";
  import { roomActions } from "$lib/stores/roomState";
  import type { RoomState } from "$lib/stores/roomState";

  interface Props {
    roomState: RoomState;
    onLeaveRoom?: () => void;
  }

  let { roomState, onLeaveRoom }: Props = $props();

  let isReady = $state(false);
  let showError = $state(false);
  let errorMessage = $state("");

  // Check if current user is ready
  $effect(() => {
    if (roomState.currentUserId) {
      const currentPlayer = roomState.players.find(
        (p) => p.userId === roomState.currentUserId
      );
      isReady = currentPlayer?.isReady || false;
    }
  });

  function handleStartGame() {
    if (!roomState.isHost) {
      showErrorMessage("Only the host can start the game");
      return;
    }

    if (!canStartGame) {
      showErrorMessage("All players must be ready before starting");
      return;
    }

    try {
      socketClient.startGameAsHost();
    } catch (error) {
      showErrorMessage("Failed to start game. Please try again.");
    }
  }

  function handleToggleReady() {
    try {
      socketClient.setPlayerReady(!isReady);
    } catch (error) {
      showErrorMessage("Failed to update ready status. Please try again.");
    }
  }

  function handleLeaveRoom() {
    try {
      socketClient.leaveRoom();
      if (onLeaveRoom) {
        onLeaveRoom();
      }
    } catch (error) {
      showErrorMessage("Failed to leave room. Please try again.");
    }
  }

  function showErrorMessage(message: string) {
    errorMessage = message;
    showError = true;
    // Auto-hide error after 3 seconds
    setTimeout(() => {
      showError = false;
    }, 3000);
  }

  function copyRoomCode() {
    if (roomState.roomCode) {
      navigator.clipboard
        .writeText(roomState.roomCode)
        .then(() => {
          // Could show a toast notification here
          console.log("Room code copied to clipboard");
        })
        .catch(() => {
          // Fallback for older browsers
          const textArea = document.createElement("textarea");
          textArea.value = roomState.roomCode || "";
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand("copy");
          document.body.removeChild(textArea);
        });
    }
  }

  // Check if all players are ready (for host to enable start button)
  const allPlayersReady = $derived(
    roomState.players.length > 1 &&
      roomState.players.filter((p) => p.isConnected).every((p) => p.isReady)
  );

  const canStartGame = $derived(
    roomState.isHost &&
      roomState.players.length > 1 &&
      allPlayersReady &&
      !roomState.isLoading
  );
</script>

<div class="room-controls">
  <!-- Room Info Section -->
  <div class="room-info-section">
    <div class="room-code-display">
      <div class="room-code-label">Room Code</div>
      <div class="room-code-value" onclick={copyRoomCode} title="Click to copy">
        {roomState.roomCode || roomState.roomId || "N/A"}
        <svg class="copy-icon" viewBox="0 0 20 20" fill="currentColor">
          <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
          <path
            d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"
          />
        </svg>
      </div>
    </div>

    <div class="game-info">
      <div class="game-name">Game: {roomState.gameId || "Unknown"}</div>
      <div class="player-count">
        {roomState.players.length}/{roomState.maxPlayers} Players
      </div>
    </div>
  </div>

  <!-- Control Buttons Section -->
  <div class="controls-section">
    {#if roomState.isHost}
      <!-- Host Controls -->
      <div class="host-controls">
        <div class="host-badge">
          <svg class="crown-icon" viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"
              clip-rule="evenodd"
            />
          </svg>
          You are the Host
        </div>

        <button
          type="button"
          onclick={handleStartGame}
          class="start-game-button"
          disabled={!canStartGame}
          title={!canStartGame
            ? "Waiting for all players to be ready"
            : "Start the game for all players"}
        >
          {#if roomState.isLoading}
            <div class="loading-spinner"></div>
            Starting...
          {:else if !allPlayersReady && roomState.players.length > 1}
            Waiting for Players
          {:else if roomState.players.length <= 1}
            Need More Players
          {:else}
            Start Game
          {/if}
        </button>
      </div>
    {:else}
      <!-- Player Controls -->
      <div class="player-controls">
        <button
          type="button"
          onclick={handleToggleReady}
          class="ready-button"
          class:ready={isReady}
          disabled={roomState.isLoading}
        >
          {#if isReady}
            <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
            Ready
          {:else}
            <svg class="clock-icon" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                clip-rule="evenodd"
              />
            </svg>
            Not Ready
          {/if}
        </button>

        <div class="waiting-message">
          {#if roomState.isHost}
            Waiting for you to start the game...
          {:else}
            Waiting for host to start the game...
          {/if}
        </div>
      </div>
    {/if}

    <!-- Leave Room Button -->
    <button
      type="button"
      onclick={handleLeaveRoom}
      class="leave-room-button"
      disabled={roomState.isLoading}
    >
      <svg class="exit-icon" viewBox="0 0 20 20" fill="currentColor">
        <path
          fill-rule="evenodd"
          d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"
          clip-rule="evenodd"
        />
      </svg>
      Leave Room
    </button>
  </div>

  <!-- Error Display -->
  {#if showError}
    <div class="error-display">
      <div class="error-icon">⚠️</div>
      <div class="error-text">{errorMessage}</div>
      <button class="error-close" onclick={() => (showError = false)}>×</button>
    </div>
  {/if}
</div>

<style>
  .room-controls {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .room-info-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .room-code-display {
    text-align: center;
  }

  .room-code-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .room-code-value {
    font-size: 24px;
    font-weight: 700;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    letter-spacing: 2px;
  }

  .room-code-value:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .copy-icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
  }

  .game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
  }

  .controls-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .host-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .host-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
  }

  .crown-icon {
    width: 16px;
    height: 16px;
  }

  .start-game-button {
    padding: 14px 24px;
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .start-game-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
  }

  .start-game-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: rgba(255, 255, 255, 0.2);
  }

  .player-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .ready-button {
    padding: 12px 24px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
  }

  .ready-button.ready {
    background: linear-gradient(135deg, #10b981, #059669);
    border-color: #10b981;
  }

  .ready-button:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .ready-button.ready:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
  }

  .check-icon,
  .clock-icon {
    width: 16px;
    height: 16px;
  }

  .waiting-message {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    font-style: italic;
  }

  .leave-room-button {
    padding: 10px 20px;
    border-radius: 8px;
    border: 1px solid rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .leave-room-button:hover:not(:disabled) {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.7);
  }

  .exit-icon {
    width: 16px;
    height: 16px;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Error Display */
  .error-display {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideIn 0.3s ease-out;
  }

  .error-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .error-text {
    color: #ef4444;
    font-size: 12px;
    font-weight: 500;
    flex: 1;
  }

  .error-close {
    background: none;
    border: none;
    color: #ef4444;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
  }

  .error-close:hover {
    background: rgba(239, 68, 68, 0.2);
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
