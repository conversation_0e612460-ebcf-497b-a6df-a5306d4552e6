<script lang="ts">
  import { RoomStatus, type RoomState } from '$lib/stores/roomState';
  
  interface Props {
    roomState: RoomState;
  }
  
  let { roomState }: Props = $props();
  
  function getStatusMessage(status: RoomStatus): string {
    switch (status) {
      case RoomStatus.NotInRoom:
        return 'Not in a room';
      case RoomStatus.Joining:
        return 'Joining room...';
      case RoomStatus.InRoom:
        return 'In room - waiting for game to start';
      case RoomStatus.Starting:
        return 'Starting game...';
      case RoomStatus.GameActive:
        return 'Game in progress';
      case RoomStatus.Leaving:
        return 'Leaving room...';
      case RoomStatus.Error:
        return 'Room error occurred';
      default:
        return 'Unknown status';
    }
  }
  
  function getStatusColor(status: RoomStatus): string {
    switch (status) {
      case RoomStatus.NotInRoom:
        return 'text-gray-400';
      case RoomStatus.Joining:
      case RoomStatus.Starting:
      case RoomStatus.Leaving:
        return 'text-yellow-400';
      case RoomStatus.InRoom:
        return 'text-blue-400';
      case RoomStatus.GameActive:
        return 'text-green-400';
      case RoomStatus.Error:
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  }
  
  function getStatusIcon(status: RoomStatus): string {
    switch (status) {
      case RoomStatus.NotInRoom:
        return '🏠';
      case RoomStatus.Joining:
        return '🚪';
      case RoomStatus.InRoom:
        return '👥';
      case RoomStatus.Starting:
        return '🎮';
      case RoomStatus.GameActive:
        return '🎯';
      case RoomStatus.Leaving:
        return '🚶';
      case RoomStatus.Error:
        return '⚠️';
      default:
        return '❓';
    }
  }
  
  const connectedPlayersCount = $derived(
    roomState.players.filter(p => p.isConnected).length
  );
  
  const readyPlayersCount = $derived(
    roomState.players.filter(p => p.isConnected && p.isReady).length
  );
</script>

<div class="room-info">
  <div class="status-section">
    <div class="status-indicator">
      <span class="status-icon">{getStatusIcon(roomState.status)}</span>
      <div class="status-details">
        <div class="status-text {getStatusColor(roomState.status)}">
          {getStatusMessage(roomState.status)}
        </div>
        {#if roomState.loadingMessage}
          <div class="loading-message">
            {roomState.loadingMessage}
          </div>
        {/if}
      </div>
    </div>
    
    {#if roomState.isLoading}
      <div class="loading-spinner"></div>
    {/if}
  </div>
  
  {#if roomState.status === RoomStatus.InRoom || roomState.status === RoomStatus.Starting}
    <div class="room-details">
      <div class="detail-item">
        <span class="detail-label">Room:</span>
        <span class="detail-value">{roomState.roomCode || roomState.roomId}</span>
      </div>
      
      <div class="detail-item">
        <span class="detail-label">Game:</span>
        <span class="detail-value">{roomState.gameId}</span>
      </div>
      
      <div class="detail-item">
        <span class="detail-label">Players:</span>
        <span class="detail-value">
          {connectedPlayersCount}/{roomState.maxPlayers}
          {#if roomState.players.length > 1}
            ({readyPlayersCount} ready)
          {/if}
        </span>
      </div>
      
      {#if roomState.isHost}
        <div class="detail-item">
          <span class="detail-label">Role:</span>
          <span class="detail-value host-role">Host</span>
        </div>
      {/if}
    </div>
  {/if}
  
  {#if roomState.error}
    <div class="error-section">
      <div class="error-icon">⚠️</div>
      <div class="error-message">{roomState.error}</div>
    </div>
  {/if}
  
  {#if !roomState.isConnected && roomState.status !== RoomStatus.NotInRoom}
    <div class="connection-warning">
      <div class="warning-icon">📡</div>
      <div class="warning-message">Connection lost - attempting to reconnect...</div>
    </div>
  {/if}
</div>

<style>
  .room-info {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 16px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .status-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .status-icon {
    font-size: 20px;
  }
  
  .status-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .status-text {
    font-size: 14px;
    font-weight: 600;
  }
  
  .loading-message {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
  }
  
  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .room-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }
  
  .detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
  }
  
  .detail-value {
    color: white;
    font-weight: 600;
  }
  
  .host-role {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .error-section {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
  }
  
  .error-icon {
    font-size: 16px;
  }
  
  .error-message {
    color: #ef4444;
    font-size: 12px;
    font-weight: 500;
    flex: 1;
  }
  
  .connection-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 6px;
  }
  
  .warning-icon {
    font-size: 16px;
  }
  
  .warning-message {
    color: #f59e0b;
    font-size: 12px;
    font-weight: 500;
    flex: 1;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Mobile responsiveness */
  @media (max-width: 480px) {
    .room-info {
      padding: 12px;
    }
    
    .detail-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }
    
    .status-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
</style>
