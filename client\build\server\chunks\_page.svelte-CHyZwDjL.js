import { v as push, U as ensure_array_like, V as head, W as attr_class, T as escape_html, X as stringify, x as pop, Y as attr, Z as bind_props } from './exports-CtUTVNuO.js';
import './state.svelte-Bbvw5NZb.js';
import './MumsNumbers.svelte_svelte_type_style_lang-Bs_cgvkx.js';
import 'pixi.js';
import './shared-server-i79vVjEm.js';
import 'socket.io-client';

function RoomIdManager($$payload, $$props) {
  push();
  let randomRoomId = "";
  let inputRoomId = "";
  function getRoomId() {
    return inputRoomId.trim() || randomRoomId;
  }
  $$payload.out.push(`<div class="space-y-4 p-4 bg-white/10 rounded-lg backdrop-blur-sm"><div><label class="block text-sm font-medium text-white mb-2">Generated Room ID:</label> <div class="flex items-center gap-2"><input type="text"${attr("value", randomRoomId)} readonly class="flex-1 px-3 py-2 bg-black/20 border border-white/20 rounded text-white text-sm font-mono"/> <button class="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm transition-colors">${escape_html("Copy")}</button></div></div> <div><label class="block text-sm font-medium text-white mb-2">Or enter custom Room ID (optional):</label> <input type="text"${attr("value", inputRoomId)} placeholder="Leave empty to use generated ID" class="w-full px-3 py-2 bg-black/20 border border-white/20 rounded text-white placeholder-white/50"/></div> <p class="text-xs text-white/70">${escape_html(inputRoomId.trim() ? `Using custom room: ${inputRoomId.trim()}` : `Using generated room: ${randomRoomId}`)}</p></div>`);
  bind_props($$props, { getRoomId });
  pop();
}
function _page($$payload, $$props) {
  push();
  const games = [
    {
      id: "finger-frenzy",
      name: "Finger Frenzy",
      description: "Fast-paced tapping game",
      color: "from-red-500 to-orange-500",
      available: true
    },
    {
      id: "bingo",
      name: "Bingo",
      description: "Classic bingo game",
      color: "from-blue-500 to-purple-500",
      available: true
    },
    {
      id: "matching-mayhem",
      name: "Matching Mayhem",
      description: "Memory matching game",
      color: "from-green-500 to-teal-500",
      available: true
    },
    {
      id: "numbers",
      name: "Number Sequence",
      description: "Number pattern recognition",
      color: "from-purple-500 to-pink-500",
      available: true
    },
    {
      id: "mums-numbers",
      name: "Mums Numbers!",
      description: "Draw a continuous path through numbers 1-5 covering all grid cells",
      color: "from-cyan-500 to-blue-500",
      available: true
    }
  ];
  const each_array = ensure_array_like(games);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>TicTaps Games</title>`;
    $$payload2.out.push(`<meta name="description" content="Select from our collection of exciting mini-games"/>`);
  });
  $$payload.out.push(`<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"><header class="text-center py-12 px-4"><h1 class="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">TicTaps Games</h1></header> <div class="container mx-auto px-4 mb-8"><div class="max-w-md mx-auto">`);
  RoomIdManager($$payload, {});
  $$payload.out.push(`<!----></div></div> <main class="container mx-auto px-4 pb-12"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let game = each_array[$$index];
    $$payload.out.push(`<div class="game-card group svelte-um5wpz">`);
    if (game.available) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<button${attr_class(`block h-full w-full p-6 rounded-xl bg-gradient-to-br ${stringify(game.color)} shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/10 text-left cursor-pointer`, "svelte-um5wpz")}><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white">Available</div></div> <h2 class="text-2xl font-bold text-white mb-2 group-hover:text-yellow-200 transition-colors">${escape_html(game.name)}</h2> <p class="text-white/80 text-sm leading-relaxed mb-4">${escape_html(game.description)}</p> <div class="flex items-center text-white/60 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg> Play Now</div></button>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div class="block h-full p-6 rounded-xl bg-gradient-to-br from-gray-600 to-gray-700 shadow-lg border border-white/10 opacity-60 cursor-not-allowed"><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-gray-500/50 rounded-full text-sm font-medium text-gray-300">Coming Soon</div></div> <h2 class="text-2xl font-bold text-gray-300 mb-2">${escape_html(game.name)}</h2> <p class="text-gray-400 text-sm leading-relaxed mb-4">${escape_html(game.description)}</p> <div class="flex items-center text-gray-500 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg> Not Available</div></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--></div></main></div>`);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CHyZwDjL.js.map
