<script lang="ts">
  import { goto, invalidate } from "$app/navigation";

  interface Props {
    show?: boolean;
    finalScore?: number;
  }

  let { show = false, finalScore = 0 }: Props = $props();

  function handleBackToLobby() {
    // window.location.reload();
    window.location.href = "/";

    // invalidate("/");
    // goto("/", { replaceState: true });
  }
</script>

{#if show}
  <!-- End Game Overlay with game background -->
  <div class="game-end-overlay">
    <!-- Background with blur effect -->
    <div class="game-background"></div>

    <!-- Main panel with blur and transparency -->
    <div class="game-panel">
      <!-- Blur background for panel -->
      <div class="panel-blur-bg"></div>

      <!-- Main panel content -->
      <div class="panel-content">
        <!-- Game Over Title with glow effect -->
        <div class="game-over-title">
          <h1 class="game-over-text">GAME OVER</h1>
        </div>

        <!-- Score Section -->
        <div class="score-section">
          <div class="score-label">SCORE</div>
          <div class="score-value">{finalScore}</div>
        </div>

        <!-- Back to Lobby Button -->
        <button class="back-to-lobby-btn" onclick={handleBackToLobby}>
          <div class="btn-border"></div>
          <div class="btn-background"></div>
          <span class="btn-text">BACK TO LOBBY</span>
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .game-end-overlay {
    position: fixed;
    inset: 0;
    z-index: 3000;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); */
  }

  .game-background {
    position: absolute;
    inset: 0;
    background: black;
    opacity: 0.5;
  }

  .game-panel {
    position: relative;
    width: 80%;
    max-width: 500px;
    height: 60%;
    max-height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .panel-blur-bg {
    position: absolute;
    inset: -2px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .panel-content {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(26, 35, 49, 0.4);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 2rem;
    box-sizing: border-box;
  }

  .game-over-title {
    flex-shrink: 0;
    margin-bottom: 1rem;
  }

  .game-over-text {
    font-family: Arial, sans-serif;
    font-size: clamp(2rem, 8vw, 4rem);
    font-weight: bold;
    margin: 0;
    background: linear-gradient(90deg, #4bffae 0%, #32c4ff 50%, #5c67ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(75, 255, 174, 0.5);
    filter: drop-shadow(0 0 15px rgba(75, 255, 174, 0.3));
  }

  .score-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .score-label {
    font-family: Arial, sans-serif;
    font-size: clamp(1.2rem, 4vw, 1.8rem);
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 0.5rem;
  }

  .score-value {
    font-family: Arial, sans-serif;
    font-size: clamp(3rem, 12vw, 5.5rem);
    font-weight: bold;
    background: linear-gradient(135deg, #4cffae 0%, #32c4ff 40%, #5c67ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* text-shadow: 0 0 10px rgba(255, 255, 255, 0.9); */
    /* filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5)); */
    -webkit-text-stroke: 2px rgba(255, 255, 255, 0.3);
  }

  .back-to-lobby-btn {
    position: relative;
    width: 70%;
    max-width: 350px;
    height: 80px;
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.1s ease;
    flex-shrink: 0;
  }

  .back-to-lobby-btn:hover {
    transform: scale(1.05);
  }

  .back-to-lobby-btn:active {
    transform: scale(0.95);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .game-panel {
      width: 90%;
      height: 70%;
    }

    .panel-content {
      padding: 1.5rem;
    }

    .back-to-lobby-btn {
      width: 85%;
      height: 70px;
    }
  }

  @media (max-width: 480px) {
    .panel-content {
      padding: 1rem;
    }

    .score-section {
      gap: 0.5rem;
    }
  }
</style>
