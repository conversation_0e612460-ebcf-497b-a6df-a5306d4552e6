import{D as at,u as W,d as Ft,aa as ei,y as L,k as Z,ab as Ge,M as H,H as D,ac as ni,w as ht,R as N,G as wn,n as Se,E as Y,ad as ii,P as ft,B as kt,f as Pn,ae as ri,af as si,I as $,ag as Ot,a1 as oi}from"./BwME0dYm.js";const te=Object.create(null),Fe=Object.create(null);function we(i,t){let e=Fe[i];return e===void 0&&(te[t]===void 0&&(te[t]=1),Fe[i]=e=te[t]++),e}let Wt;function vn(){return(!Wt||Wt?.isContextLost())&&(Wt=at.get().createCanvas().getContext("webgl",{})),Wt}let $t;function ai(){if(!$t){$t="mediump";const i=vn();i&&i.getShaderPrecisionFormat&&($t=i.getShaderPrecisionFormat(i.FRAGMENT_SHADER,i.HIGH_FLOAT).precision?"highp":"mediump")}return $t}function li(i,t,e){return t?i:e?(i=i.replace("out vec4 finalColor;",""),`

        #ifdef GL_ES // This checks if it is WebGL1
        #define in varying
        #define finalColor gl_FragColor
        #define texture texture2D
        #endif
        ${i}
        `):`

        #ifdef GL_ES // This checks if it is WebGL1
        #define in attribute
        #define out varying
        #endif
        ${i}
        `}function hi(i,t,e){const n=e?t.maxSupportedFragmentPrecision:t.maxSupportedVertexPrecision;if(i.substring(0,9)!=="precision"){let r=e?t.requestedFragmentPrecision:t.requestedVertexPrecision;return r==="highp"&&n!=="highp"&&(r="mediump"),`precision ${r} float;
${i}`}else if(n!=="highp"&&i.substring(0,15)==="precision highp")return i.replace("precision highp","precision mediump");return i}function ci(i,t){return t?`#version 300 es
${i}`:i}const ui={},di={};function fi(i,{name:t="pixi-program"},e=!0){t=t.replace(/\s+/g,"-"),t+=e?"-fragment":"-vertex";const n=e?ui:di;return n[t]?(n[t]++,t+=`-${n[t]}`):n[t]=1,i.indexOf("#define SHADER_NAME")!==-1?i:`${`#define SHADER_NAME ${t}`}
${i}`}function pi(i,t){return t?i.replace("#version 300 es",""):i}const ee={stripVersion:pi,ensurePrecision:hi,addProgramDefines:li,setProgramName:fi,insertVersion:ci},bt=Object.create(null),Mn=class ue{constructor(t){t={...ue.defaultOptions,...t};const e=t.fragment.indexOf("#version 300 es")!==-1,n={stripVersion:e,ensurePrecision:{requestedFragmentPrecision:t.preferredFragmentPrecision,requestedVertexPrecision:t.preferredVertexPrecision,maxSupportedVertexPrecision:"highp",maxSupportedFragmentPrecision:ai()},setProgramName:{name:t.name},addProgramDefines:e,insertVersion:e};let r=t.fragment,s=t.vertex;Object.keys(ee).forEach(o=>{const a=n[o];r=ee[o](r,a,!0),s=ee[o](s,a,!1)}),this.fragment=r,this.vertex=s,this.transformFeedbackVaryings=t.transformFeedbackVaryings,this._key=we(`${this.vertex}:${this.fragment}`,"gl-program")}destroy(){this.fragment=null,this.vertex=null,this._attributeData=null,this._uniformData=null,this._uniformBlockData=null,this.transformFeedbackVaryings=null,bt[this._cacheKey]=null}static from(t){const e=`${t.vertex}:${t.fragment}`;return bt[e]||(bt[e]=new ue(t),bt[e]._cacheKey=e),bt[e]}};Mn.defaultOptions={preferredVertexPrecision:"highp",preferredFragmentPrecision:"mediump"};let Cn=Mn;const De={uint8x2:{size:2,stride:2,normalised:!1},uint8x4:{size:4,stride:4,normalised:!1},sint8x2:{size:2,stride:2,normalised:!1},sint8x4:{size:4,stride:4,normalised:!1},unorm8x2:{size:2,stride:2,normalised:!0},unorm8x4:{size:4,stride:4,normalised:!0},snorm8x2:{size:2,stride:2,normalised:!0},snorm8x4:{size:4,stride:4,normalised:!0},uint16x2:{size:2,stride:4,normalised:!1},uint16x4:{size:4,stride:8,normalised:!1},sint16x2:{size:2,stride:4,normalised:!1},sint16x4:{size:4,stride:8,normalised:!1},unorm16x2:{size:2,stride:4,normalised:!0},unorm16x4:{size:4,stride:8,normalised:!0},snorm16x2:{size:2,stride:4,normalised:!0},snorm16x4:{size:4,stride:8,normalised:!0},float16x2:{size:2,stride:4,normalised:!1},float16x4:{size:4,stride:8,normalised:!1},float32:{size:1,stride:4,normalised:!1},float32x2:{size:2,stride:8,normalised:!1},float32x3:{size:3,stride:12,normalised:!1},float32x4:{size:4,stride:16,normalised:!1},uint32:{size:1,stride:4,normalised:!1},uint32x2:{size:2,stride:8,normalised:!1},uint32x3:{size:3,stride:12,normalised:!1},uint32x4:{size:4,stride:16,normalised:!1},sint32:{size:1,stride:4,normalised:!1},sint32x2:{size:2,stride:8,normalised:!1},sint32x3:{size:3,stride:12,normalised:!1},sint32x4:{size:4,stride:16,normalised:!1}};function xi(i){return De[i]??De.float32}const gi={f32:"float32","vec2<f32>":"float32x2","vec3<f32>":"float32x3","vec4<f32>":"float32x4",vec2f:"float32x2",vec3f:"float32x3",vec4f:"float32x4",i32:"sint32","vec2<i32>":"sint32x2","vec3<i32>":"sint32x3","vec4<i32>":"sint32x4",u32:"uint32","vec2<u32>":"uint32x2","vec3<u32>":"uint32x3","vec4<u32>":"uint32x4",bool:"uint32","vec2<bool>":"uint32x2","vec3<bool>":"uint32x3","vec4<bool>":"uint32x4"};function mi({source:i,entryPoint:t}){const e={},n=i.indexOf(`fn ${t}`);if(n!==-1){const r=i.indexOf("->",n);if(r!==-1){const s=i.substring(n,r),o=/@location\((\d+)\)\s+([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>]+)(?:,|\s|$)/g;let a;for(;(a=o.exec(s))!==null;){const l=gi[a[3]]??"float32";e[a[2]]={location:parseInt(a[1],10),format:l,stride:xi(l).stride,offset:0,instance:!1,start:0}}}}return e}function ne(i){const t=/(^|[^/])@(group|binding)\(\d+\)[^;]+;/g,e=/@group\((\d+)\)/,n=/@binding\((\d+)\)/,r=/var(<[^>]+>)? (\w+)/,s=/:\s*(\w+)/,o=/struct\s+(\w+)\s*{([^}]+)}/g,a=/(\w+)\s*:\s*([\w\<\>]+)/g,l=/struct\s+(\w+)/,c=i.match(t)?.map(u=>({group:parseInt(u.match(e)[1],10),binding:parseInt(u.match(n)[1],10),name:u.match(r)[2],isUniform:u.match(r)[1]==="<uniform>",type:u.match(s)[1]}));if(!c)return{groups:[],structs:[]};const h=i.match(o)?.map(u=>{const f=u.match(l)[1],d=u.match(a).reduce((p,g)=>{const[x,_]=g.split(":");return p[x.trim()]=_.trim(),p},{});return d?{name:f,members:d}:null}).filter(({name:u})=>c.some(f=>f.type===u))??[];return{groups:c,structs:h}}var Mt=(i=>(i[i.VERTEX=1]="VERTEX",i[i.FRAGMENT=2]="FRAGMENT",i[i.COMPUTE=4]="COMPUTE",i))(Mt||{});function yi({groups:i}){const t=[];for(let e=0;e<i.length;e++){const n=i[e];t[n.group]||(t[n.group]=[]),n.isUniform?t[n.group].push({binding:n.binding,visibility:Mt.VERTEX|Mt.FRAGMENT,buffer:{type:"uniform"}}):n.type==="sampler"?t[n.group].push({binding:n.binding,visibility:Mt.FRAGMENT,sampler:{type:"filtering"}}):n.type==="texture_2d"&&t[n.group].push({binding:n.binding,visibility:Mt.FRAGMENT,texture:{sampleType:"float",viewDimension:"2d",multisampled:!1}})}return t}function _i({groups:i}){const t=[];for(let e=0;e<i.length;e++){const n=i[e];t[n.group]||(t[n.group]={}),t[n.group][n.name]=n.binding}return t}function bi(i,t){const e=new Set,n=new Set,r=[...i.structs,...t.structs].filter(o=>e.has(o.name)?!1:(e.add(o.name),!0)),s=[...i.groups,...t.groups].filter(o=>{const a=`${o.name}-${o.binding}`;return n.has(a)?!1:(n.add(a),!0)});return{structs:r,groups:s}}const St=Object.create(null);class Zt{constructor(t){this._layoutKey=0,this._attributeLocationsKey=0;const{fragment:e,vertex:n,layout:r,gpuLayout:s,name:o}=t;if(this.name=o,this.fragment=e,this.vertex=n,e.source===n.source){const a=ne(e.source);this.structsAndGroups=a}else{const a=ne(n.source),l=ne(e.source);this.structsAndGroups=bi(a,l)}this.layout=r??_i(this.structsAndGroups),this.gpuLayout=s??yi(this.structsAndGroups),this.autoAssignGlobalUniforms=this.layout[0]?.globalUniforms!==void 0,this.autoAssignLocalUniforms=this.layout[1]?.localUniforms!==void 0,this._generateProgramKey()}_generateProgramKey(){const{vertex:t,fragment:e}=this,n=t.source+e.source+t.entryPoint+e.entryPoint;this._layoutKey=we(n,"program")}get attributeData(){return this._attributeData??(this._attributeData=mi(this.vertex)),this._attributeData}destroy(){this.gpuLayout=null,this.layout=null,this.structsAndGroups=null,this.fragment=null,this.vertex=null,St[this._cacheKey]=null}static from(t){const e=`${t.vertex.source}:${t.fragment.source}:${t.fragment.entryPoint}:${t.vertex.entryPoint}`;return St[e]||(St[e]=new Zt(t),St[e]._cacheKey=e),St[e]}}const kn=["f32","i32","vec2<f32>","vec3<f32>","vec4<f32>","mat2x2<f32>","mat3x3<f32>","mat4x4<f32>","mat3x2<f32>","mat4x2<f32>","mat2x3<f32>","mat4x3<f32>","mat2x4<f32>","mat3x4<f32>","vec2<i32>","vec3<i32>","vec4<i32>"],Si=kn.reduce((i,t)=>(i[t]=!0,i),{});function wi(i,t){switch(i){case"f32":return 0;case"vec2<f32>":return new Float32Array(2*t);case"vec3<f32>":return new Float32Array(3*t);case"vec4<f32>":return new Float32Array(4*t);case"mat2x2<f32>":return new Float32Array([1,0,0,1]);case"mat3x3<f32>":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4x4<f32>":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const Tn=class An{constructor(t,e){this._touched=0,this.uid=W("uniform"),this._resourceType="uniformGroup",this._resourceId=W("resource"),this.isUniformGroup=!0,this._dirtyId=0,this.destroyed=!1,e={...An.defaultOptions,...e},this.uniformStructures=t;const n={};for(const r in t){const s=t[r];if(s.name=r,s.size=s.size??1,!Si[s.type]){const o=s.type.match(/^array<(\w+(?:<\w+>)?),\s*(\d+)>$/);if(o){const[,a,l]=o;throw new Error(`Uniform type ${s.type} is not supported. Use type: '${a}', size: ${l} instead.`)}throw new Error(`Uniform type ${s.type} is not supported. Supported uniform types are: ${kn.join(", ")}`)}s.value??(s.value=wi(s.type,s.size)),n[r]=s.value}this.uniforms=n,this._dirtyId=1,this.ubo=e.ubo,this.isStatic=e.isStatic,this._signature=we(Object.keys(n).map(r=>`${r}-${t[r].type}`).join("-"),"uniform-group")}update(){this._dirtyId++}};Tn.defaultOptions={ubo:!1,isStatic:!1};let In=Tn;class qt{constructor(t){this.resources=Object.create(null),this._dirty=!0;let e=0;for(const n in t){const r=t[n];this.setResource(r,e++)}this._updateKey()}_updateKey(){if(!this._dirty)return;this._dirty=!1;const t=[];let e=0;for(const n in this.resources)t[e++]=this.resources[n]._resourceId;this._key=t.join("|")}setResource(t,e){const n=this.resources[e];t!==n&&(n&&t.off?.("change",this.onResourceChange,this),t.on?.("change",this.onResourceChange,this),this.resources[e]=t,this._dirty=!0)}getResource(t){return this.resources[t]}_touch(t){const e=this.resources;for(const n in e)e[n]._touched=t}destroy(){const t=this.resources;for(const e in t)t[e]?.off?.("change",this.onResourceChange,this);this.resources=null}onResourceChange(t){if(this._dirty=!0,t.destroyed){const e=this.resources;for(const n in e)e[n]===t&&(e[n]=null)}else this._updateKey()}}var de=(i=>(i[i.WEBGL=1]="WEBGL",i[i.WEBGPU=2]="WEBGPU",i[i.BOTH=3]="BOTH",i))(de||{});class Pe extends Ft{constructor(t){super(),this.uid=W("shader"),this._uniformBindMap=Object.create(null),this._ownedBindGroups=[];let{gpuProgram:e,glProgram:n,groups:r,resources:s,compatibleRenderers:o,groupMap:a}=t;this.gpuProgram=e,this.glProgram=n,o===void 0&&(o=0,e&&(o|=de.WEBGPU),n&&(o|=de.WEBGL)),this.compatibleRenderers=o;const l={};if(!s&&!r&&(s={}),s&&r)throw new Error("[Shader] Cannot have both resources and groups");if(!e&&r&&!a)throw new Error("[Shader] No group map or WebGPU shader provided - consider using resources instead.");if(!e&&r&&a)for(const c in a)for(const h in a[c]){const u=a[c][h];l[u]={group:c,binding:h,name:u}}else if(e&&r&&!a){const c=e.structsAndGroups.groups;a={},c.forEach(h=>{a[h.group]=a[h.group]||{},a[h.group][h.binding]=h.name,l[h.name]=h})}else if(s){r={},a={},e&&e.structsAndGroups.groups.forEach(u=>{a[u.group]=a[u.group]||{},a[u.group][u.binding]=u.name,l[u.name]=u});let c=0;for(const h in s)l[h]||(r[99]||(r[99]=new qt,this._ownedBindGroups.push(r[99])),l[h]={group:99,binding:c,name:h},a[99]=a[99]||{},a[99][c]=h,c++);for(const h in s){const u=h;let f=s[h];!f.source&&!f._resourceType&&(f=new In(f));const d=l[u];d&&(r[d.group]||(r[d.group]=new qt,this._ownedBindGroups.push(r[d.group])),r[d.group].setResource(f,d.binding))}}this.groups=r,this._uniformBindMap=a,this.resources=this._buildResourceAccessor(r,l)}addResource(t,e,n){var r,s;(r=this._uniformBindMap)[e]||(r[e]={}),(s=this._uniformBindMap[e])[n]||(s[n]=t),this.groups[e]||(this.groups[e]=new qt,this._ownedBindGroups.push(this.groups[e]))}_buildResourceAccessor(t,e){const n={};for(const r in e){const s=e[r];Object.defineProperty(n,s.name,{get(){return t[s.group].getResource(s.binding)},set(o){t[s.group].setResource(o,s.binding)}})}return n}destroy(t=!1){this.emit("destroy",this),t&&(this.gpuProgram?.destroy(),this.glProgram?.destroy()),this.gpuProgram=null,this.glProgram=null,this.removeAllListeners(),this._uniformBindMap=null,this._ownedBindGroups.forEach(e=>{e.destroy()}),this._ownedBindGroups=null,this.resources=null,this.groups=null}static from(t){const{gpu:e,gl:n,...r}=t;let s,o;return e&&(s=Zt.from(e)),n&&(o=Cn.from(n)),new Pe({gpuProgram:s,glProgram:o,...r})}}function Le(i,t,e=2){const n=t&&t.length,r=n?t[0]*e:i.length;let s=Bn(i,0,r,e,!0);const o=[];if(!s||s.next===s.prev)return o;let a,l,c;if(n&&(s=ki(i,t,s,e)),i.length>80*e){a=i[0],l=i[1];let h=a,u=l;for(let f=e;f<r;f+=e){const d=i[f],p=i[f+1];d<a&&(a=d),p<l&&(l=p),d>h&&(h=d),p>u&&(u=p)}c=Math.max(h-a,u-l),c=c!==0?32767/c:0}return Bt(s,o,e,a,l,c,0),o}function Bn(i,t,e,n,r){let s;if(r===Li(i,t,e,n)>0)for(let o=t;o<e;o+=n)s=Ve(o/n|0,i[o],i[o+1],s);else for(let o=e-n;o>=t;o-=n)s=Ve(o/n|0,i[o],i[o+1],s);return s&&pt(s,s.next)&&(zt(s),s=s.next),s}function lt(i,t){if(!i)return i;t||(t=i);let e=i,n;do if(n=!1,!e.steiner&&(pt(e,e.next)||G(e.prev,e,e.next)===0)){if(zt(e),e=t=e.prev,e===e.next)break;n=!0}else e=e.next;while(n||e!==t);return t}function Bt(i,t,e,n,r,s,o){if(!i)return;!o&&s&&Ri(i,n,r,s);let a=i;for(;i.prev!==i.next;){const l=i.prev,c=i.next;if(s?vi(i,n,r,s):Pi(i)){t.push(l.i,i.i,c.i),zt(i),i=c.next,a=c.next;continue}if(i=c,i===a){o?o===1?(i=Mi(lt(i),t),Bt(i,t,e,n,r,s,2)):o===2&&Ci(i,t,e,n,r,s):Bt(lt(i),t,e,n,r,s,1);break}}}function Pi(i){const t=i.prev,e=i,n=i.next;if(G(t,e,n)>=0)return!1;const r=t.x,s=e.x,o=n.x,a=t.y,l=e.y,c=n.y,h=Math.min(r,s,o),u=Math.min(a,l,c),f=Math.max(r,s,o),d=Math.max(a,l,c);let p=n.next;for(;p!==t;){if(p.x>=h&&p.x<=f&&p.y>=u&&p.y<=d&&Ct(r,a,s,l,o,c,p.x,p.y)&&G(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function vi(i,t,e,n){const r=i.prev,s=i,o=i.next;if(G(r,s,o)>=0)return!1;const a=r.x,l=s.x,c=o.x,h=r.y,u=s.y,f=o.y,d=Math.min(a,l,c),p=Math.min(h,u,f),g=Math.max(a,l,c),x=Math.max(h,u,f),_=fe(d,p,t,e,n),m=fe(g,x,t,e,n);let b=i.prevZ,y=i.nextZ;for(;b&&b.z>=_&&y&&y.z<=m;){if(b.x>=d&&b.x<=g&&b.y>=p&&b.y<=x&&b!==r&&b!==o&&Ct(a,h,l,u,c,f,b.x,b.y)&&G(b.prev,b,b.next)>=0||(b=b.prevZ,y.x>=d&&y.x<=g&&y.y>=p&&y.y<=x&&y!==r&&y!==o&&Ct(a,h,l,u,c,f,y.x,y.y)&&G(y.prev,y,y.next)>=0))return!1;y=y.nextZ}for(;b&&b.z>=_;){if(b.x>=d&&b.x<=g&&b.y>=p&&b.y<=x&&b!==r&&b!==o&&Ct(a,h,l,u,c,f,b.x,b.y)&&G(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;y&&y.z<=m;){if(y.x>=d&&y.x<=g&&y.y>=p&&y.y<=x&&y!==r&&y!==o&&Ct(a,h,l,u,c,f,y.x,y.y)&&G(y.prev,y,y.next)>=0)return!1;y=y.nextZ}return!0}function Mi(i,t){let e=i;do{const n=e.prev,r=e.next.next;!pt(n,r)&&zn(n,e,e.next,r)&&Rt(n,r)&&Rt(r,n)&&(t.push(n.i,e.i,r.i),zt(e),zt(e.next),e=i=r),e=e.next}while(e!==i);return lt(e)}function Ci(i,t,e,n,r,s){let o=i;do{let a=o.next.next;for(;a!==o.prev;){if(o.i!==a.i&&Gi(o,a)){let l=En(o,a);o=lt(o,o.next),l=lt(l,l.next),Bt(o,t,e,n,r,s,0),Bt(l,t,e,n,r,s,0);return}a=a.next}o=o.next}while(o!==i)}function ki(i,t,e,n){const r=[];for(let s=0,o=t.length;s<o;s++){const a=t[s]*n,l=s<o-1?t[s+1]*n:i.length,c=Bn(i,a,l,n,!1);c===c.next&&(c.steiner=!0),r.push(Ei(c))}r.sort(Ti);for(let s=0;s<r.length;s++)e=Ai(r[s],e);return e}function Ti(i,t){let e=i.x-t.x;if(e===0&&(e=i.y-t.y,e===0)){const n=(i.next.y-i.y)/(i.next.x-i.x),r=(t.next.y-t.y)/(t.next.x-t.x);e=n-r}return e}function Ai(i,t){const e=Ii(i,t);if(!e)return t;const n=En(e,i);return lt(n,n.next),lt(e,e.next)}function Ii(i,t){let e=t;const n=i.x,r=i.y;let s=-1/0,o;if(pt(i,e))return e;do{if(pt(i,e.next))return e.next;if(r<=e.y&&r>=e.next.y&&e.next.y!==e.y){const u=e.x+(r-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(u<=n&&u>s&&(s=u,o=e.x<e.next.x?e:e.next,u===n))return o}e=e.next}while(e!==t);if(!o)return null;const a=o,l=o.x,c=o.y;let h=1/0;e=o;do{if(n>=e.x&&e.x>=l&&n!==e.x&&Rn(r<c?n:s,r,l,c,r<c?s:n,r,e.x,e.y)){const u=Math.abs(r-e.y)/(n-e.x);Rt(e,i)&&(u<h||u===h&&(e.x>o.x||e.x===o.x&&Bi(o,e)))&&(o=e,h=u)}e=e.next}while(e!==a);return o}function Bi(i,t){return G(i.prev,i,t.prev)<0&&G(t.next,i,i.next)<0}function Ri(i,t,e,n){let r=i;do r.z===0&&(r.z=fe(r.x,r.y,t,e,n)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==i);r.prevZ.nextZ=null,r.prevZ=null,zi(r)}function zi(i){let t,e=1;do{let n=i,r;i=null;let s=null;for(t=0;n;){t++;let o=n,a=0;for(let c=0;c<e&&(a++,o=o.nextZ,!!o);c++);let l=e;for(;a>0||l>0&&o;)a!==0&&(l===0||!o||n.z<=o.z)?(r=n,n=n.nextZ,a--):(r=o,o=o.nextZ,l--),s?s.nextZ=r:i=r,r.prevZ=s,s=r;n=o}s.nextZ=null,e*=2}while(t>1);return i}function fe(i,t,e,n,r){return i=(i-e)*r|0,t=(t-n)*r|0,i=(i|i<<8)&16711935,i=(i|i<<4)&252645135,i=(i|i<<2)&858993459,i=(i|i<<1)&1431655765,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,i|t<<1}function Ei(i){let t=i,e=i;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==i);return e}function Rn(i,t,e,n,r,s,o,a){return(r-o)*(t-a)>=(i-o)*(s-a)&&(i-o)*(n-a)>=(e-o)*(t-a)&&(e-o)*(s-a)>=(r-o)*(n-a)}function Ct(i,t,e,n,r,s,o,a){return!(i===o&&t===a)&&Rn(i,t,e,n,r,s,o,a)}function Gi(i,t){return i.next.i!==t.i&&i.prev.i!==t.i&&!Fi(i,t)&&(Rt(i,t)&&Rt(t,i)&&Di(i,t)&&(G(i.prev,i,t.prev)||G(i,t.prev,t))||pt(i,t)&&G(i.prev,i,i.next)>0&&G(t.prev,t,t.next)>0)}function G(i,t,e){return(t.y-i.y)*(e.x-t.x)-(t.x-i.x)*(e.y-t.y)}function pt(i,t){return i.x===t.x&&i.y===t.y}function zn(i,t,e,n){const r=Ut(G(i,t,e)),s=Ut(G(i,t,n)),o=Ut(G(e,n,i)),a=Ut(G(e,n,t));return!!(r!==s&&o!==a||r===0&&Ht(i,e,t)||s===0&&Ht(i,n,t)||o===0&&Ht(e,i,n)||a===0&&Ht(e,t,n))}function Ht(i,t,e){return t.x<=Math.max(i.x,e.x)&&t.x>=Math.min(i.x,e.x)&&t.y<=Math.max(i.y,e.y)&&t.y>=Math.min(i.y,e.y)}function Ut(i){return i>0?1:i<0?-1:0}function Fi(i,t){let e=i;do{if(e.i!==i.i&&e.next.i!==i.i&&e.i!==t.i&&e.next.i!==t.i&&zn(e,e.next,i,t))return!0;e=e.next}while(e!==i);return!1}function Rt(i,t){return G(i.prev,i,i.next)<0?G(i,t,i.next)>=0&&G(i,i.prev,t)>=0:G(i,t,i.prev)<0||G(i,i.next,t)<0}function Di(i,t){let e=i,n=!1;const r=(i.x+t.x)/2,s=(i.y+t.y)/2;do e.y>s!=e.next.y>s&&e.next.y!==e.y&&r<(e.next.x-e.x)*(s-e.y)/(e.next.y-e.y)+e.x&&(n=!n),e=e.next;while(e!==i);return n}function En(i,t){const e=pe(i.i,i.x,i.y),n=pe(t.i,t.x,t.y),r=i.next,s=t.prev;return i.next=t,t.prev=i,e.next=r,r.prev=e,n.next=e,e.prev=n,s.next=n,n.prev=s,n}function Ve(i,t,e,n){const r=pe(i,t,e);return n?(r.next=n.next,r.prev=n,n.next.prev=r,n.next=r):(r.prev=r,r.next=r),r}function zt(i){i.next.prev=i.prev,i.prev.next=i.next,i.prevZ&&(i.prevZ.nextZ=i.nextZ),i.nextZ&&(i.nextZ.prevZ=i.prevZ)}function pe(i,t,e){return{i,x:t,y:e,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function Li(i,t,e,n){let r=0;for(let s=t,o=e-n;s<e;s+=n)r+=(i[o]-i[s])*(i[s+1]+i[o+1]),o=s;return r}const Vi=Le.default||Le;/**
 * tiny-lru
 *
 * @copyright 2025 Jason Mulligan <<EMAIL>>
 * @license BSD-3-Clause
 * @version 11.4.5
 */class Wi{constructor(t=0,e=0,n=!1){this.first=null,this.items=Object.create(null),this.last=null,this.max=t,this.resetTtl=n,this.size=0,this.ttl=e}clear(){return this.first=null,this.items=Object.create(null),this.last=null,this.size=0,this}delete(t){if(this.has(t)){const e=this.items[t];delete this.items[t],this.size--,e.prev!==null&&(e.prev.next=e.next),e.next!==null&&(e.next.prev=e.prev),this.first===e&&(this.first=e.next),this.last===e&&(this.last=e.prev)}return this}entries(t=this.keys()){return t.map(e=>[e,this.get(e)])}evict(t=!1){if(t||this.size>0){const e=this.first;delete this.items[e.key],--this.size===0?(this.first=null,this.last=null):(this.first=e.next,this.first.prev=null)}return this}expiresAt(t){let e;return this.has(t)&&(e=this.items[t].expiry),e}get(t){const e=this.items[t];if(e!==void 0){if(this.ttl>0&&e.expiry<=Date.now()){this.delete(t);return}return this.moveToEnd(e),e.value}}has(t){return t in this.items}moveToEnd(t){this.last!==t&&(t.prev!==null&&(t.prev.next=t.next),t.next!==null&&(t.next.prev=t.prev),this.first===t&&(this.first=t.next),t.prev=this.last,t.next=null,this.last!==null&&(this.last.next=t),this.last=t,this.first===null&&(this.first=t))}keys(){const t=[];let e=this.first;for(;e!==null;)t.push(e.key),e=e.next;return t}setWithEvicted(t,e,n=this.resetTtl){let r=null;if(this.has(t))this.set(t,e,!0,n);else{this.max>0&&this.size===this.max&&(r={...this.first},this.evict(!0));let s=this.items[t]={expiry:this.ttl>0?Date.now()+this.ttl:this.ttl,key:t,prev:this.last,next:null,value:e};++this.size===1?this.first=s:this.last.next=s,this.last=s}return r}set(t,e,n=!1,r=this.resetTtl){let s=this.items[t];return n||s!==void 0?(s.value=e,n===!1&&r&&(s.expiry=this.ttl>0?Date.now()+this.ttl:this.ttl),this.moveToEnd(s)):(this.max>0&&this.size===this.max&&this.evict(!0),s=this.items[t]={expiry:this.ttl>0?Date.now()+this.ttl:this.ttl,key:t,prev:this.last,next:null,value:e},++this.size===1?this.first=s:this.last.next=s,this.last=s),this}values(t=this.keys()){return t.map(e=>this.get(e))}}function $i(i=1e3,t=0,e=!1){if(isNaN(i)||i<0)throw new TypeError("Invalid max value");if(isNaN(t)||t<0)throw new TypeError("Invalid ttl value");if(typeof e!="boolean")throw new TypeError("Invalid resetTtl value");return new Wi(i,t,e)}const Hi=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function Gn(i){const t=typeof i.fontSize=="number"?`${i.fontSize}px`:i.fontSize;let e=i.fontFamily;Array.isArray(i.fontFamily)||(e=i.fontFamily.split(","));for(let n=e.length-1;n>=0;n--){let r=e[n].trim();!/([\"\'])[^\'\"]+\1/.test(r)&&!Hi.includes(r)&&(r=`"${r}"`),e[n]=r}return`${i.fontStyle} ${i.fontVariant} ${i.fontWeight} ${t} ${e.join(",")}`}const ie={willReadFrequently:!0},j=class C{static get experimentalLetterSpacingSupported(){let t=C._experimentalLetterSpacingSupported;if(t===void 0){const e=at.get().getCanvasRenderingContext2D().prototype;t=C._experimentalLetterSpacingSupported="letterSpacing"in e||"textLetterSpacing"in e}return t}constructor(t,e,n,r,s,o,a,l,c){this.text=t,this.style=e,this.width=n,this.height=r,this.lines=s,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=l,this.fontProperties=c}static measureText(t=" ",e,n=C._canvas,r=e.wordWrap){const s=`${t}-${e.styleKey}-wordWrap-${r}`;if(C._measurementCache.has(s))return C._measurementCache.get(s);const o=Gn(e),a=C.measureFont(o);a.fontSize===0&&(a.fontSize=e.fontSize,a.ascent=e.fontSize);const l=C.__context;l.font=o;const h=(r?C._wordWrap(t,e,n):t).split(/(?:\r\n|\r|\n)/),u=new Array(h.length);let f=0;for(let m=0;m<h.length;m++){const b=C._measureText(h[m],e.letterSpacing,l);u[m]=b,f=Math.max(f,b)}const d=e._stroke?.width||0;let p=f+d;e.dropShadow&&(p+=e.dropShadow.distance);const g=e.lineHeight||a.fontSize;let x=Math.max(g,a.fontSize+d)+(h.length-1)*(g+e.leading);e.dropShadow&&(x+=e.dropShadow.distance);const _=new C(t,e,p,x,h,u,g+e.leading,f,a);return C._measurementCache.set(s,_),_}static _measureText(t,e,n){let r=!1;C.experimentalLetterSpacingSupported&&(C.experimentalLetterSpacing?(n.letterSpacing=`${e}px`,n.textLetterSpacing=`${e}px`,r=!0):(n.letterSpacing="0px",n.textLetterSpacing="0px"));const s=n.measureText(t);let o=s.width;const a=-s.actualBoundingBoxLeft;let c=s.actualBoundingBoxRight-a;if(o>0)if(r)o-=e,c-=e;else{const h=(C.graphemeSegmenter(t).length-1)*e;o+=h,c+=h}return Math.max(o,c)}static _wordWrap(t,e,n=C._canvas){const r=n.getContext("2d",ie);let s=0,o="",a="";const l=Object.create(null),{letterSpacing:c,whiteSpace:h}=e,u=C._collapseSpaces(h),f=C._collapseNewlines(h);let d=!u;const p=e.wordWrapWidth+c,g=C._tokenize(t);for(let x=0;x<g.length;x++){let _=g[x];if(C._isNewline(_)){if(!f){a+=C._addLine(o),d=!u,o="",s=0;continue}_=" "}if(u){const b=C.isBreakingSpace(_),y=C.isBreakingSpace(o[o.length-1]);if(b&&y)continue}const m=C._getFromCache(_,c,l,r);if(m>p)if(o!==""&&(a+=C._addLine(o),o="",s=0),C.canBreakWords(_,e.breakWords)){const b=C.wordWrapSplit(_);for(let y=0;y<b.length;y++){let P=b[y],w=P,S=1;for(;b[y+S];){const k=b[y+S];if(!C.canBreakChars(w,k,_,y,e.breakWords))P+=k;else break;w=k,S++}y+=S-1;const I=C._getFromCache(P,c,l,r);I+s>p&&(a+=C._addLine(o),d=!1,o="",s=0),o+=P,s+=I}}else{o.length>0&&(a+=C._addLine(o),o="",s=0);const b=x===g.length-1;a+=C._addLine(_,!b),d=!1,o="",s=0}else m+s>p&&(d=!1,a+=C._addLine(o),o="",s=0),(o.length>0||!C.isBreakingSpace(_)||d)&&(o+=_,s+=m)}return a+=C._addLine(o,!1),a}static _addLine(t,e=!0){return t=C._trimRight(t),t=e?`${t}
`:t,t}static _getFromCache(t,e,n,r){let s=n[t];return typeof s!="number"&&(s=C._measureText(t,e,r)+e,n[t]=s),s}static _collapseSpaces(t){return t==="normal"||t==="pre-line"}static _collapseNewlines(t){return t==="normal"}static _trimRight(t){if(typeof t!="string")return"";for(let e=t.length-1;e>=0;e--){const n=t[e];if(!C.isBreakingSpace(n))break;t=t.slice(0,-1)}return t}static _isNewline(t){return typeof t!="string"?!1:C._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:C._breakingSpaces.includes(t.charCodeAt(0))}static _tokenize(t){const e=[];let n="";if(typeof t!="string")return e;for(let r=0;r<t.length;r++){const s=t[r],o=t[r+1];if(C.isBreakingSpace(s,o)||C._isNewline(s)){n!==""&&(e.push(n),n=""),s==="\r"&&o===`
`?(e.push(`\r
`),r++):e.push(s);continue}n+=s}return n!==""&&e.push(n),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,n,r,s){return!0}static wordWrapSplit(t){return C.graphemeSegmenter(t)}static measureFont(t){if(C._fonts[t])return C._fonts[t];const e=C._context;e.font=t;const n=e.measureText(C.METRICS_STRING+C.BASELINE_SYMBOL),r={ascent:n.actualBoundingBoxAscent,descent:n.actualBoundingBoxDescent,fontSize:n.actualBoundingBoxAscent+n.actualBoundingBoxDescent};return C._fonts[t]=r,r}static clearMetrics(t=""){t?delete C._fonts[t]:C._fonts={}}static get _canvas(){if(!C.__canvas){let t;try{const e=new OffscreenCanvas(0,0);if(e.getContext("2d",ie)?.measureText)return C.__canvas=e,e;t=at.get().createCanvas()}catch{t=at.get().createCanvas()}t.width=t.height=10,C.__canvas=t}return C.__canvas}static get _context(){return C.__context||(C.__context=C._canvas.getContext("2d",ie)),C.__context}};j.METRICS_STRING="|ÉqÅ";j.BASELINE_SYMBOL="M";j.BASELINE_MULTIPLIER=1.4;j.HEIGHT_MULTIPLIER=2;j.graphemeSegmenter=(()=>{if(typeof Intl?.Segmenter=="function"){const i=new Intl.Segmenter;return t=>{const e=i.segment(t),n=[];let r=0;for(const s of e)n[r++]=s.segment;return n}}return i=>[...i]})();j.experimentalLetterSpacing=!1;j._fonts={};j._newlines=[10,13];j._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];j._measurementCache=$i(1e3);let wt=j;const We=[{offset:0,color:"white"},{offset:1,color:"black"}],ve=class xe{constructor(...t){this.uid=W("fillGradient"),this._tick=0,this.type="linear",this.colorStops=[];let e=Ui(t);e={...e.type==="radial"?xe.defaultRadialOptions:xe.defaultLinearOptions,...ei(e)},this._textureSize=e.textureSize,this._wrapMode=e.wrapMode,e.type==="radial"?(this.center=e.center,this.outerCenter=e.outerCenter??this.center,this.innerRadius=e.innerRadius,this.outerRadius=e.outerRadius,this.scale=e.scale,this.rotation=e.rotation):(this.start=e.start,this.end=e.end),this.textureSpace=e.textureSpace,this.type=e.type,e.colorStops.forEach(r=>{this.addColorStop(r.offset,r.color)})}addColorStop(t,e){return this.colorStops.push({offset:t,color:L.shared.setValue(e).toHexa()}),this}buildLinearGradient(){if(this.texture)return;let{x:t,y:e}=this.start,{x:n,y:r}=this.end,s=n-t,o=r-e;const a=s<0||o<0;if(this._wrapMode==="clamp-to-edge"){if(s<0){const x=t;t=n,n=x,s*=-1}if(o<0){const x=e;e=r,r=x,o*=-1}}const l=this.colorStops.length?this.colorStops:We,c=this._textureSize,{canvas:h,context:u}=He(c,1),f=a?u.createLinearGradient(this._textureSize,0,0,0):u.createLinearGradient(0,0,this._textureSize,0);$e(f,l),u.fillStyle=f,u.fillRect(0,0,c,1),this.texture=new Z({source:new Ge({resource:h,addressMode:this._wrapMode})});const d=Math.sqrt(s*s+o*o),p=Math.atan2(o,s),g=new H;g.scale(d/c,1),g.rotate(p),g.translate(t,e),this.textureSpace==="local"&&g.scale(c,c),this.transform=g}buildGradient(){this.texture||this._tick++,this.type==="linear"?this.buildLinearGradient():this.buildRadialGradient()}buildRadialGradient(){if(this.texture)return;const t=this.colorStops.length?this.colorStops:We,e=this._textureSize,{canvas:n,context:r}=He(e,e),{x:s,y:o}=this.center,{x:a,y:l}=this.outerCenter,c=this.innerRadius,h=this.outerRadius,u=a-h,f=l-h,d=e/(h*2),p=(s-u)*d,g=(o-f)*d,x=r.createRadialGradient(p,g,c*d,(a-u)*d,(l-f)*d,h*d);$e(x,t),r.fillStyle=t[t.length-1].color,r.fillRect(0,0,e,e),r.fillStyle=x,r.translate(p,g),r.rotate(this.rotation),r.scale(1,this.scale),r.translate(-p,-g),r.fillRect(0,0,e,e),this.texture=new Z({source:new Ge({resource:n,addressMode:this._wrapMode})});const _=new H;_.scale(1/d,1/d),_.translate(u,f),this.textureSpace==="local"&&_.scale(e,e),this.transform=_}destroy(){this.texture?.destroy(!0),this.texture=null,this.transform=null,this.colorStops=[],this.start=null,this.end=null,this.center=null,this.outerCenter=null}get styleKey(){return`fill-gradient-${this.uid}-${this._tick}`}};ve.defaultLinearOptions={start:{x:0,y:0},end:{x:0,y:1},colorStops:[],textureSpace:"local",type:"linear",textureSize:256,wrapMode:"clamp-to-edge"};ve.defaultRadialOptions={center:{x:.5,y:.5},innerRadius:0,outerRadius:.5,colorStops:[],scale:1,textureSpace:"local",type:"radial",textureSize:256,wrapMode:"clamp-to-edge"};let K=ve;function $e(i,t){for(let e=0;e<t.length;e++){const n=t[e];i.addColorStop(n.offset,n.color)}}function He(i,t){const e=at.get().createCanvas(i,t),n=e.getContext("2d");return{canvas:e,context:n}}function Ui(i){let t=i[0]??{};return(typeof t=="number"||i[1])&&(D("8.5.2","use options object instead"),t={type:"linear",start:{x:i[0],y:i[1]},end:{x:i[2],y:i[3]},textureSpace:i[4],textureSize:i[5]??K.defaultLinearOptions.textureSize}),t}const Ue={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class Kt{constructor(t,e){this.uid=W("fillPattern"),this._tick=0,this.transform=new H,this.texture=t,this.transform.scale(1/t.frame.width,1/t.frame.height),e&&(t.source.style.addressModeU=Ue[e].addressModeU,t.source.style.addressModeV=Ue[e].addressModeV)}setTransform(t){const e=this.texture;this.transform.copyFrom(t),this.transform.invert(),this.transform.scale(1/e.frame.width,1/e.frame.height),this._tick++}get texture(){return this._texture}set texture(t){this._texture!==t&&(this._texture=t,this._tick++)}get styleKey(){return`fill-pattern-${this.uid}-${this._tick}`}destroy(){this.texture.destroy(!0),this.texture=null}}var re,Ne;function Ni(){if(Ne)return re;Ne=1,re=e;var i={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},t=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function e(s){var o=[];return s.replace(t,function(a,l,c){var h=l.toLowerCase();for(c=r(c),h=="m"&&c.length>2&&(o.push([l].concat(c.splice(0,2))),h="l",l=l=="m"?"l":"L");;){if(c.length==i[h])return c.unshift(l),o.push(c);if(c.length<i[h])throw new Error("malformed path data");o.push([l].concat(c.splice(0,i[h])))}}),o}var n=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function r(s){var o=s.match(n);return o?o.map(Number):[]}return re}var ji=Ni();const qi=ni(ji);function Oi(i,t){const e=qi(i),n=[];let r=null,s=0,o=0;for(let a=0;a<e.length;a++){const l=e[a],c=l[0],h=l;switch(c){case"M":s=h[1],o=h[2],t.moveTo(s,o);break;case"m":s+=h[1],o+=h[2],t.moveTo(s,o);break;case"H":s=h[1],t.lineTo(s,o);break;case"h":s+=h[1],t.lineTo(s,o);break;case"V":o=h[1],t.lineTo(s,o);break;case"v":o+=h[1],t.lineTo(s,o);break;case"L":s=h[1],o=h[2],t.lineTo(s,o);break;case"l":s+=h[1],o+=h[2],t.lineTo(s,o);break;case"C":s=h[5],o=h[6],t.bezierCurveTo(h[1],h[2],h[3],h[4],s,o);break;case"c":t.bezierCurveTo(s+h[1],o+h[2],s+h[3],o+h[4],s+h[5],o+h[6]),s+=h[5],o+=h[6];break;case"S":s=h[3],o=h[4],t.bezierCurveToShort(h[1],h[2],s,o);break;case"s":t.bezierCurveToShort(s+h[1],o+h[2],s+h[3],o+h[4]),s+=h[3],o+=h[4];break;case"Q":s=h[3],o=h[4],t.quadraticCurveTo(h[1],h[2],s,o);break;case"q":t.quadraticCurveTo(s+h[1],o+h[2],s+h[3],o+h[4]),s+=h[3],o+=h[4];break;case"T":s=h[1],o=h[2],t.quadraticCurveToShort(s,o);break;case"t":s+=h[1],o+=h[2],t.quadraticCurveToShort(s,o);break;case"A":s=h[6],o=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],s,o);break;case"a":s+=h[6],o+=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],s,o);break;case"Z":case"z":t.closePath(),n.length>0&&(r=n.pop(),r?(s=r.startX,o=r.startY):(s=0,o=0)),r=null;break;default:ht(`Unknown SVG path command: ${c}`)}c!=="Z"&&c!=="z"&&r===null&&(r={startX:s,startY:o},n.push(r))}return t}class Me{constructor(t=0,e=0,n=0){this.type="circle",this.x=t,this.y=e,this.radius=n}clone(){return new Me(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return!1;const n=this.radius*this.radius;let r=this.x-t,s=this.y-e;return r*=r,s*=s,r+s<=n}strokeContains(t,e,n,r=.5){if(this.radius===0)return!1;const s=this.x-t,o=this.y-e,a=this.radius,l=(1-r)*n,c=Math.sqrt(s*s+o*o);return c<=a+l&&c>a-(n-l)}getBounds(t){return t||(t=new N),t.x=this.x-this.radius,t.y=this.y-this.radius,t.width=this.radius*2,t.height=this.radius*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.radius=t.radius,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class Ce{constructor(t=0,e=0,n=0,r=0){this.type="ellipse",this.x=t,this.y=e,this.halfWidth=n,this.halfHeight=r}clone(){return new Ce(this.x,this.y,this.halfWidth,this.halfHeight)}contains(t,e){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let n=(t-this.x)/this.halfWidth,r=(e-this.y)/this.halfHeight;return n*=n,r*=r,n+r<=1}strokeContains(t,e,n,r=.5){const{halfWidth:s,halfHeight:o}=this;if(s<=0||o<=0)return!1;const a=n*(1-r),l=n-a,c=s-l,h=o-l,u=s+a,f=o+a,d=t-this.x,p=e-this.y,g=d*d/(c*c)+p*p/(h*h),x=d*d/(u*u)+p*p/(f*f);return g>1&&x<=1}getBounds(t){return t||(t=new N),t.x=this.x-this.halfWidth,t.y=this.y-this.halfHeight,t.width=this.halfWidth*2,t.height=this.halfHeight*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.halfWidth=t.halfWidth,this.halfHeight=t.halfHeight,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function Yi(i,t,e,n,r,s){const o=i-e,a=t-n,l=r-e,c=s-n,h=o*l+a*c,u=l*l+c*c;let f=-1;u!==0&&(f=h/u);let d,p;f<0?(d=e,p=n):f>1?(d=r,p=s):(d=e+f*l,p=n+f*c);const g=i-d,x=t-p;return g*g+x*x}let Xi,Zi;class Tt{constructor(...t){this.type="polygon";let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const n=[];for(let r=0,s=e.length;r<s;r++)n.push(e[r].x,e[r].y);e=n}this.points=e,this.closePath=!0}isClockwise(){let t=0;const e=this.points,n=e.length;for(let r=0;r<n;r+=2){const s=e[r],o=e[r+1],a=e[(r+2)%n],l=e[(r+3)%n];t+=(a-s)*(l+o)}return t<0}containsPolygon(t){const e=this.getBounds(Xi),n=t.getBounds(Zi);if(!e.containsRect(n))return!1;const r=t.points;for(let s=0;s<r.length;s+=2){const o=r[s],a=r[s+1];if(!this.contains(o,a))return!1}return!0}clone(){const t=this.points.slice(),e=new Tt(t);return e.closePath=this.closePath,e}contains(t,e){let n=!1;const r=this.points.length/2;for(let s=0,o=r-1;s<r;o=s++){const a=this.points[s*2],l=this.points[s*2+1],c=this.points[o*2],h=this.points[o*2+1];l>e!=h>e&&t<(c-a)*((e-l)/(h-l))+a&&(n=!n)}return n}strokeContains(t,e,n,r=.5){const s=n*n,o=s*(1-r),a=s-o,{points:l}=this,c=l.length-(this.closePath?0:2);for(let h=0;h<c;h+=2){const u=l[h],f=l[h+1],d=l[(h+2)%l.length],p=l[(h+3)%l.length],g=Yi(t,e,u,f,d,p),x=Math.sign((d-u)*(e-f)-(p-f)*(t-u));if(g<=(x<0?a:o))return!0}return!1}getBounds(t){t||(t=new N);const e=this.points;let n=1/0,r=-1/0,s=1/0,o=-1/0;for(let a=0,l=e.length;a<l;a+=2){const c=e[a],h=e[a+1];n=c<n?c:n,r=c>r?c:r,s=h<s?h:s,o=h>o?h:o}return t.x=n,t.width=r-n,t.y=s,t.height=o-s,t}copyFrom(t){return this.points=t.points.slice(),this.closePath=t.closePath,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((t,e)=>`${t}, ${e}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return D("8.11.0","Polygon.lastX is deprecated, please use Polygon.lastX instead."),this.points[this.points.length-2]}get y(){return D("8.11.0","Polygon.y is deprecated, please use Polygon.lastY instead."),this.points[this.points.length-1]}get startX(){return this.points[0]}get startY(){return this.points[1]}}const Nt=(i,t,e,n,r,s,o)=>{const a=i-e,l=t-n,c=Math.sqrt(a*a+l*l);return c>=r-s&&c<=r+o};class ke{constructor(t=0,e=0,n=0,r=0,s=20){this.type="roundedRectangle",this.x=t,this.y=e,this.width=n,this.height=r,this.radius=s}getBounds(t){return t||(t=new N),t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}clone(){return new ke(this.x,this.y,this.width,this.height,this.radius)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const n=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+n&&e<=this.y+this.height-n||t>=this.x+n&&t<=this.x+this.width-n)return!0;let r=t-(this.x+n),s=e-(this.y+n);const o=n*n;if(r*r+s*s<=o||(r=t-(this.x+this.width-n),r*r+s*s<=o)||(s=e-(this.y+this.height-n),r*r+s*s<=o)||(r=t-(this.x+n),r*r+s*s<=o))return!0}return!1}strokeContains(t,e,n,r=.5){const{x:s,y:o,width:a,height:l,radius:c}=this,h=n*(1-r),u=n-h,f=s+c,d=o+c,p=a-c*2,g=l-c*2,x=s+a,_=o+l;return(t>=s-h&&t<=s+u||t>=x-u&&t<=x+h)&&e>=d&&e<=d+g||(e>=o-h&&e<=o+u||e>=_-u&&e<=_+h)&&t>=f&&t<=f+p?!0:t<f&&e<d&&Nt(t,e,f,d,c,u,h)||t>x-c&&e<d&&Nt(t,e,x-c,d,c,u,h)||t>x-c&&e>_-c&&Nt(t,e,x-c,_-c,c,u,h)||t<f&&e>_-c&&Nt(t,e,f,_-c,c,u,h)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}const Fn={};function Ki(i,t,e){let n=2166136261;for(let r=0;r<t;r++)n^=i[r].uid,n=Math.imul(n,16777619),n>>>=0;return Fn[n]||Qi(i,t,n,e)}function Qi(i,t,e,n){const r={};let s=0;for(let a=0;a<n;a++){const l=a<t?i[a]:Z.EMPTY.source;r[s++]=l.source,r[s++]=l.style}const o=new qt(r);return Fn[e]=o,o}class je{constructor(t){typeof t=="number"?this.rawBinaryData=new ArrayBuffer(t):t instanceof Uint8Array?this.rawBinaryData=t.buffer:this.rawBinaryData=t,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData),this.size=this.rawBinaryData.byteLength}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}get float64View(){return this._float64Array||(this._float64Array=new Float64Array(this.rawBinaryData)),this._float64Array}get bigUint64View(){return this._bigUint64Array||(this._bigUint64Array=new BigUint64Array(this.rawBinaryData)),this._bigUint64Array}view(t){return this[`${t}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this.uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(t){switch(t){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${t} isn't a valid view type`)}}}function qe(i,t){const e=i.byteLength/8|0,n=new Float64Array(i,0,e);new Float64Array(t,0,e).set(n);const s=i.byteLength-e*8;if(s>0){const o=new Uint8Array(i,e*8,s);new Uint8Array(t,e*8,s).set(o)}}const Ji={normal:"normal-npm",add:"add-npm",screen:"screen-npm"};var tr=(i=>(i[i.DISABLED=0]="DISABLED",i[i.RENDERING_MASK_ADD=1]="RENDERING_MASK_ADD",i[i.MASK_ACTIVE=2]="MASK_ACTIVE",i[i.INVERSE_MASK_ACTIVE=3]="INVERSE_MASK_ACTIVE",i[i.RENDERING_MASK_REMOVE=4]="RENDERING_MASK_REMOVE",i[i.NONE=5]="NONE",i))(tr||{});function Oe(i,t){return t.alphaMode==="no-premultiply-alpha"&&Ji[i]||i}const er=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function nr(i){let t="";for(let e=0;e<i;++e)e>0&&(t+=`
else `),e<i-1&&(t+=`if(test == ${e}.0){}`);return t}function ir(i,t){if(i===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const e=t.createShader(t.FRAGMENT_SHADER);try{for(;;){const n=er.replace(/%forloop%/gi,nr(i));if(t.shaderSource(e,n),t.compileShader(e),!t.getShaderParameter(e,t.COMPILE_STATUS))i=i/2|0;else break}}finally{t.deleteShader(e)}return i}let ut=null;function rr(){if(ut)return ut;const i=vn();return ut=i.getParameter(i.MAX_TEXTURE_IMAGE_UNITS),ut=ir(ut,i),i.getExtension("WEBGL_lose_context")?.loseContext(),ut}class sr{constructor(){this.ids=Object.create(null),this.textures=[],this.count=0}clear(){for(let t=0;t<this.count;t++){const e=this.textures[t];this.textures[t]=null,this.ids[e.uid]=null}this.count=0}}class or{constructor(){this.renderPipeId="batch",this.action="startBatch",this.start=0,this.size=0,this.textures=new sr,this.blendMode="normal",this.topology="triangle-strip",this.canBundle=!0}destroy(){this.textures=null,this.gpuBindGroup=null,this.bindGroup=null,this.batcher=null}}const At=[];let Yt=0;wn.register({clear:()=>{if(At.length>0)for(const i of At)i&&i.destroy();At.length=0,Yt=0}});function Ye(){return Yt>0?At[--Yt]:new or}function Xe(i){At[Yt++]=i}let Pt=0;const Dn=class Ln{constructor(t){this.uid=W("batcher"),this.dirty=!0,this.batchIndex=0,this.batches=[],this._elements=[],t={...Ln.defaultOptions,...t},t.maxTextures||(D("v8.8.0","maxTextures is a required option for Batcher now, please pass it in the options"),t.maxTextures=rr());const{maxTextures:e,attributesInitialSize:n,indicesInitialSize:r}=t;this.attributeBuffer=new je(n*4),this.indexBuffer=new Uint16Array(r),this.maxTextures=e}begin(){this.elementSize=0,this.elementStart=0,this.indexSize=0,this.attributeSize=0;for(let t=0;t<this.batchIndex;t++)Xe(this.batches[t]);this.batchIndex=0,this._batchIndexStart=0,this._batchIndexSize=0,this.dirty=!0}add(t){this._elements[this.elementSize++]=t,t._indexStart=this.indexSize,t._attributeStart=this.attributeSize,t._batcher=this,this.indexSize+=t.indexSize,this.attributeSize+=t.attributeSize*this.vertexSize}checkAndUpdateTexture(t,e){const n=t._batch.textures.ids[e._source.uid];return!n&&n!==0?!1:(t._textureId=n,t.texture=e,!0)}updateElement(t){this.dirty=!0;const e=this.attributeBuffer;t.packAsQuad?this.packQuadAttributes(t,e.float32View,e.uint32View,t._attributeStart,t._textureId):this.packAttributes(t,e.float32View,e.uint32View,t._attributeStart,t._textureId)}break(t){const e=this._elements;if(!e[this.elementStart])return;let n=Ye(),r=n.textures;r.clear();const s=e[this.elementStart];let o=Oe(s.blendMode,s.texture._source),a=s.topology;this.attributeSize*4>this.attributeBuffer.size&&this._resizeAttributeBuffer(this.attributeSize*4),this.indexSize>this.indexBuffer.length&&this._resizeIndexBuffer(this.indexSize);const l=this.attributeBuffer.float32View,c=this.attributeBuffer.uint32View,h=this.indexBuffer;let u=this._batchIndexSize,f=this._batchIndexStart,d="startBatch";const p=this.maxTextures;for(let g=this.elementStart;g<this.elementSize;++g){const x=e[g];e[g]=null;const m=x.texture._source,b=Oe(x.blendMode,m),y=o!==b||a!==x.topology;if(m._batchTick===Pt&&!y){x._textureId=m._textureBindLocation,u+=x.indexSize,x.packAsQuad?(this.packQuadAttributes(x,l,c,x._attributeStart,x._textureId),this.packQuadIndex(h,x._indexStart,x._attributeStart/this.vertexSize)):(this.packAttributes(x,l,c,x._attributeStart,x._textureId),this.packIndex(x,h,x._indexStart,x._attributeStart/this.vertexSize)),x._batch=n;continue}m._batchTick=Pt,(r.count>=p||y)&&(this._finishBatch(n,f,u-f,r,o,a,t,d),d="renderBatch",f=u,o=b,a=x.topology,n=Ye(),r=n.textures,r.clear(),++Pt),x._textureId=m._textureBindLocation=r.count,r.ids[m.uid]=r.count,r.textures[r.count++]=m,x._batch=n,u+=x.indexSize,x.packAsQuad?(this.packQuadAttributes(x,l,c,x._attributeStart,x._textureId),this.packQuadIndex(h,x._indexStart,x._attributeStart/this.vertexSize)):(this.packAttributes(x,l,c,x._attributeStart,x._textureId),this.packIndex(x,h,x._indexStart,x._attributeStart/this.vertexSize))}r.count>0&&(this._finishBatch(n,f,u-f,r,o,a,t,d),f=u,++Pt),this.elementStart=this.elementSize,this._batchIndexStart=f,this._batchIndexSize=u}_finishBatch(t,e,n,r,s,o,a,l){t.gpuBindGroup=null,t.bindGroup=null,t.action=l,t.batcher=this,t.textures=r,t.blendMode=s,t.topology=o,t.start=e,t.size=n,++Pt,this.batches[this.batchIndex++]=t,a.add(t)}finish(t){this.break(t)}ensureAttributeBuffer(t){t*4<=this.attributeBuffer.size||this._resizeAttributeBuffer(t*4)}ensureIndexBuffer(t){t<=this.indexBuffer.length||this._resizeIndexBuffer(t)}_resizeAttributeBuffer(t){const e=Math.max(t,this.attributeBuffer.size*2),n=new je(e);qe(this.attributeBuffer.rawBinaryData,n.rawBinaryData),this.attributeBuffer=n}_resizeIndexBuffer(t){const e=this.indexBuffer;let n=Math.max(t,e.length*1.5);n+=n%2;const r=n>65535?new Uint32Array(n):new Uint16Array(n);if(r.BYTES_PER_ELEMENT!==e.BYTES_PER_ELEMENT)for(let s=0;s<e.length;s++)r[s]=e[s];else qe(e.buffer,r.buffer);this.indexBuffer=r}packQuadIndex(t,e,n){t[e]=n+0,t[e+1]=n+1,t[e+2]=n+2,t[e+3]=n+0,t[e+4]=n+2,t[e+5]=n+3}packIndex(t,e,n,r){const s=t.indices,o=t.indexSize,a=t.indexOffset,l=t.attributeOffset;for(let c=0;c<o;c++)e[n++]=r+s[c+a]-l}destroy(){if(this.batches!==null){for(let t=0;t<this.batches.length;t++)Xe(this.batches[t]);this.batches=null;for(let t=0;t<this._elements.length;t++)this._elements[t]&&(this._elements[t]._batch=null);this._elements=null,this.indexBuffer=null,this.attributeBuffer.destroy(),this.attributeBuffer=null}}};Dn.defaultOptions={maxTextures:null,attributesInitialSize:4,indicesInitialSize:6};let ar=Dn;var V=(i=>(i[i.MAP_READ=1]="MAP_READ",i[i.MAP_WRITE=2]="MAP_WRITE",i[i.COPY_SRC=4]="COPY_SRC",i[i.COPY_DST=8]="COPY_DST",i[i.INDEX=16]="INDEX",i[i.VERTEX=32]="VERTEX",i[i.UNIFORM=64]="UNIFORM",i[i.STORAGE=128]="STORAGE",i[i.INDIRECT=256]="INDIRECT",i[i.QUERY_RESOLVE=512]="QUERY_RESOLVE",i[i.STATIC=1024]="STATIC",i))(V||{});class Et extends Ft{constructor(t){let{data:e,size:n}=t;const{usage:r,label:s,shrinkToFit:o}=t;super(),this.uid=W("buffer"),this._resourceType="buffer",this._resourceId=W("resource"),this._touched=0,this._updateID=1,this._dataInt32=null,this.shrinkToFit=!0,this.destroyed=!1,e instanceof Array&&(e=new Float32Array(e)),this._data=e,n??(n=e?.byteLength);const a=!!e;this.descriptor={size:n,usage:r,mappedAtCreation:a,label:s},this.shrinkToFit=o??!0}get data(){return this._data}set data(t){this.setDataWithSize(t,t.length,!0)}get dataInt32(){return this._dataInt32||(this._dataInt32=new Int32Array(this.data.buffer)),this._dataInt32}get static(){return!!(this.descriptor.usage&V.STATIC)}set static(t){t?this.descriptor.usage|=V.STATIC:this.descriptor.usage&=~V.STATIC}setDataWithSize(t,e,n){if(this._updateID++,this._updateSize=e*t.BYTES_PER_ELEMENT,this._data===t){n&&this.emit("update",this);return}const r=this._data;if(this._data=t,this._dataInt32=null,!r||r.length!==t.length){!this.shrinkToFit&&r&&t.byteLength<r.byteLength?n&&this.emit("update",this):(this.descriptor.size=t.byteLength,this._resourceId=W("resource"),this.emit("change",this));return}n&&this.emit("update",this)}update(t){this._updateSize=t??this._updateSize,this._updateID++,this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._data=null,this.descriptor=null,this.removeAllListeners()}}function Vn(i,t){if(!(i instanceof Et)){let e=t?V.INDEX:V.VERTEX;i instanceof Array&&(t?(i=new Uint32Array(i),e=V.INDEX|V.COPY_DST):(i=new Float32Array(i),e=V.VERTEX|V.COPY_DST)),i=new Et({data:i,label:t?"index-mesh-buffer":"vertex-mesh-buffer",usage:e})}return i}function lr(i,t,e){const n=i.getAttribute(t);if(!n)return e.minX=0,e.minY=0,e.maxX=0,e.maxY=0,e;const r=n.buffer.data;let s=1/0,o=1/0,a=-1/0,l=-1/0;const c=r.BYTES_PER_ELEMENT,h=(n.offset||0)/c,u=(n.stride||8)/c;for(let f=h;f<r.length;f+=u){const d=r[f],p=r[f+1];d>a&&(a=d),p>l&&(l=p),d<s&&(s=d),p<o&&(o=p)}return e.minX=s,e.minY=o,e.maxX=a,e.maxY=l,e}function hr(i){return(i instanceof Et||Array.isArray(i)||i.BYTES_PER_ELEMENT)&&(i={buffer:i}),i.buffer=Vn(i.buffer,!1),i}class cr extends Ft{constructor(t={}){super(),this.uid=W("geometry"),this._layoutKey=0,this.instanceCount=1,this._bounds=new Se,this._boundsDirty=!0;const{attributes:e,indexBuffer:n,topology:r}=t;if(this.buffers=[],this.attributes={},e)for(const s in e)this.addAttribute(s,e[s]);this.instanceCount=t.instanceCount??1,n&&this.addIndex(n),this.topology=r||"triangle-list"}onBufferUpdate(){this._boundsDirty=!0,this.emit("update",this)}getAttribute(t){return this.attributes[t]}getIndex(){return this.indexBuffer}getBuffer(t){return this.getAttribute(t).buffer}getSize(){for(const t in this.attributes){const e=this.attributes[t];return e.buffer.data.length/(e.stride/4||e.size)}return 0}addAttribute(t,e){const n=hr(e);this.buffers.indexOf(n.buffer)===-1&&(this.buffers.push(n.buffer),n.buffer.on("update",this.onBufferUpdate,this),n.buffer.on("change",this.onBufferUpdate,this)),this.attributes[t]=n}addIndex(t){this.indexBuffer=Vn(t,!0),this.buffers.push(this.indexBuffer)}get bounds(){return this._boundsDirty?(this._boundsDirty=!1,lr(this,"aPosition",this._bounds)):this._bounds}destroy(t=!1){this.emit("destroy",this),this.removeAllListeners(),t&&this.buffers.forEach(e=>e.destroy()),this.attributes=null,this.buffers=null,this.indexBuffer=null,this._bounds=null}}const ur=new Float32Array(1),dr=new Uint32Array(1);class fr extends cr{constructor(){const e=new Et({data:ur,label:"attribute-batch-buffer",usage:V.VERTEX|V.COPY_DST,shrinkToFit:!1}),n=new Et({data:dr,label:"index-batch-buffer",usage:V.INDEX|V.COPY_DST,shrinkToFit:!1}),r=24;super({attributes:{aPosition:{buffer:e,format:"float32x2",stride:r,offset:0},aUV:{buffer:e,format:"float32x2",stride:r,offset:8},aColor:{buffer:e,format:"unorm8x4",stride:r,offset:16},aTextureIdAndRound:{buffer:e,format:"uint16x2",stride:r,offset:20}},indexBuffer:n})}}function Ze(i,t,e){if(i)for(const n in i){const r=n.toLocaleLowerCase(),s=t[r];if(s){let o=i[n];n==="header"&&(o=o.replace(/@in\s+[^;]+;\s*/g,"").replace(/@out\s+[^;]+;\s*/g,"")),e&&s.push(`//----${e}----//`),s.push(o)}else ht(`${n} placement hook does not exist in shader`)}}const pr=/\{\{(.*?)\}\}/g;function Ke(i){const t={};return(i.match(pr)?.map(n=>n.replace(/[{()}]/g,""))??[]).forEach(n=>{t[n]=[]}),t}function Qe(i,t){let e;const n=/@in\s+([^;]+);/g;for(;(e=n.exec(i))!==null;)t.push(e[1])}function Je(i,t,e=!1){const n=[];Qe(t,n),i.forEach(a=>{a.header&&Qe(a.header,n)});const r=n;e&&r.sort();const s=r.map((a,l)=>`       @location(${l}) ${a},`).join(`
`);let o=t.replace(/@in\s+[^;]+;\s*/g,"");return o=o.replace("{{in}}",`
${s}
`),o}function tn(i,t){let e;const n=/@out\s+([^;]+);/g;for(;(e=n.exec(i))!==null;)t.push(e[1])}function xr(i){const e=/\b(\w+)\s*:/g.exec(i);return e?e[1]:""}function gr(i){const t=/@.*?\s+/g;return i.replace(t,"")}function mr(i,t){const e=[];tn(t,e),i.forEach(l=>{l.header&&tn(l.header,e)});let n=0;const r=e.sort().map(l=>l.indexOf("builtin")>-1?l:`@location(${n++}) ${l}`).join(`,
`),s=e.sort().map(l=>`       var ${gr(l)};`).join(`
`),o=`return VSOutput(
            ${e.sort().map(l=>` ${xr(l)}`).join(`,
`)});`;let a=t.replace(/@out\s+[^;]+;\s*/g,"");return a=a.replace("{{struct}}",`
${r}
`),a=a.replace("{{start}}",`
${s}
`),a=a.replace("{{return}}",`
${o}
`),a}function en(i,t){let e=i;for(const n in t){const r=t[n];r.join(`
`).length?e=e.replace(`{{${n}}}`,`//-----${n} START-----//
${r.join(`
`)}
//----${n} FINISH----//`):e=e.replace(`{{${n}}}`,"")}return e}const J=Object.create(null),se=new Map;let yr=0;function _r({template:i,bits:t}){const e=Wn(i,t);if(J[e])return J[e];const{vertex:n,fragment:r}=Sr(i,t);return J[e]=$n(n,r,t),J[e]}function br({template:i,bits:t}){const e=Wn(i,t);return J[e]||(J[e]=$n(i.vertex,i.fragment,t)),J[e]}function Sr(i,t){const e=t.map(o=>o.vertex).filter(o=>!!o),n=t.map(o=>o.fragment).filter(o=>!!o);let r=Je(e,i.vertex,!0);r=mr(e,r);const s=Je(n,i.fragment,!0);return{vertex:r,fragment:s}}function Wn(i,t){return t.map(e=>(se.has(e)||se.set(e,yr++),se.get(e))).sort((e,n)=>e-n).join("-")+i.vertex+i.fragment}function $n(i,t,e){const n=Ke(i),r=Ke(t);return e.forEach(s=>{Ze(s.vertex,n,s.name),Ze(s.fragment,r,s.name)}),{vertex:en(i,n),fragment:en(t,r)}}const wr=`
    @in aPosition: vec2<f32>;
    @in aUV: vec2<f32>;

    @out @builtin(position) vPosition: vec4<f32>;
    @out vUV : vec2<f32>;
    @out vColor : vec4<f32>;

    {{header}}

    struct VSOutput {
        {{struct}}
    };

    @vertex
    fn main( {{in}} ) -> VSOutput {

        var worldTransformMatrix = globalUniforms.uWorldTransformMatrix;
        var modelMatrix = mat3x3<f32>(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        var position = aPosition;
        var uv = aUV;

        {{start}}

        vColor = vec4<f32>(1., 1., 1., 1.);

        {{main}}

        vUV = uv;

        var modelViewProjectionMatrix = globalUniforms.uProjectionMatrix * worldTransformMatrix * modelMatrix;

        vPosition =  vec4<f32>((modelViewProjectionMatrix *  vec3<f32>(position, 1.0)).xy, 0.0, 1.0);

        vColor *= globalUniforms.uWorldColorAlpha;

        {{end}}

        {{return}}
    };
`,Pr=`
    @in vUV : vec2<f32>;
    @in vColor : vec4<f32>;

    {{header}}

    @fragment
    fn main(
        {{in}}
      ) -> @location(0) vec4<f32> {

        {{start}}

        var outColor:vec4<f32>;

        {{main}}

        var finalColor:vec4<f32> = outColor * vColor;

        {{end}}

        return finalColor;
      };
`,vr=`
    in vec2 aPosition;
    in vec2 aUV;

    out vec4 vColor;
    out vec2 vUV;

    {{header}}

    void main(void){

        mat3 worldTransformMatrix = uWorldTransformMatrix;
        mat3 modelMatrix = mat3(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        vec2 position = aPosition;
        vec2 uv = aUV;

        {{start}}

        vColor = vec4(1.);

        {{main}}

        vUV = uv;

        mat3 modelViewProjectionMatrix = uProjectionMatrix * worldTransformMatrix * modelMatrix;

        gl_Position = vec4((modelViewProjectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);

        vColor *= uWorldColorAlpha;

        {{end}}
    }
`,Mr=`

    in vec4 vColor;
    in vec2 vUV;

    out vec4 finalColor;

    {{header}}

    void main(void) {

        {{start}}

        vec4 outColor;

        {{main}}

        finalColor = outColor * vColor;

        {{end}}
    }
`,Cr={name:"global-uniforms-bit",vertex:{header:`
        struct GlobalUniforms {
            uProjectionMatrix:mat3x3<f32>,
            uWorldTransformMatrix:mat3x3<f32>,
            uWorldColorAlpha: vec4<f32>,
            uResolution: vec2<f32>,
        }

        @group(0) @binding(0) var<uniform> globalUniforms : GlobalUniforms;
        `}},kr={name:"global-uniforms-bit",vertex:{header:`
          uniform mat3 uProjectionMatrix;
          uniform mat3 uWorldTransformMatrix;
          uniform vec4 uWorldColorAlpha;
          uniform vec2 uResolution;
        `}};function Tr({bits:i,name:t}){const e=_r({template:{fragment:Pr,vertex:wr},bits:[Cr,...i]});return Zt.from({name:t,vertex:{source:e.vertex,entryPoint:"main"},fragment:{source:e.fragment,entryPoint:"main"}})}function Ar({bits:i,name:t}){return new Cn({name:t,...br({template:{vertex:vr,fragment:Mr},bits:[kr,...i]})})}const Ir={name:"color-bit",vertex:{header:`
            @in aColor: vec4<f32>;
        `,main:`
            vColor *= vec4<f32>(aColor.rgb * aColor.a, aColor.a);
        `}},Br={name:"color-bit",vertex:{header:`
            in vec4 aColor;
        `,main:`
            vColor *= vec4(aColor.rgb * aColor.a, aColor.a);
        `}},oe={};function Rr(i){const t=[];if(i===1)t.push("@group(1) @binding(0) var textureSource1: texture_2d<f32>;"),t.push("@group(1) @binding(1) var textureSampler1: sampler;");else{let e=0;for(let n=0;n<i;n++)t.push(`@group(1) @binding(${e++}) var textureSource${n+1}: texture_2d<f32>;`),t.push(`@group(1) @binding(${e++}) var textureSampler${n+1}: sampler;`)}return t.join(`
`)}function zr(i){const t=[];if(i===1)t.push("outColor = textureSampleGrad(textureSource1, textureSampler1, vUV, uvDx, uvDy);");else{t.push("switch vTextureId {");for(let e=0;e<i;e++)e===i-1?t.push("  default:{"):t.push(`  case ${e}:{`),t.push(`      outColor = textureSampleGrad(textureSource${e+1}, textureSampler${e+1}, vUV, uvDx, uvDy);`),t.push("      break;}");t.push("}")}return t.join(`
`)}function Er(i){return oe[i]||(oe[i]={name:"texture-batch-bit",vertex:{header:`
                @in aTextureIdAndRound: vec2<u32>;
                @out @interpolate(flat) vTextureId : u32;
            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1)
                {
                    vPosition = vec4<f32>(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
                }
            `},fragment:{header:`
                @in @interpolate(flat) vTextureId: u32;

                ${Rr(i)}
            `,main:`
                var uvDx = dpdx(vUV);
                var uvDy = dpdy(vUV);

                ${zr(i)}
            `}}),oe[i]}const ae={};function Gr(i){const t=[];for(let e=0;e<i;e++)e>0&&t.push("else"),e<i-1&&t.push(`if(vTextureId < ${e}.5)`),t.push("{"),t.push(`	outColor = texture(uTextures[${e}], vUV);`),t.push("}");return t.join(`
`)}function Fr(i){return ae[i]||(ae[i]={name:"texture-batch-bit",vertex:{header:`
                in vec2 aTextureIdAndRound;
                out float vTextureId;

            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1.)
                {
                    gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
                }
            `},fragment:{header:`
                in float vTextureId;

                uniform sampler2D uTextures[${i}];

            `,main:`

                ${Gr(i)}
            `}}),ae[i]}const Dr={name:"round-pixels-bit",vertex:{header:`
            fn roundPixels(position: vec2<f32>, targetSize: vec2<f32>) -> vec2<f32>
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Lr={name:"round-pixels-bit",vertex:{header:`
            vec2 roundPixels(vec2 position, vec2 targetSize)
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},nn={};function Vr(i){let t=nn[i];if(t)return t;const e=new Int32Array(i);for(let n=0;n<i;n++)e[n]=n;return t=nn[i]=new In({uTextures:{value:e,type:"i32",size:i}},{isStatic:!0}),t}class Wr extends Pe{constructor(t){const e=Ar({name:"batch",bits:[Br,Fr(t),Lr]}),n=Tr({name:"batch",bits:[Ir,Er(t),Dr]});super({glProgram:e,gpuProgram:n,resources:{batchSamplers:Vr(t)}})}}let le=null;const Hn=class Un extends ar{constructor(t){super(t),this.geometry=new fr,this.name=Un.extension.name,this.vertexSize=6,le??(le=new Wr(t.maxTextures)),this.shader=le}packAttributes(t,e,n,r,s){const o=s<<16|t.roundPixels&65535,a=t.transform,l=a.a,c=a.b,h=a.c,u=a.d,f=a.tx,d=a.ty,{positions:p,uvs:g}=t,x=t.color,_=t.attributeOffset,m=_+t.attributeSize;for(let b=_;b<m;b++){const y=b*2,P=p[y],w=p[y+1];e[r++]=l*P+h*w+f,e[r++]=u*w+c*P+d,e[r++]=g[y],e[r++]=g[y+1],n[r++]=x,n[r++]=o}}packQuadAttributes(t,e,n,r,s){const o=t.texture,a=t.transform,l=a.a,c=a.b,h=a.c,u=a.d,f=a.tx,d=a.ty,p=t.bounds,g=p.maxX,x=p.minX,_=p.maxY,m=p.minY,b=o.uvs,y=t.color,P=s<<16|t.roundPixels&65535;e[r+0]=l*x+h*m+f,e[r+1]=u*m+c*x+d,e[r+2]=b.x0,e[r+3]=b.y0,n[r+4]=y,n[r+5]=P,e[r+6]=l*g+h*m+f,e[r+7]=u*m+c*g+d,e[r+8]=b.x1,e[r+9]=b.y1,n[r+10]=y,n[r+11]=P,e[r+12]=l*g+h*_+f,e[r+13]=u*_+c*g+d,e[r+14]=b.x2,e[r+15]=b.y2,n[r+16]=y,n[r+17]=P,e[r+18]=l*x+h*_+f,e[r+19]=u*_+c*x+d,e[r+20]=b.x3,e[r+21]=b.y3,n[r+22]=y,n[r+23]=P}};Hn.extension={type:[Y.Batcher],name:"default"};let $r=Hn;function Hr(i,t,e,n,r,s,o,a=null){let l=0;e*=t,r*=s;const c=a.a,h=a.b,u=a.c,f=a.d,d=a.tx,p=a.ty;for(;l<o;){const g=i[e],x=i[e+1];n[r]=c*g+u*x+d,n[r+1]=h*g+f*x+p,r+=s,e+=t,l++}}function Ur(i,t,e,n){let r=0;for(t*=e;r<n;)i[t]=0,i[t+1]=0,t+=e,r++}function Nn(i,t,e,n,r){const s=t.a,o=t.b,a=t.c,l=t.d,c=t.tx,h=t.ty;e||(e=0),n||(n=2),r||(r=i.length/n-e);let u=e*n;for(let f=0;f<r;f++){const d=i[u],p=i[u+1];i[u]=s*d+a*p+c,i[u+1]=o*d+l*p+h,u+=n}}const Nr=new H;class jn{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.renderable&&this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const t=this.baseColor,e=t>>16|t&65280|(t&255)<<16,n=this.renderable;return n?ii(e,n.groupColor)+(this.alpha*n.groupAlpha*255<<24):e+(this.alpha*255<<24)}get transform(){return this.renderable?.groupTransform||Nr}copyTo(t){t.indexOffset=this.indexOffset,t.indexSize=this.indexSize,t.attributeOffset=this.attributeOffset,t.attributeSize=this.attributeSize,t.baseColor=this.baseColor,t.alpha=this.alpha,t.texture=this.texture,t.geometryData=this.geometryData,t.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}destroy(){this.renderable=null,this.texture=null,this.geometryData=null,this._batcher=null,this._batch=null}}const Gt={extension:{type:Y.ShapeBuilder,name:"circle"},build(i,t){let e,n,r,s,o,a;if(i.type==="circle"){const y=i;if(o=a=y.radius,o<=0)return!1;e=y.x,n=y.y,r=s=0}else if(i.type==="ellipse"){const y=i;if(o=y.halfWidth,a=y.halfHeight,o<=0||a<=0)return!1;e=y.x,n=y.y,r=s=0}else{const y=i,P=y.width/2,w=y.height/2;e=y.x+P,n=y.y+w,o=a=Math.max(0,Math.min(y.radius,Math.min(P,w))),r=P-o,s=w-a}if(r<0||s<0)return!1;const l=Math.ceil(2.3*Math.sqrt(o+a)),c=l*8+(r?4:0)+(s?4:0);if(c===0)return!1;if(l===0)return t[0]=t[6]=e+r,t[1]=t[3]=n+s,t[2]=t[4]=e-r,t[5]=t[7]=n-s,!0;let h=0,u=l*4+(r?2:0)+2,f=u,d=c,p=r+o,g=s,x=e+p,_=e-p,m=n+g;if(t[h++]=x,t[h++]=m,t[--u]=m,t[--u]=_,s){const y=n-g;t[f++]=_,t[f++]=y,t[--d]=y,t[--d]=x}for(let y=1;y<l;y++){const P=Math.PI/2*(y/l),w=r+Math.cos(P)*o,S=s+Math.sin(P)*a,I=e+w,k=e-w,v=n+S,T=n-S;t[h++]=I,t[h++]=v,t[--u]=v,t[--u]=k,t[f++]=k,t[f++]=T,t[--d]=T,t[--d]=I}p=r,g=s+a,x=e+p,_=e-p,m=n+g;const b=n-g;return t[h++]=x,t[h++]=m,t[--d]=b,t[--d]=x,r&&(t[h++]=_,t[h++]=m,t[--d]=b,t[--d]=_),!0},triangulate(i,t,e,n,r,s){if(i.length===0)return;let o=0,a=0;for(let h=0;h<i.length;h+=2)o+=i[h],a+=i[h+1];o/=i.length/2,a/=i.length/2;let l=n;t[l*e]=o,t[l*e+1]=a;const c=l++;for(let h=0;h<i.length;h+=2)t[l*e]=i[h],t[l*e+1]=i[h+1],h>0&&(r[s++]=l,r[s++]=c,r[s++]=l-1),l++;r[s++]=c+1,r[s++]=c,r[s++]=l-1}},jr={...Gt,extension:{...Gt.extension,name:"ellipse"}},qr={...Gt,extension:{...Gt.extension,name:"roundedRectangle"}},qn=1e-4,rn=1e-4;function Or(i){const t=i.length;if(t<6)return 1;let e=0;for(let n=0,r=i[t-2],s=i[t-1];n<t;n+=2){const o=i[n],a=i[n+1];e+=(o-r)*(a+s),r=o,s=a}return e<0?-1:1}function sn(i,t,e,n,r,s,o,a){const l=i-e*r,c=t-n*r,h=i+e*s,u=t+n*s;let f,d;o?(f=n,d=-e):(f=-n,d=e);const p=l+f,g=c+d,x=h+f,_=u+d;return a.push(p,g),a.push(x,_),2}function rt(i,t,e,n,r,s,o,a){const l=e-i,c=n-t;let h=Math.atan2(l,c),u=Math.atan2(r-i,s-t);a&&h<u?h+=Math.PI*2:!a&&h>u&&(u+=Math.PI*2);let f=h;const d=u-h,p=Math.abs(d),g=Math.sqrt(l*l+c*c),x=(15*p*Math.sqrt(g)/Math.PI>>0)+1,_=d/x;if(f+=_,a){o.push(i,t),o.push(e,n);for(let m=1,b=f;m<x;m++,b+=_)o.push(i,t),o.push(i+Math.sin(b)*g,t+Math.cos(b)*g);o.push(i,t),o.push(r,s)}else{o.push(e,n),o.push(i,t);for(let m=1,b=f;m<x;m++,b+=_)o.push(i+Math.sin(b)*g,t+Math.cos(b)*g),o.push(i,t);o.push(r,s),o.push(i,t)}return x*2}function Yr(i,t,e,n,r,s){const o=qn;if(i.length===0)return;const a=t;let l=a.alignment;if(t.alignment!==.5){let B=Or(i);l=(l-.5)*B+.5}const c=new ft(i[0],i[1]),h=new ft(i[i.length-2],i[i.length-1]),u=n,f=Math.abs(c.x-h.x)<o&&Math.abs(c.y-h.y)<o;if(u){i=i.slice(),f&&(i.pop(),i.pop(),h.set(i[i.length-2],i[i.length-1]));const B=(c.x+h.x)*.5,Q=(h.y+c.y)*.5;i.unshift(B,Q),i.push(B,Q)}const d=r,p=i.length/2;let g=i.length;const x=d.length/2,_=a.width/2,m=_*_,b=a.miterLimit*a.miterLimit;let y=i[0],P=i[1],w=i[2],S=i[3],I=0,k=0,v=-(P-S),T=y-w,R=0,z=0,E=Math.sqrt(v*v+T*T);v/=E,T/=E,v*=_,T*=_;const ct=l,M=(1-ct)*2,A=ct*2;u||(a.cap==="round"?g+=rt(y-v*(M-A)*.5,P-T*(M-A)*.5,y-v*M,P-T*M,y+v*A,P+T*A,d,!0)+2:a.cap==="square"&&(g+=sn(y,P,v,T,M,A,!0,d))),d.push(y-v*M,P-T*M),d.push(y+v*A,P+T*A);for(let B=1;B<p-1;++B){y=i[(B-1)*2],P=i[(B-1)*2+1],w=i[B*2],S=i[B*2+1],I=i[(B+1)*2],k=i[(B+1)*2+1],v=-(P-S),T=y-w,E=Math.sqrt(v*v+T*T),v/=E,T/=E,v*=_,T*=_,R=-(S-k),z=w-I,E=Math.sqrt(R*R+z*z),R/=E,z/=E,R*=_,z*=_;const Q=w-y,gt=P-S,mt=w-I,yt=k-S,Be=Q*mt+gt*yt,Dt=gt*mt-yt*Q,_t=Dt<0;if(Math.abs(Dt)<.001*Math.abs(Be)){d.push(w-v*M,S-T*M),d.push(w+v*A,S+T*A),Be>=0&&(a.join==="round"?g+=rt(w,S,w-v*M,S-T*M,w-R*M,S-z*M,d,!1)+4:g+=2,d.push(w-R*A,S-z*A),d.push(w+R*M,S+z*M));continue}const Re=(-v+y)*(-T+S)-(-v+w)*(-T+P),ze=(-R+I)*(-z+S)-(-R+w)*(-z+k),Lt=(Q*ze-mt*Re)/Dt,Vt=(yt*Re-gt*ze)/Dt,Jt=(Lt-w)*(Lt-w)+(Vt-S)*(Vt-S),tt=w+(Lt-w)*M,et=S+(Vt-S)*M,nt=w-(Lt-w)*A,it=S-(Vt-S)*A,Jn=Math.min(Q*Q+gt*gt,mt*mt+yt*yt),Ee=_t?M:A,ti=Jn+Ee*Ee*m;Jt<=ti?a.join==="bevel"||Jt/m>b?(_t?(d.push(tt,et),d.push(w+v*A,S+T*A),d.push(tt,et),d.push(w+R*A,S+z*A)):(d.push(w-v*M,S-T*M),d.push(nt,it),d.push(w-R*M,S-z*M),d.push(nt,it)),g+=2):a.join==="round"?_t?(d.push(tt,et),d.push(w+v*A,S+T*A),g+=rt(w,S,w+v*A,S+T*A,w+R*A,S+z*A,d,!0)+4,d.push(tt,et),d.push(w+R*A,S+z*A)):(d.push(w-v*M,S-T*M),d.push(nt,it),g+=rt(w,S,w-v*M,S-T*M,w-R*M,S-z*M,d,!1)+4,d.push(w-R*M,S-z*M),d.push(nt,it)):(d.push(tt,et),d.push(nt,it)):(d.push(w-v*M,S-T*M),d.push(w+v*A,S+T*A),a.join==="round"?_t?g+=rt(w,S,w+v*A,S+T*A,w+R*A,S+z*A,d,!0)+2:g+=rt(w,S,w-v*M,S-T*M,w-R*M,S-z*M,d,!1)+2:a.join==="miter"&&Jt/m<=b&&(_t?(d.push(nt,it),d.push(nt,it)):(d.push(tt,et),d.push(tt,et)),g+=2),d.push(w-R*M,S-z*M),d.push(w+R*A,S+z*A),g+=2)}y=i[(p-2)*2],P=i[(p-2)*2+1],w=i[(p-1)*2],S=i[(p-1)*2+1],v=-(P-S),T=y-w,E=Math.sqrt(v*v+T*T),v/=E,T/=E,v*=_,T*=_,d.push(w-v*M,S-T*M),d.push(w+v*A,S+T*A),u||(a.cap==="round"?g+=rt(w-v*(M-A)*.5,S-T*(M-A)*.5,w-v*M,S-T*M,w+v*A,S+T*A,d,!1)+2:a.cap==="square"&&(g+=sn(w,S,v,T,M,A,!1,d)));const xt=rn*rn;for(let B=x;B<g+x-2;++B)y=d[B*2],P=d[B*2+1],w=d[(B+1)*2],S=d[(B+1)*2+1],I=d[(B+2)*2],k=d[(B+2)*2+1],!(Math.abs(y*(S-k)+w*(k-P)+I*(P-S))<xt)&&s.push(B,B+1,B+2)}function Xr(i,t,e,n){const r=qn;if(i.length===0)return;const s=i[0],o=i[1],a=i[i.length-2],l=i[i.length-1],c=t||Math.abs(s-a)<r&&Math.abs(o-l)<r,h=e,u=i.length/2,f=h.length/2;for(let d=0;d<u;d++)h.push(i[d*2]),h.push(i[d*2+1]);for(let d=0;d<u-1;d++)n.push(f+d,f+d+1);c&&n.push(f+u-1,f)}function On(i,t,e,n,r,s,o){const a=Vi(i,t,2);if(!a)return;for(let c=0;c<a.length;c+=3)s[o++]=a[c]+r,s[o++]=a[c+1]+r,s[o++]=a[c+2]+r;let l=r*n;for(let c=0;c<i.length;c+=2)e[l]=i[c],e[l+1]=i[c+1],l+=n}const Zr=[],Kr={extension:{type:Y.ShapeBuilder,name:"polygon"},build(i,t){for(let e=0;e<i.points.length;e++)t[e]=i.points[e];return!0},triangulate(i,t,e,n,r,s){On(i,Zr,t,e,n,r,s)}},Qr={extension:{type:Y.ShapeBuilder,name:"rectangle"},build(i,t){const e=i,n=e.x,r=e.y,s=e.width,o=e.height;return s>0&&o>0?(t[0]=n,t[1]=r,t[2]=n+s,t[3]=r,t[4]=n+s,t[5]=r+o,t[6]=n,t[7]=r+o,!0):!1},triangulate(i,t,e,n,r,s){let o=0;n*=e,t[n+o]=i[0],t[n+o+1]=i[1],o+=e,t[n+o]=i[2],t[n+o+1]=i[3],o+=e,t[n+o]=i[6],t[n+o+1]=i[7],o+=e,t[n+o]=i[4],t[n+o+1]=i[5],o+=e;const a=n/e;r[s++]=a,r[s++]=a+1,r[s++]=a+2,r[s++]=a+1,r[s++]=a+3,r[s++]=a+2}},Jr={extension:{type:Y.ShapeBuilder,name:"triangle"},build(i,t){return t[0]=i.x,t[1]=i.y,t[2]=i.x2,t[3]=i.y2,t[4]=i.x3,t[5]=i.y3,!0},triangulate(i,t,e,n,r,s){let o=0;n*=e,t[n+o]=i[0],t[n+o+1]=i[1],o+=e,t[n+o]=i[2],t[n+o+1]=i[3],o+=e,t[n+o]=i[4],t[n+o+1]=i[5];const a=n/e;r[s++]=a,r[s++]=a+1,r[s++]=a+2}},ts=new H,es=new N;function ns(i,t,e,n){const r=t.matrix?i.copyFrom(t.matrix).invert():i.identity();if(t.textureSpace==="local"){const o=e.getBounds(es);t.width&&o.pad(t.width);const{x:a,y:l}=o,c=1/o.width,h=1/o.height,u=-a*c,f=-l*h,d=r.a,p=r.b,g=r.c,x=r.d;r.a*=c,r.b*=c,r.c*=h,r.d*=h,r.tx=u*d+f*g+r.tx,r.ty=u*p+f*x+r.ty}else r.translate(t.texture.frame.x,t.texture.frame.y),r.scale(1/t.texture.source.width,1/t.texture.source.height);const s=t.texture.source.style;return!(t.fill instanceof K)&&s.addressMode==="clamp-to-edge"&&(s.addressMode="repeat",s.update()),n&&r.append(ts.copyFrom(n).invert()),r}const Qt={};Pn.handleByMap(Y.ShapeBuilder,Qt);Pn.add(Qr,Kr,Jr,Gt,jr,qr);const is=new N,rs=new H;function ss(i,t){const{geometryData:e,batches:n}=t;n.length=0,e.indices.length=0,e.vertices.length=0,e.uvs.length=0;for(let r=0;r<i.instructions.length;r++){const s=i.instructions[r];if(s.action==="texture")os(s.data,n,e);else if(s.action==="fill"||s.action==="stroke"){const o=s.action==="stroke",a=s.data.path.shapePath,l=s.data.style,c=s.data.hole;o&&c&&on(c.shapePath,l,!0,n,e),c&&(a.shapePrimitives[a.shapePrimitives.length-1].holes=c.shapePath.shapePrimitives),on(a,l,o,n,e)}}}function os(i,t,e){const n=[],r=Qt.rectangle,s=is;s.x=i.dx,s.y=i.dy,s.width=i.dw,s.height=i.dh;const o=i.transform;if(!r.build(s,n))return;const{vertices:a,uvs:l,indices:c}=e,h=c.length,u=a.length/2;o&&Nn(n,o),r.triangulate(n,a,2,u,c,h);const f=i.image,d=f.uvs;l.push(d.x0,d.y0,d.x1,d.y1,d.x3,d.y3,d.x2,d.y2);const p=kt.get(jn);p.indexOffset=h,p.indexSize=c.length-h,p.attributeOffset=u,p.attributeSize=a.length/2-u,p.baseColor=i.style,p.alpha=i.alpha,p.texture=f,p.geometryData=e,t.push(p)}function on(i,t,e,n,r){const{vertices:s,uvs:o,indices:a}=r;i.shapePrimitives.forEach(({shape:l,transform:c,holes:h})=>{const u=[],f=Qt[l.type];if(!f.build(l,u))return;const d=a.length,p=s.length/2;let g="triangle-list";if(c&&Nn(u,c),e){const b=l.closePath??!0,y=t;y.pixelLine?(Xr(u,b,s,a),g="line-list"):Yr(u,y,!1,b,s,a)}else if(h){const b=[],y=u.slice();as(h).forEach(w=>{b.push(y.length/2),y.push(...w)}),On(y,b,s,2,p,a,d)}else f.triangulate(u,s,2,p,a,d);const x=o.length/2,_=t.texture;if(_!==Z.WHITE){const b=ns(rs,t,l,c);Hr(s,2,p,o,x,2,s.length/2-p,b)}else Ur(o,x,2,s.length/2-p);const m=kt.get(jn);m.indexOffset=d,m.indexSize=a.length-d,m.attributeOffset=p,m.attributeSize=s.length/2-p,m.baseColor=t.color,m.alpha=t.alpha,m.texture=_,m.geometryData=r,m.topology=g,n.push(m)})}function as(i){const t=[];for(let e=0;e<i.length;e++){const n=i[e].shape,r=[];Qt[n.type].build(n,r)&&t.push(r)}return t}class ls{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class hs{constructor(){this.instructions=new ri}init(t){this.batcher=new $r({maxTextures:t}),this.instructions.reset()}get geometry(){return D(si,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}destroy(){this.batcher.destroy(),this.instructions.destroy(),this.batcher=null,this.instructions=null}}const Te=class ge{constructor(t){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),this._renderer=t,t.renderableGC.addManagedHash(this,"_gpuContextHash"),t.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(t){ge.defaultOptions.bezierSmoothness=t?.bezierSmoothness??ge.defaultOptions.bezierSmoothness}getContextRenderData(t){return this._graphicsDataContextHash[t.uid]||this._initContextRenderData(t)}updateGpuContext(t){let e=this._gpuContextHash[t.uid]||this._initContext(t);if(t.dirty){e?this._cleanGraphicsContextData(t):e=this._initContext(t),ss(t,e);const n=t.batchMode;t.customShader||n==="no-batch"?e.isBatchable=!1:n==="auto"?e.isBatchable=e.geometryData.vertices.length<400:e.isBatchable=!0,t.dirty=!1}return e}getGpuContext(t){return this._gpuContextHash[t.uid]||this._initContext(t)}_initContextRenderData(t){const e=kt.get(hs,{maxTextures:this._renderer.limits.maxBatchableTextures}),{batches:n,geometryData:r}=this._gpuContextHash[t.uid],s=r.vertices.length,o=r.indices.length;for(let h=0;h<n.length;h++)n[h].applyTransform=!1;const a=e.batcher;a.ensureAttributeBuffer(s),a.ensureIndexBuffer(o),a.begin();for(let h=0;h<n.length;h++){const u=n[h];a.add(u)}a.finish(e.instructions);const l=a.geometry;l.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),l.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const c=a.batches;for(let h=0;h<c.length;h++){const u=c[h];u.bindGroup=Ki(u.textures.textures,u.textures.count,this._renderer.limits.maxBatchableTextures)}return this._graphicsDataContextHash[t.uid]=e,e}_initContext(t){const e=new ls;return e.context=t,this._gpuContextHash[t.uid]=e,t.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]}onGraphicsContextDestroy(t){this._cleanGraphicsContextData(t),t.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]=null}_cleanGraphicsContextData(t){const e=this._gpuContextHash[t.uid];e.isBatchable||this._graphicsDataContextHash[t.uid]&&(kt.return(this.getContextRenderData(t)),this._graphicsDataContextHash[t.uid]=null),e.batches&&e.batches.forEach(n=>{kt.return(n)})}destroy(){for(const t in this._gpuContextHash)this._gpuContextHash[t]&&this.onGraphicsContextDestroy(this._gpuContextHash[t].context)}};Te.extension={type:[Y.WebGLSystem,Y.WebGPUSystem,Y.CanvasSystem],name:"graphicsContext"};Te.defaultOptions={bezierSmoothness:.5};let Yn=Te;const cs=8,jt=11920929e-14,us=1;function Xn(i,t,e,n,r,s,o,a,l,c){const u=Math.min(.99,Math.max(0,c??Yn.defaultOptions.bezierSmoothness));let f=(us-u)/1;return f*=f,ds(t,e,n,r,s,o,a,l,i,f),i}function ds(i,t,e,n,r,s,o,a,l,c){me(i,t,e,n,r,s,o,a,l,c,0),l.push(o,a)}function me(i,t,e,n,r,s,o,a,l,c,h){if(h>cs)return;const u=(i+e)/2,f=(t+n)/2,d=(e+r)/2,p=(n+s)/2,g=(r+o)/2,x=(s+a)/2,_=(u+d)/2,m=(f+p)/2,b=(d+g)/2,y=(p+x)/2,P=(_+b)/2,w=(m+y)/2;if(h>0){let S=o-i,I=a-t;const k=Math.abs((e-o)*I-(n-a)*S),v=Math.abs((r-o)*I-(s-a)*S);if(k>jt&&v>jt){if((k+v)*(k+v)<=c*(S*S+I*I)){l.push(P,w);return}}else if(k>jt){if(k*k<=c*(S*S+I*I)){l.push(P,w);return}}else if(v>jt){if(v*v<=c*(S*S+I*I)){l.push(P,w);return}}else if(S=P-(i+o)/2,I=w-(t+a)/2,S*S+I*I<=c){l.push(P,w);return}}me(i,t,u,f,_,m,P,w,l,c,h+1),me(P,w,b,y,g,x,o,a,l,c,h+1)}const fs=8,ps=11920929e-14,xs=1;function gs(i,t,e,n,r,s,o,a){const c=Math.min(.99,Math.max(0,a??Yn.defaultOptions.bezierSmoothness));let h=(xs-c)/1;return h*=h,ms(t,e,n,r,s,o,i,h),i}function ms(i,t,e,n,r,s,o,a){ye(o,i,t,e,n,r,s,a,0),o.push(r,s)}function ye(i,t,e,n,r,s,o,a,l){if(l>fs)return;const c=(t+n)/2,h=(e+r)/2,u=(n+s)/2,f=(r+o)/2,d=(c+u)/2,p=(h+f)/2;let g=s-t,x=o-e;const _=Math.abs((n-s)*x-(r-o)*g);if(_>ps){if(_*_<=a*(g*g+x*x)){i.push(d,p);return}}else if(g=d-(t+s)/2,x=p-(e+o)/2,g*g+x*x<=a){i.push(d,p);return}ye(i,t,e,c,h,d,p,a,l+1),ye(i,d,p,u,f,s,o,a,l+1)}function Zn(i,t,e,n,r,s,o,a){let l=Math.abs(r-s);(!o&&r>s||o&&s>r)&&(l=2*Math.PI-l),a||(a=Math.max(6,Math.floor(6*Math.pow(n,1/3)*(l/Math.PI)))),a=Math.max(a,3);let c=l/a,h=r;c*=o?-1:1;for(let u=0;u<a+1;u++){const f=Math.cos(h),d=Math.sin(h),p=t+f*n,g=e+d*n;i.push(p,g),h+=c}}function ys(i,t,e,n,r,s){const o=i[i.length-2],l=i[i.length-1]-e,c=o-t,h=r-e,u=n-t,f=Math.abs(l*u-c*h);if(f<1e-8||s===0){(i[i.length-2]!==t||i[i.length-1]!==e)&&i.push(t,e);return}const d=l*l+c*c,p=h*h+u*u,g=l*h+c*u,x=s*Math.sqrt(d)/f,_=s*Math.sqrt(p)/f,m=x*g/d,b=_*g/p,y=x*u+_*c,P=x*h+_*l,w=c*(_+m),S=l*(_+m),I=u*(x+b),k=h*(x+b),v=Math.atan2(S-P,w-y),T=Math.atan2(k-P,I-y);Zn(i,y+t,P+e,s,v,T,c*h>u*l)}const It=Math.PI*2,he={centerX:0,centerY:0,ang1:0,ang2:0},ce=({x:i,y:t},e,n,r,s,o,a,l)=>{i*=e,t*=n;const c=r*i-s*t,h=s*i+r*t;return l.x=c+o,l.y=h+a,l};function _s(i,t){const e=t===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(t/4),n=t===1.5707963267948966?.551915024494:e,r=Math.cos(i),s=Math.sin(i),o=Math.cos(i+t),a=Math.sin(i+t);return[{x:r-s*n,y:s+r*n},{x:o+a*n,y:a-o*n},{x:o,y:a}]}const an=(i,t,e,n)=>{const r=i*n-t*e<0?-1:1;let s=i*e+t*n;return s>1&&(s=1),s<-1&&(s=-1),r*Math.acos(s)},bs=(i,t,e,n,r,s,o,a,l,c,h,u,f)=>{const d=Math.pow(r,2),p=Math.pow(s,2),g=Math.pow(h,2),x=Math.pow(u,2);let _=d*p-d*x-p*g;_<0&&(_=0),_/=d*x+p*g,_=Math.sqrt(_)*(o===a?-1:1);const m=_*r/s*u,b=_*-s/r*h,y=c*m-l*b+(i+e)/2,P=l*m+c*b+(t+n)/2,w=(h-m)/r,S=(u-b)/s,I=(-h-m)/r,k=(-u-b)/s,v=an(1,0,w,S);let T=an(w,S,I,k);a===0&&T>0&&(T-=It),a===1&&T<0&&(T+=It),f.centerX=y,f.centerY=P,f.ang1=v,f.ang2=T};function Ss(i,t,e,n,r,s,o,a=0,l=0,c=0){if(s===0||o===0)return;const h=Math.sin(a*It/360),u=Math.cos(a*It/360),f=u*(t-n)/2+h*(e-r)/2,d=-h*(t-n)/2+u*(e-r)/2;if(f===0&&d===0)return;s=Math.abs(s),o=Math.abs(o);const p=Math.pow(f,2)/Math.pow(s,2)+Math.pow(d,2)/Math.pow(o,2);p>1&&(s*=Math.sqrt(p),o*=Math.sqrt(p)),bs(t,e,n,r,s,o,l,c,h,u,f,d,he);let{ang1:g,ang2:x}=he;const{centerX:_,centerY:m}=he;let b=Math.abs(x)/(It/4);Math.abs(1-b)<1e-7&&(b=1);const y=Math.max(Math.ceil(b),1);x/=y;let P=i[i.length-2],w=i[i.length-1];const S={x:0,y:0};for(let I=0;I<y;I++){const k=_s(g,x),{x:v,y:T}=ce(k[0],s,o,u,h,_,m,S),{x:R,y:z}=ce(k[1],s,o,u,h,_,m,S),{x:E,y:ct}=ce(k[2],s,o,u,h,_,m,S);Xn(i,P,w,v,T,R,z,E,ct),P=E,w=ct,g+=x}}function ws(i,t,e){const n=(o,a)=>{const l=a.x-o.x,c=a.y-o.y,h=Math.sqrt(l*l+c*c),u=l/h,f=c/h;return{len:h,nx:u,ny:f}},r=(o,a)=>{o===0?i.moveTo(a.x,a.y):i.lineTo(a.x,a.y)};let s=t[t.length-1];for(let o=0;o<t.length;o++){const a=t[o%t.length],l=a.radius??e;if(l<=0){r(o,a),s=a;continue}const c=t[(o+1)%t.length],h=n(a,s),u=n(a,c);if(h.len<1e-4||u.len<1e-4){r(o,a),s=a;continue}let f=Math.asin(h.nx*u.ny-h.ny*u.nx),d=1,p=!1;h.nx*u.nx-h.ny*-u.ny<0?f<0?f=Math.PI+f:(f=Math.PI-f,d=-1,p=!0):f>0&&(d=-1,p=!0);const g=f/2;let x,_=Math.abs(Math.cos(g)*l/Math.sin(g));_>Math.min(h.len/2,u.len/2)?(_=Math.min(h.len/2,u.len/2),x=Math.abs(_*Math.sin(g)/Math.cos(g))):x=l;const m=a.x+u.nx*_+-u.ny*x*d,b=a.y+u.ny*_+u.nx*x*d,y=Math.atan2(h.ny,h.nx)+Math.PI/2*d,P=Math.atan2(u.ny,u.nx)-Math.PI/2*d;o===0&&i.moveTo(m+Math.cos(y)*x,b+Math.sin(y)*x),i.arc(m,b,x,y,P,p),s=a}}function Ps(i,t,e,n){const r=(a,l)=>Math.sqrt((a.x-l.x)**2+(a.y-l.y)**2),s=(a,l,c)=>({x:a.x+(l.x-a.x)*c,y:a.y+(l.y-a.y)*c}),o=t.length;for(let a=0;a<o;a++){const l=t[(a+1)%o],c=l.radius??e;if(c<=0){a===0?i.moveTo(l.x,l.y):i.lineTo(l.x,l.y);continue}const h=t[a],u=t[(a+2)%o],f=r(h,l);let d;if(f<1e-4)d=l;else{const x=Math.min(f/2,c);d=s(l,h,x/f)}const p=r(u,l);let g;if(p<1e-4)g=l;else{const x=Math.min(p/2,c);g=s(l,u,x/p)}a===0?i.moveTo(d.x,d.y):i.lineTo(d.x,d.y),i.quadraticCurveTo(l.x,l.y,g.x,g.y,n)}}const vs=new N;class Ms{constructor(t){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new Se,this._graphicsPath2D=t,this.signed=t.checkForHoles}moveTo(t,e){return this.startPoly(t,e),this}lineTo(t,e){this._ensurePoly();const n=this._currentPoly.points,r=n[n.length-2],s=n[n.length-1];return(r!==t||s!==e)&&n.push(t,e),this}arc(t,e,n,r,s,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Zn(a,t,e,n,r,s,o),this}arcTo(t,e,n,r,s){this._ensurePoly();const o=this._currentPoly.points;return ys(o,t,e,n,r,s),this}arcToSvg(t,e,n,r,s,o,a){const l=this._currentPoly.points;return Ss(l,this._currentPoly.lastX,this._currentPoly.lastY,o,a,t,e,n,r,s),this}bezierCurveTo(t,e,n,r,s,o,a){this._ensurePoly();const l=this._currentPoly;return Xn(this._currentPoly.points,l.lastX,l.lastY,t,e,n,r,s,o,a),this}quadraticCurveTo(t,e,n,r,s){this._ensurePoly();const o=this._currentPoly;return gs(this._currentPoly.points,o.lastX,o.lastY,t,e,n,r,s),this}closePath(){return this.endPoly(!0),this}addPath(t,e){this.endPoly(),e&&!e.isIdentity()&&(t=t.clone(!0),t.transform(e));const n=this.shapePrimitives,r=n.length;for(let s=0;s<t.instructions.length;s++){const o=t.instructions[s];this[o.action](...o.data)}if(t.checkForHoles&&n.length-r>1){let s=null;for(let o=r;o<n.length;o++){const a=n[o];if(a.shape.type==="polygon"){const l=a.shape,c=s?.shape;c&&c.containsPolygon(l)?(s.holes||(s.holes=[]),s.holes.push(a),n.copyWithin(o,o+1),n.length--,o--):s=a}}}return this}finish(t=!1){this.endPoly(t)}rect(t,e,n,r,s){return this.drawShape(new N(t,e,n,r),s),this}circle(t,e,n,r){return this.drawShape(new Me(t,e,n),r),this}poly(t,e,n){const r=new Tt(t);return r.closePath=e,this.drawShape(r,n),this}regularPoly(t,e,n,r,s=0,o){r=Math.max(r|0,3);const a=-1*Math.PI/2+s,l=Math.PI*2/r,c=[];for(let h=0;h<r;h++){const u=a-h*l;c.push(t+n*Math.cos(u),e+n*Math.sin(u))}return this.poly(c,!0,o),this}roundPoly(t,e,n,r,s,o=0,a){if(r=Math.max(r|0,3),s<=0)return this.regularPoly(t,e,n,r,o);const l=n*Math.sin(Math.PI/r)-.001;s=Math.min(s,l);const c=-1*Math.PI/2+o,h=Math.PI*2/r,u=(r-2)*Math.PI/r/2;for(let f=0;f<r;f++){const d=f*h+c,p=t+n*Math.cos(d),g=e+n*Math.sin(d),x=d+Math.PI+u,_=d-Math.PI-u,m=p+s*Math.cos(x),b=g+s*Math.sin(x),y=p+s*Math.cos(_),P=g+s*Math.sin(_);f===0?this.moveTo(m,b):this.lineTo(m,b),this.quadraticCurveTo(p,g,y,P,a)}return this.closePath()}roundShape(t,e,n=!1,r){return t.length<3?this:(n?Ps(this,t,e,r):ws(this,t,e),this.closePath())}filletRect(t,e,n,r,s){if(s===0)return this.rect(t,e,n,r);const o=Math.min(n,r)/2,a=Math.min(o,Math.max(-o,s)),l=t+n,c=e+r,h=a<0?-a:0,u=Math.abs(a);return this.moveTo(t,e+u).arcTo(t+h,e+h,t+u,e,u).lineTo(l-u,e).arcTo(l-h,e+h,l,e+u,u).lineTo(l,c-u).arcTo(l-h,c-h,t+n-u,c,u).lineTo(t+u,c).arcTo(t+h,c-h,t,c-u,u).closePath()}chamferRect(t,e,n,r,s,o){if(s<=0)return this.rect(t,e,n,r);const a=Math.min(s,Math.min(n,r)/2),l=t+n,c=e+r,h=[t+a,e,l-a,e,l,e+a,l,c-a,l-a,c,t+a,c,t,c-a,t,e+a];for(let u=h.length-1;u>=2;u-=2)h[u]===h[u-2]&&h[u-1]===h[u-3]&&h.splice(u-1,2);return this.poly(h,!0,o)}ellipse(t,e,n,r,s){return this.drawShape(new Ce(t,e,n,r),s),this}roundRect(t,e,n,r,s,o){return this.drawShape(new ke(t,e,n,r,s),o),this}drawShape(t,e){return this.endPoly(),this.shapePrimitives.push({shape:t,transform:e}),this}startPoly(t,e){let n=this._currentPoly;return n&&this.endPoly(),n=new Tt,n.points.push(t,e),this._currentPoly=n,this}endPoly(t=!1){const e=this._currentPoly;return e&&e.points.length>2&&(e.closePath=t,this.shapePrimitives.push({shape:e})),this._currentPoly=null,this}_ensurePoly(t=!0){if(!this._currentPoly&&(this._currentPoly=new Tt,t)){const e=this.shapePrimitives[this.shapePrimitives.length-1];if(e){let n=e.shape.x,r=e.shape.y;if(e.transform&&!e.transform.isIdentity()){const s=e.transform,o=n;n=s.a*n+s.c*r+s.tx,r=s.b*o+s.d*r+s.ty}this._currentPoly.points.push(n,r)}else this._currentPoly.points.push(0,0)}}buildPath(){const t=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let e=0;e<t.instructions.length;e++){const n=t.instructions[e];this[n.action](...n.data)}this.finish()}get bounds(){const t=this._bounds;t.clear();const e=this.shapePrimitives;for(let n=0;n<e.length;n++){const r=e[n],s=r.shape.getBounds(vs);r.transform?t.addRect(s,r.transform):t.addRect(s)}return t}}class X{constructor(t,e=!1){this.instructions=[],this.uid=W("graphicsPath"),this._dirty=!0,this.checkForHoles=e,typeof t=="string"?Oi(t,this):this.instructions=t?.slice()??[]}get shapePath(){return this._shapePath||(this._shapePath=new Ms(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(t,e){return t=t.clone(),this.instructions.push({action:"addPath",data:[t,e]}),this._dirty=!0,this}arc(...t){return this.instructions.push({action:"arc",data:t}),this._dirty=!0,this}arcTo(...t){return this.instructions.push({action:"arcTo",data:t}),this._dirty=!0,this}arcToSvg(...t){return this.instructions.push({action:"arcToSvg",data:t}),this._dirty=!0,this}bezierCurveTo(...t){return this.instructions.push({action:"bezierCurveTo",data:t}),this._dirty=!0,this}bezierCurveToShort(t,e,n,r,s){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(ft.shared);let l=0,c=0;if(!o||o.action!=="bezierCurveTo")l=a.x,c=a.y;else{l=o.data[2],c=o.data[3];const h=a.x,u=a.y;l=h+(h-l),c=u+(u-c)}return this.instructions.push({action:"bezierCurveTo",data:[l,c,t,e,n,r,s]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...t){return this.instructions.push({action:"ellipse",data:t}),this._dirty=!0,this}lineTo(...t){return this.instructions.push({action:"lineTo",data:t}),this._dirty=!0,this}moveTo(...t){return this.instructions.push({action:"moveTo",data:t}),this}quadraticCurveTo(...t){return this.instructions.push({action:"quadraticCurveTo",data:t}),this._dirty=!0,this}quadraticCurveToShort(t,e,n){const r=this.instructions[this.instructions.length-1],s=this.getLastPoint(ft.shared);let o=0,a=0;if(!r||r.action!=="quadraticCurveTo")o=s.x,a=s.y;else{o=r.data[0],a=r.data[1];const l=s.x,c=s.y;o=l+(l-o),a=c+(c-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,t,e,n]}),this._dirty=!0,this}rect(t,e,n,r,s){return this.instructions.push({action:"rect",data:[t,e,n,r,s]}),this._dirty=!0,this}circle(t,e,n,r){return this.instructions.push({action:"circle",data:[t,e,n,r]}),this._dirty=!0,this}roundRect(...t){return this.instructions.push({action:"roundRect",data:t}),this._dirty=!0,this}poly(...t){return this.instructions.push({action:"poly",data:t}),this._dirty=!0,this}regularPoly(...t){return this.instructions.push({action:"regularPoly",data:t}),this._dirty=!0,this}roundPoly(...t){return this.instructions.push({action:"roundPoly",data:t}),this._dirty=!0,this}roundShape(...t){return this.instructions.push({action:"roundShape",data:t}),this._dirty=!0,this}filletRect(...t){return this.instructions.push({action:"filletRect",data:t}),this._dirty=!0,this}chamferRect(...t){return this.instructions.push({action:"chamferRect",data:t}),this._dirty=!0,this}star(t,e,n,r,s,o,a){s||(s=r/2);const l=-1*Math.PI/2+o,c=n*2,h=Math.PI*2/c,u=[];for(let f=0;f<c;f++){const d=f%2?s:r,p=f*h+l;u.push(t+d*Math.cos(p),e+d*Math.sin(p))}return this.poly(u,!0,a),this}clone(t=!1){const e=new X;if(e.checkForHoles=this.checkForHoles,!t)e.instructions=this.instructions.slice();else for(let n=0;n<this.instructions.length;n++){const r=this.instructions[n];e.instructions.push({action:r.action,data:r.data.slice()})}return e}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(t){if(t.isIdentity())return this;const e=t.a,n=t.b,r=t.c,s=t.d,o=t.tx,a=t.ty;let l=0,c=0,h=0,u=0,f=0,d=0,p=0,g=0;for(let x=0;x<this.instructions.length;x++){const _=this.instructions[x],m=_.data;switch(_.action){case"moveTo":case"lineTo":l=m[0],c=m[1],m[0]=e*l+r*c+o,m[1]=n*l+s*c+a;break;case"bezierCurveTo":h=m[0],u=m[1],f=m[2],d=m[3],l=m[4],c=m[5],m[0]=e*h+r*u+o,m[1]=n*h+s*u+a,m[2]=e*f+r*d+o,m[3]=n*f+s*d+a,m[4]=e*l+r*c+o,m[5]=n*l+s*c+a;break;case"quadraticCurveTo":h=m[0],u=m[1],l=m[2],c=m[3],m[0]=e*h+r*u+o,m[1]=n*h+s*u+a,m[2]=e*l+r*c+o,m[3]=n*l+s*c+a;break;case"arcToSvg":l=m[5],c=m[6],p=m[0],g=m[1],m[0]=e*p+r*g,m[1]=n*p+s*g,m[5]=e*l+r*c+o,m[6]=n*l+s*c+a;break;case"circle":m[4]=vt(m[3],t);break;case"rect":m[4]=vt(m[4],t);break;case"ellipse":m[8]=vt(m[8],t);break;case"roundRect":m[5]=vt(m[5],t);break;case"addPath":m[0].transform(t);break;case"poly":m[2]=vt(m[2],t);break;default:ht("unknown transform action",_.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(t){let e=this.instructions.length-1,n=this.instructions[e];if(!n)return t.x=0,t.y=0,t;for(;n.action==="closePath";){if(e--,e<0)return t.x=0,t.y=0,t;n=this.instructions[e]}switch(n.action){case"moveTo":case"lineTo":t.x=n.data[0],t.y=n.data[1];break;case"quadraticCurveTo":t.x=n.data[2],t.y=n.data[3];break;case"bezierCurveTo":t.x=n.data[4],t.y=n.data[5];break;case"arc":case"arcToSvg":t.x=n.data[5],t.y=n.data[6];break;case"addPath":n.data[0].getLastPoint(t);break}return t}}function vt(i,t){return i?i.prepend(t):t.clone()}function F(i,t,e){const n=i.getAttribute(t);return n?Number(n):e}function Cs(i,t){const e=i.querySelectorAll("defs");for(let n=0;n<e.length;n++){const r=e[n];for(let s=0;s<r.children.length;s++){const o=r.children[s];switch(o.nodeName.toLowerCase()){case"lineargradient":t.defs[o.id]=ks(o);break;case"radialgradient":t.defs[o.id]=Ts();break}}}}function ks(i){const t=F(i,"x1",0),e=F(i,"y1",0),n=F(i,"x2",1),r=F(i,"y2",0),s=i.getAttribute("gradientUnits")||"objectBoundingBox",o=new K(t,e,n,r,s==="objectBoundingBox"?"local":"global");for(let a=0;a<i.children.length;a++){const l=i.children[a],c=F(l,"offset",0),h=L.shared.setValue(l.getAttribute("stop-color")).toNumber();o.addColorStop(c,h)}return o}function Ts(i){return ht("[SVG Parser] Radial gradients are not yet supported"),new K(0,0,1,0)}function ln(i){const t=i.match(/url\s*\(\s*['"]?\s*#([^'"\s)]+)\s*['"]?\s*\)/i);return t?t[1]:""}const hn={fill:{type:"paint",default:0},"fill-opacity":{type:"number",default:1},stroke:{type:"paint",default:0},"stroke-width":{type:"number",default:1},"stroke-opacity":{type:"number",default:1},"stroke-linecap":{type:"string",default:"butt"},"stroke-linejoin":{type:"string",default:"miter"},"stroke-miterlimit":{type:"number",default:10},"stroke-dasharray":{type:"string",default:"none"},"stroke-dashoffset":{type:"number",default:0},opacity:{type:"number",default:1}};function Kn(i,t){const e=i.getAttribute("style"),n={},r={},s={strokeStyle:n,fillStyle:r,useFill:!1,useStroke:!1};for(const o in hn){const a=i.getAttribute(o);a&&cn(t,s,o,a.trim())}if(e){const o=e.split(";");for(let a=0;a<o.length;a++){const l=o[a].trim(),[c,h]=l.split(":");hn[c]&&cn(t,s,c,h.trim())}}return{strokeStyle:s.useStroke?n:null,fillStyle:s.useFill?r:null,useFill:s.useFill,useStroke:s.useStroke}}function cn(i,t,e,n){switch(e){case"stroke":if(n!=="none"){if(n.startsWith("url(")){const r=ln(n);t.strokeStyle.fill=i.defs[r]}else t.strokeStyle.color=L.shared.setValue(n).toNumber();t.useStroke=!0}break;case"stroke-width":t.strokeStyle.width=Number(n);break;case"fill":if(n!=="none"){if(n.startsWith("url(")){const r=ln(n);t.fillStyle.fill=i.defs[r]}else t.fillStyle.color=L.shared.setValue(n).toNumber();t.useFill=!0}break;case"fill-opacity":t.fillStyle.alpha=Number(n);break;case"stroke-opacity":t.strokeStyle.alpha=Number(n);break;case"opacity":t.fillStyle.alpha=Number(n),t.strokeStyle.alpha=Number(n);break}}function As(i){if(i.length<=2)return!0;const t=i.map(a=>a.area).sort((a,l)=>l-a),[e,n]=t,r=t[t.length-1],s=e/n,o=n/r;return!(s>3&&o<2)}function Is(i){return i.split(/(?=[Mm])/).filter(n=>n.trim().length>0)}function Bs(i){const t=i.match(/[-+]?[0-9]*\.?[0-9]+/g);if(!t||t.length<4)return 0;const e=t.map(Number),n=[],r=[];for(let h=0;h<e.length;h+=2)h+1<e.length&&(n.push(e[h]),r.push(e[h+1]));if(n.length===0||r.length===0)return 0;const s=Math.min(...n),o=Math.max(...n),a=Math.min(...r),l=Math.max(...r);return(o-s)*(l-a)}function un(i,t){const e=new X(i,!1);for(const n of e.instructions)t.instructions.push(n)}function Rs(i,t){if(typeof i=="string"){const o=document.createElement("div");o.innerHTML=i.trim(),i=o.querySelector("svg")}const e={context:t,defs:{},path:new X};Cs(i,e);const n=i.children,{fillStyle:r,strokeStyle:s}=Kn(i,e);for(let o=0;o<n.length;o++){const a=n[o];a.nodeName.toLowerCase()!=="defs"&&Qn(a,e,r,s)}return t}function Qn(i,t,e,n){const r=i.children,{fillStyle:s,strokeStyle:o}=Kn(i,t);s&&e?e={...e,...s}:s&&(e=s),o&&n?n={...n,...o}:o&&(n=o);const a=!e&&!n;a&&(e={color:0});let l,c,h,u,f,d,p,g,x,_,m,b,y,P,w,S,I;switch(i.nodeName.toLowerCase()){case"path":{P=i.getAttribute("d");const k=i.getAttribute("fill-rule"),v=Is(P),T=k==="evenodd",R=v.length>1;if(T&&R){const E=v.map(M=>({path:M,area:Bs(M)}));if(E.sort((M,A)=>A.area-M.area),v.length>3||!As(E))for(let M=0;M<E.length;M++){const A=E[M],xt=M===0;t.context.beginPath();const B=new X(void 0,!0);un(A.path,B),t.context.path(B),xt?(e&&t.context.fill(e),n&&t.context.stroke(n)):t.context.cut()}else for(let M=0;M<E.length;M++){const A=E[M],xt=M%2===1;t.context.beginPath();const B=new X(void 0,!0);un(A.path,B),t.context.path(B),xt?t.context.cut():(e&&t.context.fill(e),n&&t.context.stroke(n))}}else{const E=k?k==="evenodd":!0;w=new X(P,E),t.context.path(w),e&&t.context.fill(e),n&&t.context.stroke(n)}break}case"circle":p=F(i,"cx",0),g=F(i,"cy",0),x=F(i,"r",0),t.context.ellipse(p,g,x,x),e&&t.context.fill(e),n&&t.context.stroke(n);break;case"rect":l=F(i,"x",0),c=F(i,"y",0),S=F(i,"width",0),I=F(i,"height",0),_=F(i,"rx",0),m=F(i,"ry",0),_||m?t.context.roundRect(l,c,S,I,_||m):t.context.rect(l,c,S,I),e&&t.context.fill(e),n&&t.context.stroke(n);break;case"ellipse":p=F(i,"cx",0),g=F(i,"cy",0),_=F(i,"rx",0),m=F(i,"ry",0),t.context.beginPath(),t.context.ellipse(p,g,_,m),e&&t.context.fill(e),n&&t.context.stroke(n);break;case"line":h=F(i,"x1",0),u=F(i,"y1",0),f=F(i,"x2",0),d=F(i,"y2",0),t.context.beginPath(),t.context.moveTo(h,u),t.context.lineTo(f,d),n&&t.context.stroke(n);break;case"polygon":y=i.getAttribute("points"),b=y.match(/\d+/g).map(k=>parseInt(k,10)),t.context.poly(b,!0),e&&t.context.fill(e),n&&t.context.stroke(n);break;case"polyline":y=i.getAttribute("points"),b=y.match(/\d+/g).map(k=>parseInt(k,10)),t.context.poly(b,!1),n&&t.context.stroke(n);break;case"g":case"svg":break;default:{ht(`[SVG parser] <${i.nodeName}> elements unsupported`);break}}a&&(e=null);for(let k=0;k<r.length;k++)Qn(r[k],t,e,n)}function zs(i){return L.isColorLike(i)}function dn(i){return i instanceof Kt}function fn(i){return i instanceof K}function Es(i){return i instanceof Z}function Gs(i,t,e){const n=L.shared.setValue(t??0);return i.color=n.toNumber(),i.alpha=n.alpha===1?e.alpha:n.alpha,i.texture=Z.WHITE,{...e,...i}}function Fs(i,t,e){return i.texture=t,{...e,...i}}function pn(i,t,e){return i.fill=t,i.color=16777215,i.texture=t.texture,i.matrix=t.transform,{...e,...i}}function xn(i,t,e){return t.buildGradient(),i.fill=t,i.color=16777215,i.texture=t.texture,i.matrix=t.transform,i.textureSpace=t.textureSpace,{...e,...i}}function Ds(i,t){const e={...t,...i},n=L.shared.setValue(e.color);return e.alpha*=n.alpha,e.color=n.toNumber(),e}function ot(i,t){if(i==null)return null;const e={},n=i;return zs(i)?Gs(e,i,t):Es(i)?Fs(e,i,t):dn(i)?pn(e,i,t):fn(i)?xn(e,i,t):n.fill&&dn(n.fill)?pn(n,n.fill,t):n.fill&&fn(n.fill)?xn(n,n.fill,t):Ds(n,t)}function Xt(i,t){const{width:e,alignment:n,miterLimit:r,cap:s,join:o,pixelLine:a,...l}=t,c=ot(i,l);return c?{width:e,alignment:n,miterLimit:r,cap:s,join:o,pixelLine:a,...c}:null}const Ls=new ft,gn=new H,Ae=class q extends Ft{constructor(){super(...arguments),this.uid=W("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new X,this._transform=new H,this._fillStyle={...q.defaultFillStyle},this._strokeStyle={...q.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new Se,this._boundsDirty=!0}clone(){const t=new q;return t.batchMode=this.batchMode,t.instructions=this.instructions.slice(),t._activePath=this._activePath.clone(),t._transform=this._transform.clone(),t._fillStyle={...this._fillStyle},t._strokeStyle={...this._strokeStyle},t._stateStack=this._stateStack.slice(),t._bounds=this._bounds.clone(),t._boundsDirty=!0,t}get fillStyle(){return this._fillStyle}set fillStyle(t){this._fillStyle=ot(t,q.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(t){this._strokeStyle=Xt(t,q.defaultStrokeStyle)}setFillStyle(t){return this._fillStyle=ot(t,q.defaultFillStyle),this}setStrokeStyle(t){return this._strokeStyle=ot(t,q.defaultStrokeStyle),this}texture(t,e,n,r,s,o){return this.instructions.push({action:"texture",data:{image:t,dx:n||0,dy:r||0,dw:s||t.frame.width,dh:o||t.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:e?L.shared.setValue(e).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new X,this}fill(t,e){let n;const r=this.instructions[this.instructions.length-1];return this._tick===0&&r&&r.action==="stroke"?n=r.data.path:n=this._activePath.clone(),n?(t!=null&&(e!==void 0&&typeof t=="number"&&(D($,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),t={color:t,alpha:e}),this._fillStyle=ot(t,q.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:n}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:t,y:e}=this._activePath.getLastPoint(ft.shared);this._activePath.clear(),this._activePath.moveTo(t,e)}stroke(t){let e;const n=this.instructions[this.instructions.length-1];return this._tick===0&&n&&n.action==="fill"?e=n.data.path:e=this._activePath.clone(),e?(t!=null&&(this._strokeStyle=Xt(t,q.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:e}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let t=0;t<2;t++){const e=this.instructions[this.instructions.length-1-t],n=this._activePath.clone();if(e&&(e.action==="stroke"||e.action==="fill"))if(e.data.hole)e.data.hole.addPath(n);else{e.data.hole=n;break}}return this._initNextPathLocation(),this}arc(t,e,n,r,s,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*t+a.c*e+a.tx,a.b*t+a.d*e+a.ty,n,r,s,o),this}arcTo(t,e,n,r,s){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*n+o.c*r+o.tx,o.b*n+o.d*r+o.ty,s),this}arcToSvg(t,e,n,r,s,o,a){this._tick++;const l=this._transform;return this._activePath.arcToSvg(t,e,n,r,s,l.a*o+l.c*a+l.tx,l.b*o+l.d*a+l.ty),this}bezierCurveTo(t,e,n,r,s,o,a){this._tick++;const l=this._transform;return this._activePath.bezierCurveTo(l.a*t+l.c*e+l.tx,l.b*t+l.d*e+l.ty,l.a*n+l.c*r+l.tx,l.b*n+l.d*r+l.ty,l.a*s+l.c*o+l.tx,l.b*s+l.d*o+l.ty,a),this}closePath(){return this._tick++,this._activePath?.closePath(),this}ellipse(t,e,n,r){return this._tick++,this._activePath.ellipse(t,e,n,r,this._transform.clone()),this}circle(t,e,n){return this._tick++,this._activePath.circle(t,e,n,this._transform.clone()),this}path(t){return this._tick++,this._activePath.addPath(t,this._transform.clone()),this}lineTo(t,e){this._tick++;const n=this._transform;return this._activePath.lineTo(n.a*t+n.c*e+n.tx,n.b*t+n.d*e+n.ty),this}moveTo(t,e){this._tick++;const n=this._transform,r=this._activePath.instructions,s=n.a*t+n.c*e+n.tx,o=n.b*t+n.d*e+n.ty;return r.length===1&&r[0].action==="moveTo"?(r[0].data[0]=s,r[0].data[1]=o,this):(this._activePath.moveTo(s,o),this)}quadraticCurveTo(t,e,n,r,s){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*n+o.c*r+o.tx,o.b*n+o.d*r+o.ty,s),this}rect(t,e,n,r){return this._tick++,this._activePath.rect(t,e,n,r,this._transform.clone()),this}roundRect(t,e,n,r,s){return this._tick++,this._activePath.roundRect(t,e,n,r,s,this._transform.clone()),this}poly(t,e){return this._tick++,this._activePath.poly(t,e,this._transform.clone()),this}regularPoly(t,e,n,r,s=0,o){return this._tick++,this._activePath.regularPoly(t,e,n,r,s,o),this}roundPoly(t,e,n,r,s,o){return this._tick++,this._activePath.roundPoly(t,e,n,r,s,o),this}roundShape(t,e,n,r){return this._tick++,this._activePath.roundShape(t,e,n,r),this}filletRect(t,e,n,r,s){return this._tick++,this._activePath.filletRect(t,e,n,r,s),this}chamferRect(t,e,n,r,s,o){return this._tick++,this._activePath.chamferRect(t,e,n,r,s,o),this}star(t,e,n,r,s=0,o=0){return this._tick++,this._activePath.star(t,e,n,r,s,o,this._transform.clone()),this}svg(t){return this._tick++,Rs(t,this),this}restore(){const t=this._stateStack.pop();return t&&(this._transform=t.transform,this._fillStyle=t.fillStyle,this._strokeStyle=t.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(t){return this._transform.rotate(t),this}scale(t,e=t){return this._transform.scale(t,e),this}setTransform(t,e,n,r,s,o){return t instanceof H?(this._transform.set(t.a,t.b,t.c,t.d,t.tx,t.ty),this):(this._transform.set(t,e,n,r,s,o),this)}transform(t,e,n,r,s,o){return t instanceof H?(this._transform.append(t),this):(gn.set(t,e,n,r,s,o),this._transform.append(gn),this)}translate(t,e=t){return this._transform.translate(t,e),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this._boundsDirty=!0,!this.dirty&&(this.emit("update",this,16),this.dirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;this._boundsDirty=!1;const t=this._bounds;t.clear();for(let e=0;e<this.instructions.length;e++){const n=this.instructions[e],r=n.action;if(r==="fill"){const s=n.data;t.addBounds(s.path.bounds)}else if(r==="texture"){const s=n.data;t.addFrame(s.dx,s.dy,s.dx+s.dw,s.dy+s.dh,s.transform)}if(r==="stroke"){const s=n.data,o=s.style.alignment,a=s.style.width*(1-o),l=s.path.bounds;t.addFrame(l.minX-a,l.minY-a,l.maxX+a,l.maxY+a)}}return t}containsPoint(t){if(!this.bounds.containsPoint(t.x,t.y))return!1;const e=this.instructions;let n=!1;for(let r=0;r<e.length;r++){const s=e[r],o=s.data,a=o.path;if(!s.action||!a)continue;const l=o.style,c=a.shapePath.shapePrimitives;for(let h=0;h<c.length;h++){const u=c[h].shape;if(!l||!u)continue;const f=c[h].transform,d=f?f.applyInverse(t,Ls):t;if(s.action==="fill")n=u.contains(d.x,d.y);else{const g=l;n=u.strokeContains(d.x,d.y,g.width,g.alignment)}const p=o.hole;if(p){const g=p.shapePath?.shapePrimitives;if(g)for(let x=0;x<g.length;x++)g[x].shape.contains(d.x,d.y)&&(n=!1)}if(n)return!0}}return n}destroy(t=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof t=="boolean"?t:t?.texture){const n=typeof t=="boolean"?t:t?.textureSource;this._fillStyle.texture&&(this._fillStyle.fill&&"uid"in this._fillStyle.fill?this._fillStyle.fill.destroy():this._fillStyle.texture.destroy(n)),this._strokeStyle.texture&&(this._strokeStyle.fill&&"uid"in this._strokeStyle.fill?this._strokeStyle.fill.destroy():this._strokeStyle.texture.destroy(n))}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};Ae.defaultFillStyle={color:16777215,alpha:1,texture:Z.WHITE,matrix:null,fill:null,textureSpace:"local"};Ae.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:Z.WHITE,matrix:null,fill:null,textureSpace:"local",pixelLine:!1};let U=Ae;const Ie=class dt extends Ft{constructor(t={}){super(),this.uid=W("textStyle"),this._tick=0,Ws(t);const e={...dt.defaultTextStyle,...t};for(const n in e){const r=n;this[r]=e[n]}this.update(),this._tick=0}get align(){return this._align}set align(t){this._align=t,this.update()}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords=t,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(t){t!==null&&typeof t=="object"?this._dropShadow=this._createProxy({...dt.defaultDropShadow,...t}):this._dropShadow=t?this._createProxy({...dt.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(t){this._fontFamily=t,this.update()}get fontSize(){return this._fontSize}set fontSize(t){typeof t=="string"?this._fontSize=parseInt(t,10):this._fontSize=t,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle=t.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant=t,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight=t,this.update()}get leading(){return this._leading}set leading(t){this._leading=t,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing=t,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight=t,this.update()}get padding(){return this._padding}set padding(t){this._padding=t,this.update()}get filters(){return this._filters}set filters(t){this._filters=Object.freeze(t),this.update()}get trim(){return this._trim}set trim(t){this._trim=t,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline=t,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace=t,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap=t,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth=t,this.update()}get fill(){return this._originalFill}set fill(t){t!==this._originalFill&&(this._originalFill=t,this._isFillStyle(t)&&(this._originalFill=this._createProxy({...U.defaultFillStyle,...t},()=>{this._fill=ot({...this._originalFill},U.defaultFillStyle)})),this._fill=ot(t===0?"black":t,U.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(t){t!==this._originalStroke&&(this._originalStroke=t,this._isFillStyle(t)&&(this._originalStroke=this._createProxy({...U.defaultStrokeStyle,...t},()=>{this._stroke=Xt({...this._originalStroke},U.defaultStrokeStyle)})),this._stroke=Xt(t,U.defaultStrokeStyle),this.update())}update(){this._tick++,this.emit("update",this)}reset(){const t=dt.defaultTextStyle;for(const e in t)this[e]=t[e]}get styleKey(){return`${this.uid}-${this._tick}`}clone(){return new dt({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,filters:this._filters?[...this._filters]:void 0})}_getFinalPadding(){let t=0;if(this._filters)for(let e=0;e<this._filters.length;e++)t+=this._filters[e].padding;return Math.max(this._padding,t)}destroy(t=!1){if(this.removeAllListeners(),typeof t=="boolean"?t:t?.texture){const n=typeof t=="boolean"?t:t?.textureSource;this._fill?.texture&&this._fill.texture.destroy(n),this._originalFill?.texture&&this._originalFill.texture.destroy(n),this._stroke?.texture&&this._stroke.texture.destroy(n),this._originalStroke?.texture&&this._originalStroke.texture.destroy(n)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(t,e){return new Proxy(t,{set:(n,r,s)=>(n[r]=s,e?.(r,s),this.update(),!0)})}_isFillStyle(t){return(t??null)!==null&&!(L.isColorLike(t)||t instanceof K||t instanceof Kt)}};Ie.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};Ie.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let Vs=Ie;function Ws(i){const t=i;if(typeof t.dropShadow=="boolean"&&t.dropShadow){const e=Vs.defaultDropShadow;i.dropShadow={alpha:t.dropShadowAlpha??e.alpha,angle:t.dropShadowAngle??e.angle,blur:t.dropShadowBlur??e.blur,color:t.dropShadowColor??e.color,distance:t.dropShadowDistance??e.distance}}if(t.strokeThickness!==void 0){D($,"strokeThickness is now a part of stroke");const e=t.stroke;let n={};if(L.isColorLike(e))n.color=e;else if(e instanceof K||e instanceof Kt)n.fill=e;else if(Object.hasOwnProperty.call(e,"color")||Object.hasOwnProperty.call(e,"fill"))n=e;else throw new Error("Invalid stroke value.");i.stroke={...n,width:t.strokeThickness}}if(Array.isArray(t.fillGradientStops)){if(D($,"gradient fill is now a fill pattern: `new FillGradient(...)`"),!Array.isArray(t.fill)||t.fill.length===0)throw new Error("Invalid fill value. Expected an array of colors for gradient fill.");t.fill.length!==t.fillGradientStops.length&&ht("The number of fill colors must match the number of fill gradient stops.");const e=new K({start:{x:0,y:0},end:{x:0,y:1},textureSpace:"local"}),n=t.fillGradientStops.slice(),r=t.fill.map(s=>L.shared.setValue(s).toNumber());n.forEach((s,o)=>{e.addColorStop(s,r[o])}),i.fill={fill:e}}}class $s{constructor(t){this._canvasPool=Object.create(null),this.canvasOptions=t||{},this.enableFullScreen=!1}_createCanvasAndContext(t,e){const n=at.get().createCanvas();n.width=t,n.height=e;const r=n.getContext("2d");return{canvas:n,context:r}}getOptimalCanvasAndContext(t,e,n=1){t=Math.ceil(t*n-1e-6),e=Math.ceil(e*n-1e-6),t=Ot(t),e=Ot(e);const r=(t<<17)+(e<<1);this._canvasPool[r]||(this._canvasPool[r]=[]);let s=this._canvasPool[r].pop();return s||(s=this._createCanvasAndContext(t,e)),s}returnCanvasAndContext(t){const e=t.canvas,{width:n,height:r}=e,s=(n<<17)+(r<<1);t.context.resetTransform(),t.context.clearRect(0,0,n,r),this._canvasPool[s].push(t)}clear(){this._canvasPool={}}}const _e=new $s;wn.register(_e);const mn=1e5;function yn(i,t,e,n=0){if(i.texture===Z.WHITE&&!i.fill)return L.shared.setValue(i.color).setAlpha(i.alpha??1).toHexa();if(i.fill){if(i.fill instanceof Kt){const r=i.fill,s=t.createPattern(r.texture.source.resource,"repeat"),o=r.transform.copyTo(H.shared);return o.scale(r.texture.frame.width,r.texture.frame.height),s.setTransform(o),s}else if(i.fill instanceof K){const r=i.fill,s=r.type==="linear",o=r.textureSpace==="local";let a=1,l=1;o&&e&&(a=e.width+n,l=e.height+n);let c,h=!1;if(s){const{start:u,end:f}=r;c=t.createLinearGradient(u.x*a,u.y*l,f.x*a,f.y*l),h=Math.abs(f.x-u.x)<Math.abs((f.y-u.y)*.1)}else{const{center:u,innerRadius:f,outerCenter:d,outerRadius:p}=r;c=t.createRadialGradient(u.x*a,u.y*l,f*a,d.x*a,d.y*l,p*a)}if(h&&o&&e){const u=e.lineHeight/l;for(let f=0;f<e.lines.length;f++){const d=(f*e.lineHeight+n/2)/l;r.colorStops.forEach(p=>{const g=d+p.offset*u;c.addColorStop(Math.floor(g*mn)/mn,L.shared.setValue(p.color).toHex())})}}else r.colorStops.forEach(u=>{c.addColorStop(u.offset,L.shared.setValue(u.color).toHex())});return c}}else{const r=t.createPattern(i.texture.source.resource,"repeat"),s=i.matrix.copyTo(H.shared);return s.scale(i.texture.frame.width,i.texture.frame.height),r.setTransform(s),r}return ht("FillStyle not recognised",i),"red"}class be extends oi{constructor(t){t instanceof U&&(t={context:t});const{context:e,roundPixels:n,...r}=t||{};super({label:"Graphics",...r}),this.renderPipeId="graphics",e?this._context=e:this._context=this._ownedContext=new U,this._context.on("update",this.onViewUpdate,this),this.didViewUpdate=!0,this.allowChildren=!1,this.roundPixels=n??!1}set context(t){t!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=t,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(t){return this._context.containsPoint(t)}destroy(t){this._ownedContext&&!t?this._ownedContext.destroy(t):(t===!0||t?.context===!0)&&this._context.destroy(t),this._ownedContext=null,this._context=null,super.destroy(t)}_callContextMethod(t,e){return this.context[t](...e),this}setFillStyle(...t){return this._callContextMethod("setFillStyle",t)}setStrokeStyle(...t){return this._callContextMethod("setStrokeStyle",t)}fill(...t){return this._callContextMethod("fill",t)}stroke(...t){return this._callContextMethod("stroke",t)}texture(...t){return this._callContextMethod("texture",t)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...t){return this._callContextMethod("arc",t)}arcTo(...t){return this._callContextMethod("arcTo",t)}arcToSvg(...t){return this._callContextMethod("arcToSvg",t)}bezierCurveTo(...t){return this._callContextMethod("bezierCurveTo",t)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...t){return this._callContextMethod("ellipse",t)}circle(...t){return this._callContextMethod("circle",t)}path(...t){return this._callContextMethod("path",t)}lineTo(...t){return this._callContextMethod("lineTo",t)}moveTo(...t){return this._callContextMethod("moveTo",t)}quadraticCurveTo(...t){return this._callContextMethod("quadraticCurveTo",t)}rect(...t){return this._callContextMethod("rect",t)}roundRect(...t){return this._callContextMethod("roundRect",t)}poly(...t){return this._callContextMethod("poly",t)}regularPoly(...t){return this._callContextMethod("regularPoly",t)}roundPoly(...t){return this._callContextMethod("roundPoly",t)}roundShape(...t){return this._callContextMethod("roundShape",t)}filletRect(...t){return this._callContextMethod("filletRect",t)}chamferRect(...t){return this._callContextMethod("chamferRect",t)}star(...t){return this._callContextMethod("star",t)}svg(...t){return this._callContextMethod("svg",t)}restore(...t){return this._callContextMethod("restore",t)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...t){return this._callContextMethod("rotate",t)}scaleTransform(...t){return this._callContextMethod("scale",t)}setTransform(...t){return this._callContextMethod("setTransform",t)}transform(...t){return this._callContextMethod("transform",t)}translateTransform(...t){return this._callContextMethod("translate",t)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(t){this._context.fillStyle=t}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(t){this._context.strokeStyle=t}clone(t=!1){return t?new be(this._context.clone()):(this._ownedContext=null,new be(this._context))}lineStyle(t,e,n){D($,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const r={};return t&&(r.width=t),e&&(r.color=e),n&&(r.alpha=n),this.context.strokeStyle=r,this}beginFill(t,e){D($,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const n={};return t!==void 0&&(n.color=t),e!==void 0&&(n.alpha=e),this.context.fillStyle=n,this}endFill(){D($,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const t=this.context.strokeStyle;return(t.width!==U.defaultStrokeStyle.width||t.color!==U.defaultStrokeStyle.color||t.alpha!==U.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...t){return D($,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",t)}drawEllipse(...t){return D($,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",t)}drawPolygon(...t){return D($,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",t)}drawRect(...t){return D($,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",t)}drawRoundedRect(...t){return D($,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",t)}drawStar(...t){return D($,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",t)}}let st=null,O=null;function Hs(i,t){st||(st=at.get().createCanvas(256,128),O=st.getContext("2d",{willReadFrequently:!0}),O.globalCompositeOperation="copy",O.globalAlpha=1),(st.width<i||st.height<t)&&(st.width=Ot(i),st.height=Ot(t))}function _n(i,t,e){for(let n=0,r=4*e*t;n<t;++n,r+=4)if(i[r+3]!==0)return!1;return!0}function bn(i,t,e,n,r){const s=4*t;for(let o=n,a=n*s+4*e;o<=r;++o,a+=s)if(i[a+3]!==0)return!1;return!0}function Us(...i){let t=i[0];t.canvas||(t={canvas:i[0],resolution:i[1]});const{canvas:e}=t,n=Math.min(t.resolution??1,1),r=t.width??e.width,s=t.height??e.height;let o=t.output;if(Hs(r,s),!O)throw new TypeError("Failed to get canvas 2D context");O.drawImage(e,0,0,r,s,0,0,r*n,s*n);const l=O.getImageData(0,0,r,s).data;let c=0,h=0,u=r-1,f=s-1;for(;h<s&&_n(l,r,h);)++h;if(h===s)return N.EMPTY;for(;_n(l,r,f);)--f;for(;bn(l,r,c,h,f);)++c;for(;bn(l,r,u,h,f);)--u;return++u,++f,O.globalCompositeOperation="source-over",O.strokeRect(c,h,u-c,f-h),O.globalCompositeOperation="copy",o??(o=new N),o.set(c/n,h/n,(u-c)/n,(f-h)/n),o}const Sn=new N;class Ns{getCanvasAndContext(t){const{text:e,style:n,resolution:r=1}=t,s=n._getFinalPadding(),o=wt.measureText(e||" ",n),a=Math.ceil(Math.ceil(Math.max(1,o.width)+s*2)*r),l=Math.ceil(Math.ceil(Math.max(1,o.height)+s*2)*r),c=_e.getOptimalCanvasAndContext(a,l);this._renderTextToCanvas(e,n,s,r,c);const h=n.trim?Us({canvas:c.canvas,width:a,height:l,resolution:1,output:Sn}):Sn.set(0,0,a,l);return{canvasAndContext:c,frame:h}}returnCanvasAndContext(t){_e.returnCanvasAndContext(t)}_renderTextToCanvas(t,e,n,r,s){const{canvas:o,context:a}=s,l=Gn(e),c=wt.measureText(t||" ",e),h=c.lines,u=c.lineHeight,f=c.lineWidths,d=c.maxLineWidth,p=c.fontProperties,g=o.height;if(a.resetTransform(),a.scale(r,r),a.textBaseline=e.textBaseline,e._stroke?.width){const b=e._stroke;a.lineWidth=b.width,a.miterLimit=b.miterLimit,a.lineJoin=b.join,a.lineCap=b.cap}a.font=l;let x,_;const m=e.dropShadow?2:1;for(let b=0;b<m;++b){const y=e.dropShadow&&b===0,P=y?Math.ceil(Math.max(1,g)+n*2):0,w=P*r;if(y){a.fillStyle="black",a.strokeStyle="black";const k=e.dropShadow,v=k.color,T=k.alpha;a.shadowColor=L.shared.setValue(v).setAlpha(T).toRgbaString();const R=k.blur*r,z=k.distance*r;a.shadowBlur=R,a.shadowOffsetX=Math.cos(k.angle)*z,a.shadowOffsetY=Math.sin(k.angle)*z+w}else{if(a.fillStyle=e._fill?yn(e._fill,a,c,n*2):null,e._stroke?.width){const k=e._stroke.width*.5+n*2;a.strokeStyle=yn(e._stroke,a,c,k)}a.shadowColor="black"}let S=(u-p.fontSize)/2;u-p.fontSize<0&&(S=0);const I=e._stroke?.width??0;for(let k=0;k<h.length;k++)x=I/2,_=I/2+k*u+p.ascent+S,e.align==="right"?x+=d-f[k]:e.align==="center"&&(x+=(d-f[k])/2),e._stroke?.width&&this._drawLetterSpacing(h[k],e,s,x+n,_+n-P,!0),e._fill!==void 0&&this._drawLetterSpacing(h[k],e,s,x+n,_+n-P)}}_drawLetterSpacing(t,e,n,r,s,o=!1){const{context:a}=n,l=e.letterSpacing;let c=!1;if(wt.experimentalLetterSpacingSupported&&(wt.experimentalLetterSpacing?(a.letterSpacing=`${l}px`,a.textLetterSpacing=`${l}px`,c=!0):(a.letterSpacing="0px",a.textLetterSpacing="0px")),l===0||c){o?a.strokeText(t,r,s):a.fillText(t,r,s);return}let h=r;const u=wt.graphemeSegmenter(t);let f=a.measureText(t).width,d=0;for(let p=0;p<u.length;++p){const g=u[p];o?a.strokeText(g,h,s):a.fillText(g,h,s);let x="";for(let _=p+1;_<u.length;++_)x+=u[_];d=a.measureText(x).width,h+=f-d+l,f=d}}}const Os=new Ns;export{Oe as A,V as B,_e as C,$r as D,Yn as E,Zt as G,de as R,tr as S,Vs as T,In as U,je as V,Et as a,qt as b,we as c,Tr as d,Ir as e,qe as f,Ki as g,Er as h,Pe as i,Cn as j,xi as k,cr as l,ir as m,Ar as n,Br as o,Fr as p,Lr as q,Dr as r,Vr as s,Os as t,wt as u,be as v,Gn as w,yn as x,$i as y,jn as z};
