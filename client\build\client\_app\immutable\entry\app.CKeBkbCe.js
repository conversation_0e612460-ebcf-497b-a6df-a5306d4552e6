const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../nodes/0.Cpsq4hmd.js","../chunks/DsnmJJEf.js","../chunks/4UAai7vz.js","../assets/0.PIix4Fxn.css","../nodes/1.BGymq15E.js","../chunks/C1M19Mmo.js","../chunks/DJNDnN69.js","../chunks/mXOxeudE.js","../chunks/D6Z45t_z.js","../nodes/2.DL9bb4bx.js","../chunks/DMnCbMI3.js","../chunks/BwME0dYm.js","../assets/MumsNumbers.yT0SHS_s.css","../assets/2.BN6ceEAQ.css","../nodes/3.CQxkl77G.js","../chunks/DHzIY8Hm.js","../chunks/BlcsiWND.js"])))=>i.map(i=>d[i]);
import{p as R,i as O,b as p,_ as E}from"../chunks/DMnCbMI3.js";import{A as L,B,z as G,E as J,I as K,J as Y,K as q,L as H,O as Q,N as U,v as P,a9 as W,k as f,aa as X,V as Z,x as $,p as tt,u as et,g as rt,w as k,ab as at,f as D,a as x,s as st,b as m,c as nt,o as A,d as ot,r as ct,ac as w,ad as it,t as ut}from"../chunks/4UAai7vz.js";import{b as ft,m as lt,u as dt,o as mt,s as _t}from"../chunks/DJNDnN69.js";import"../chunks/DsnmJJEf.js";function T(c,t,a){L&&B();var n=c,s,r,e=null,o=null;function y(){r&&(U(r),r=null),e&&(e.lastChild.remove(),n.before(e),e=null),r=o,o=null}G(()=>{if(s!==(s=t())){var _=H();if(s){var h=n;_&&(e=document.createDocumentFragment(),e.append(h=K())),o=Y(()=>a(h,s))}_?q.add_callback(y):y()}},J),L&&(n=Q)}function ht(c){return class extends vt{constructor(t){super({component:c,...t})}}}class vt{#e;#t;constructor(t){var a=new Map,n=(r,e)=>{var o=$(e,!1,!1);return a.set(r,o),o};const s=new Proxy({...t.props||{},$$events:{}},{get(r,e){return f(a.get(e)??n(e,Reflect.get(r,e)))},has(r,e){return e===W?!0:(f(a.get(e)??n(e,Reflect.get(r,e))),Reflect.has(r,e))},set(r,e,o){return P(a.get(e)??n(e,o),o),Reflect.set(r,e,o)}});this.#t=(t.hydrate?ft:lt)(t.component,{target:t.target,anchor:t.anchor,props:s,context:t.context,intro:t.intro??!1,recover:t.recover}),(!t?.props?.$$host||t.sync===!1)&&X(),this.#e=s.$$events;for(const r of Object.keys(this.#t))r==="$set"||r==="$destroy"||r==="$on"||Z(this,r,{get(){return this.#t[r]},set(e){this.#t[r]=e},enumerable:!0});this.#t.$set=r=>{Object.assign(s,r)},this.#t.$destroy=()=>{dt(this.#t)}}$set(t){this.#t.$set(t)}$on(t,a){this.#e[t]=this.#e[t]||[];const n=(...s)=>a.call(this,...s);return this.#e[t].push(n),()=>{this.#e[t]=this.#e[t].filter(s=>s!==n)}}$destroy(){this.#t.$destroy()}}const At={};var gt=D('<div id="svelte-announcer" aria-live="assertive" aria-atomic="true" style="position: absolute; left: 0; top: 0; clip: rect(0 0 0 0); clip-path: inset(50%); overflow: hidden; white-space: nowrap; width: 1px; height: 1px"><!></div>'),bt=D("<!> <!>",1);function yt(c,t){tt(t,!0);let a=R(t,"components",23,()=>[]),n=R(t,"data_0",3,null),s=R(t,"data_1",3,null);et(()=>t.stores.page.set(t.page)),rt(()=>{t.stores,t.page,t.constructors,a(),t.form,n(),s(),t.stores.page.notify()});let r=k(!1),e=k(!1),o=k(null);mt(()=>{const i=t.stores.page.subscribe(()=>{f(r)&&(P(e,!0),at().then(()=>{P(o,document.title||"untitled page",!0)}))});return P(r,!0),i});const y=w(()=>t.constructors[1]);var _=bt(),h=x(_);{var I=i=>{var u=A();const v=w(()=>t.constructors[0]);var g=x(u);T(g,()=>f(v),(l,d)=>{p(d(l,{get data(){return n()},get form(){return t.form},get params(){return t.page.params},children:(b,Pt)=>{var C=A(),F=x(C);T(F,()=>f(y),(N,M)=>{p(M(N,{get data(){return s()},get form(){return t.form},get params(){return t.page.params}}),z=>a()[1]=z,()=>a()?.[1])}),m(b,C)},$$slots:{default:!0}}),b=>a()[0]=b,()=>a()?.[0])}),m(i,u)},V=i=>{var u=A();const v=w(()=>t.constructors[0]);var g=x(u);T(g,()=>f(v),(l,d)=>{p(d(l,{get data(){return n()},get form(){return t.form},get params(){return t.page.params}}),b=>a()[0]=b,()=>a()?.[0])}),m(i,u)};O(h,i=>{t.constructors[1]?i(I):i(V,!1)})}var j=st(h,2);{var S=i=>{var u=gt(),v=ot(u);{var g=l=>{var d=it();ut(()=>_t(d,f(o))),m(l,d)};O(v,l=>{f(e)&&l(g)})}ct(u),m(i,u)};O(j,i=>{f(r)&&i(S)})}m(c,_),nt()}const wt=ht(yt),Tt=[()=>E(()=>import("../nodes/0.Cpsq4hmd.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),()=>E(()=>import("../nodes/1.BGymq15E.js"),__vite__mapDeps([4,1,5,2,6,7,8]),import.meta.url),()=>E(()=>import("../nodes/2.DL9bb4bx.js"),__vite__mapDeps([9,1,5,2,6,10,11,8,12,13]),import.meta.url),()=>E(()=>import("../nodes/3.CQxkl77G.js"),__vite__mapDeps([14,15,1,2,6,10,7,8,11,12,16,5]),import.meta.url)],Ct=[],Lt={"/":[2],"/game/[id]":[3]},Et={handleError:({error:c})=>{console.error(c)},reroute:()=>{},transport:{}},xt=Object.fromEntries(Object.entries(Et.transport).map(([c,t])=>[c,t.decode])),Dt=!1,It=(c,t)=>xt[c](t);export{It as decode,xt as decoders,Lt as dictionary,Dt as hash,Et as hooks,At as matchers,Tt as nodes,wt as root,Ct as server_loads};
