import { writable } from 'svelte/store';

export interface OpponentInfo {
  userId?: string;
  name?: string;
  score: number;
  lives: number;
  status?: string;
}

export interface OpponentState {
  hasOpponent: boolean;
  waiting: boolean;
  opponent: OpponentInfo | null;
}

const initialState: OpponentState = {
  hasOpponent: false,
  waiting: true,
  opponent: null
};

export const opponentState = writable<OpponentState>(initialState);

export const opponentActions = {
  reset: () => opponentState.set(initialState),
  setOpponent: (info: OpponentInfo | null) => {
    opponentState.set({
      hasOpponent: !!info,
      waiting: !info,
      opponent: info
    });
  }
};

