import { v as push, V as head, _ as store_get, $ as unsubscribe_stores, x as pop, a0 as attr_style, T as escape_html, Y as attr, a1 as fallback, Z as bind_props, U as ensure_array_like, S as writable, W as attr_class, X as stringify, a2 as spread_attributes, a3 as current_component } from './exports-CtUTVNuO.js';
import { p as page } from './index2-DNUERyeR.js';
import { G as GameStatus, r as roomState, R as RoomStatus, c as checkIconState, g as generateIcon, a as gameState, o as opponentState } from './MumsNumbers.svelte_svelte_type_style_lang-Bs_cgvkx.js';
import 'pixi.js';
import './state.svelte-Bbvw5NZb.js';
import './shared-server-i79vVjEm.js';
import 'socket.io-client';

function html(value) {
  var html2 = String(value ?? "");
  var open = "<!---->";
  return open + html2 + "<!---->";
}
function onDestroy(fn) {
  var context = (
    /** @type {Component} */
    current_component
  );
  (context.d ??= []).push(fn);
}
function Icon($$payload, $$props) {
  push();
  const iconState = {
    // Last icon name
    name: "",
    // Loading status
    loading: null,
    // Destroyed status
    destroyed: false
  };
  const { $$slots, $$events, ...props } = $$props;
  let mounted = false;
  let isMounted = !!props.ssr || mounted;
  let iconData = (() => {
    return checkIconState(props.icon, iconState, isMounted, loaded, props.onload);
  })();
  let data = (() => {
    const generatedData = iconData ? generateIcon(iconData.data, props) : null;
    if (generatedData && iconData.classes) {
      generatedData.attributes["class"] = (typeof props["class"] === "string" ? props["class"] + " " : "") + iconData.classes.join(" ");
    }
    return generatedData;
  })();
  function loaded() {
  }
  onDestroy(() => {
    iconState.destroyed = true;
    if (iconState.loading) {
      iconState.loading.abort();
      iconState.loading = null;
    }
  });
  if (data) {
    $$payload.out.push("<!--[-->");
    if (data.svg) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<svg${spread_attributes({ ...data.attributes }, null, void 0, void 0, 3)}>${html(data.body)}</svg>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<span${spread_attributes({ ...data.attributes })}></span>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function HUDPlayer($$payload, $$props) {
  let { score, lives, maxLives, name = "You", avatarUrl, compare } = $$props;
  let borderClass = compare === "win" ? "border-green-400" : compare === "lose" ? "border-red-400" : compare === "tie" ? "border-yellow-400" : "border-white/40";
  const each_array = ensure_array_like(Array(maxLives));
  $$payload.out.push(`<div class="pointer-events-auto flex flex-col items-start gap-2"><div class="flex items-center gap-2">`);
  if (avatarUrl) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<img${attr("src", avatarUrl)} alt="Player avatar"${attr_class(`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${stringify(borderClass)}`)}/>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${attr_class(`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${stringify(borderClass)}`)}>`);
    Icon($$payload, { icon: "mdi:account", color: "white", height: "3vh" });
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]--> <div class="flex flex-col"><div class="text-[1.8vh] text-left font-medium leading-tight">${escape_html(name)}</div> <div class="flex flex-row items-center gap-2"><div class="flex gap-1"><!--[-->`);
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    each_array[i];
    if (i < lives) {
      $$payload.out.push("<!--[-->");
      Icon($$payload, {
        height: "2vh",
        color: "red",
        icon: "material-symbols:favorite"
      });
    } else {
      $$payload.out.push("<!--[!-->");
      Icon($$payload, {
        height: "2vh",
        color: "red",
        icon: "material-symbols:favorite-outline"
      });
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div> <span class="font-bold text-[2vh] align-middle">${escape_html(score)}</span></div></div></div></div>`);
}
function HUDOpponent($$payload, $$props) {
  let {
    waiting = true,
    score = null,
    lives = null,
    maxLives,
    name = "Opponent",
    avatarUrl,
    compare
  } = $$props;
  let borderClass = compare === "win" ? "border-green-400" : compare === "lose" ? "border-red-400" : compare === "tie" ? "border-yellow-400" : "border-white/40";
  $$payload.out.push(`<div class="pointer-events-auto flex items-center gap-2"><div class="flex flex-col items-end gap-1">`);
  if (waiting || score === null) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="text-[2vh] italic opacity-80">Waiting for player…</div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    const each_array = ensure_array_like(Array(maxLives));
    $$payload.out.push(`<div class="flex items-center gap-2"><div class="flex flex-col"><div class="text-[1.8vh] text-right font-medium leading-tight">${escape_html(name)}</div> <div class="flex flex-row items-center gap-2"><span class="font-bold text-[2vh] align-middle">${escape_html(score)}</span> <div class="flex gap-1"><!--[-->`);
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      each_array[i];
      if (lives !== null && i < lives) {
        $$payload.out.push("<!--[-->");
        Icon($$payload, {
          height: "2vh",
          color: "red",
          icon: "material-symbols:favorite"
        });
      } else {
        $$payload.out.push("<!--[!-->");
        Icon($$payload, {
          height: "2vh",
          color: "red",
          icon: "material-symbols:favorite-outline"
        });
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></div></div></div> `);
    if (avatarUrl) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<img${attr("src", avatarUrl)} alt="Opponent avatar"${attr_class(`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${stringify(borderClass)}`)}/>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div${attr_class(`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${stringify(borderClass)}`)}>`);
      Icon($$payload, { icon: "mdi:account", color: "white", height: "3vh" });
      $$payload.out.push(`<!----></div>`);
    }
    $$payload.out.push(`<!--]--></div>`);
  }
  $$payload.out.push(`<!--]--></div></div>`);
}
function GameHUD($$payload, $$props) {
  push();
  let {
    score,
    time,
    totalTime,
    lives,
    maxLives,
    opponentScore = null,
    opponentLives = null,
    opponentWaiting = true,
    playerName,
    playerAvatarUrl,
    opponentName,
    opponentAvatarUrl
  } = $$props;
  function formatTime(time2) {
    const minutes = Math.floor(time2 / 60);
    const seconds = Math.floor(time2 % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }
  let playerCompare = opponentScore == null ? null : score > opponentScore ? "win" : score < opponentScore ? "lose" : "tie";
  let opponentCompare = opponentScore == null ? null : opponentScore > score ? "win" : opponentScore < score ? "lose" : "tie";
  $$payload.out.push(`<div class="fixed left-0 right-0 z-10 px-[0vw] py-[0vh] flex flex-col items-center justify-around text-white"><div class="w-full pointer-events-none relative top-0 left-0 right-0 px-[4vw] py-[2vh] flex items-center justify-between">`);
  HUDPlayer($$payload, {
    score,
    lives,
    maxLives,
    name: playerName,
    avatarUrl: playerAvatarUrl,
    compare: playerCompare
  });
  $$payload.out.push(`<!----> `);
  HUDOpponent($$payload, {
    waiting: opponentWaiting,
    score: opponentScore,
    lives: opponentLives,
    maxLives,
    name: opponentName,
    avatarUrl: opponentAvatarUrl,
    compare: opponentCompare
  });
  $$payload.out.push(`<!----></div> <div class="relative w-full flex items-center bg-black/20"><div class="absolute left-1/2 top-1/2 -translate-y-1/2 -translate-1/2 w-[10vh] h-[6vh] flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10 font-medium text-[2.5vh]">${escape_html(formatTime(time))}</div> <div class="relative w-full h-2 rounded-xl overflow-hidden"><div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear"${attr_style(`width: ${stringify(time / totalTime * 100)}%;`)}></div></div></div></div>`);
  pop();
}
function Countdown($$payload, $$props) {
  push();
  let { duration = 3, show = false } = $$props;
  let currentCount = duration;
  if (show && currentCount >= 0) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="fixed inset-0 bg-black/60 flex justify-center items-center z-[2000] svelte-vzg9ol"><img class="counter svelte-vzg9ol"${attr("src", `/assets/images/counter/${stringify(currentCount === 0 ? "GO" : currentCount)}.svg`)} alt=""/></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function EndGame($$payload, $$props) {
  let { show = false, finalScore = 0 } = $$props;
  if (show) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="game-end-overlay svelte-1os0kc9"><div class="game-background svelte-1os0kc9"></div> <div class="game-panel svelte-1os0kc9"><div class="panel-blur-bg svelte-1os0kc9"></div> <div class="panel-content svelte-1os0kc9"><div class="game-over-title svelte-1os0kc9"><h1 class="game-over-text svelte-1os0kc9">GAME OVER</h1></div> <div class="score-section svelte-1os0kc9"><div class="score-label svelte-1os0kc9">SCORE</div> <div class="score-value svelte-1os0kc9">${escape_html(finalScore)}</div></div> <button class="back-to-lobby-btn svelte-1os0kc9"><div class="btn-border"></div> <div class="btn-background"></div> <span class="btn-text">BACK TO LOBBY</span></button></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
}
function PlayerList($$payload, $$props) {
  push();
  let { players, currentUserId, maxPlayers } = $$props;
  const sortedPlayers = [...players].sort((a, b) => {
    if (a.isHost && !b.isHost) return -1;
    if (!a.isHost && b.isHost) return 1;
    return a.joinedAt.getTime() - b.joinedAt.getTime();
  });
  function getPlayerDisplayName(player) {
    return player.displayName || player.name || `Player ${player.userId.slice(-4)}`;
  }
  function getPlayerStatus(player) {
    if (!player.isConnected) return "Disconnected";
    if (player.isReady) return "Ready";
    return "Waiting";
  }
  function getStatusColor(player) {
    if (!player.isConnected) return "text-red-400";
    if (player.isReady) return "text-green-400";
    return "text-yellow-400";
  }
  const each_array = ensure_array_like(sortedPlayers);
  const each_array_1 = ensure_array_like(Array(Math.max(0, maxPlayers - players.length)));
  $$payload.out.push(`<div class="player-list svelte-1pruxy9"><div class="player-list-header"><h3 class="text-lg font-semibold text-white mb-2">Players (${escape_html(players.length)}/${escape_html(maxPlayers)})</h3></div> <div class="player-list-content svelte-1pruxy9"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let player = each_array[$$index];
    $$payload.out.push(`<div${attr_class("player-item svelte-1pruxy9", void 0, {
      "current-player": player.userId === currentUserId,
      "disconnected": !player.isConnected
    })}><div class="player-info svelte-1pruxy9"><div class="player-name svelte-1pruxy9">${escape_html(getPlayerDisplayName(player))} `);
    if (player.isHost) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="host-badge svelte-1pruxy9">HOST</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> `);
    if (player.userId === currentUserId) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<span class="you-badge svelte-1pruxy9">YOU</span>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> <div${attr_class(`player-status ${stringify(getStatusColor(player))}`, "svelte-1pruxy9")}>${escape_html(getPlayerStatus(player))}</div></div> <div class="player-indicators svelte-1pruxy9">`);
    if (player.isConnected) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="connection-indicator connected svelte-1pruxy9" title="Connected"></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div class="connection-indicator disconnected svelte-1pruxy9" title="Disconnected"></div>`);
    }
    $$payload.out.push(`<!--]--> `);
    if (player.isReady) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="ready-indicator ready svelte-1pruxy9" title="Ready">✓</div>`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div class="ready-indicator not-ready svelte-1pruxy9" title="Not Ready">○</div>`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  }
  $$payload.out.push(`<!--]--> <!--[-->`);
  for (let index = 0, $$length = each_array_1.length; index < $$length; index++) {
    each_array_1[index];
    $$payload.out.push(`<div class="player-item empty-slot svelte-1pruxy9"><div class="player-info svelte-1pruxy9"><div class="player-name text-gray-500 svelte-1pruxy9">Empty Slot</div></div></div>`);
  }
  $$payload.out.push(`<!--]--></div></div>`);
  pop();
}
function RoomControls($$payload, $$props) {
  push();
  let { roomState: roomState2 } = $$props;
  let isReady = false;
  const allPlayersReady = roomState2.players.length > 1 && roomState2.players.filter((p) => p.isConnected).every((p) => p.isReady);
  const canStartGame = roomState2.isHost && roomState2.players.length > 1 && allPlayersReady && !roomState2.isLoading;
  $$payload.out.push(`<div class="room-controls svelte-u634gp"><div class="room-info-section svelte-u634gp"><div class="room-code-display svelte-u634gp"><div class="room-code-label svelte-u634gp">Room Code</div> <div class="room-code-value svelte-u634gp" title="Click to copy">${escape_html(roomState2.roomCode || roomState2.roomId || "N/A")} <svg class="copy-icon svelte-u634gp" viewBox="0 0 20 20" fill="currentColor"><path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" class="svelte-u634gp"></path><path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" class="svelte-u634gp"></path></svg></div></div> <div class="game-info svelte-u634gp"><div class="game-name svelte-u634gp">Game: ${escape_html(roomState2.gameId || "Unknown")}</div> <div class="player-count svelte-u634gp">${escape_html(roomState2.players.length)}/${escape_html(roomState2.maxPlayers)} Players</div></div></div> <div class="controls-section svelte-u634gp">`);
  if (roomState2.isHost) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="host-controls svelte-u634gp"><div class="host-badge svelte-u634gp"><svg class="crown-icon svelte-u634gp" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd" class="svelte-u634gp"></path></svg> You are the Host</div> <button type="button" class="start-game-button svelte-u634gp"${attr("disabled", !canStartGame, true)}${attr("title", !canStartGame ? "Waiting for all players to be ready" : "Start the game for all players")}>`);
    if (roomState2.isLoading) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="loading-spinner svelte-u634gp"></div> Starting...`);
    } else {
      $$payload.out.push("<!--[!-->");
      if (!allPlayersReady && roomState2.players.length > 1) {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`Waiting for Players`);
      } else {
        $$payload.out.push("<!--[!-->");
        if (roomState2.players.length <= 1) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`Need More Players`);
        } else {
          $$payload.out.push("<!--[!-->");
          $$payload.out.push(`Start Game`);
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]--></button></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div class="player-controls svelte-u634gp"><button type="button"${attr_class("ready-button svelte-u634gp", void 0, { "ready": isReady })}${attr("disabled", roomState2.isLoading, true)}>`);
    {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<svg class="clock-icon svelte-u634gp" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" class="svelte-u634gp"></path></svg> Not Ready`);
    }
    $$payload.out.push(`<!--]--></button> <div class="waiting-message svelte-u634gp">`);
    if (roomState2.isHost) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`Waiting for you to start the game...`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`Waiting for host to start the game...`);
    }
    $$payload.out.push(`<!--]--></div></div>`);
  }
  $$payload.out.push(`<!--]--> <button type="button" class="leave-room-button svelte-u634gp"${attr("disabled", roomState2.isLoading, true)}><svg class="exit-icon svelte-u634gp" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd" class="svelte-u634gp"></path></svg> Leave Room</button></div> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function RoomInfo($$payload, $$props) {
  push();
  let { roomState: roomState2 } = $$props;
  function getStatusMessage(status) {
    switch (status) {
      case RoomStatus.NotInRoom:
        return "Not in a room";
      case RoomStatus.Joining:
        return "Joining room...";
      case RoomStatus.InRoom:
        return "In room - waiting for game to start";
      case RoomStatus.Starting:
        return "Starting game...";
      case RoomStatus.GameActive:
        return "Game in progress";
      case RoomStatus.Leaving:
        return "Leaving room...";
      case RoomStatus.Error:
        return "Room error occurred";
      default:
        return "Unknown status";
    }
  }
  function getStatusColor(status) {
    switch (status) {
      case RoomStatus.NotInRoom:
        return "text-gray-400";
      case RoomStatus.Joining:
      case RoomStatus.Starting:
      case RoomStatus.Leaving:
        return "text-yellow-400";
      case RoomStatus.InRoom:
        return "text-blue-400";
      case RoomStatus.GameActive:
        return "text-green-400";
      case RoomStatus.Error:
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  }
  function getStatusIcon(status) {
    switch (status) {
      case RoomStatus.NotInRoom:
        return "🏠";
      case RoomStatus.Joining:
        return "🚪";
      case RoomStatus.InRoom:
        return "👥";
      case RoomStatus.Starting:
        return "🎮";
      case RoomStatus.GameActive:
        return "🎯";
      case RoomStatus.Leaving:
        return "🚶";
      case RoomStatus.Error:
        return "⚠️";
      default:
        return "❓";
    }
  }
  const connectedPlayersCount = roomState2.players.filter((p) => p.isConnected).length;
  const readyPlayersCount = roomState2.players.filter((p) => p.isConnected && p.isReady).length;
  $$payload.out.push(`<div class="room-info svelte-161wzr1"><div class="status-section svelte-161wzr1"><div class="status-indicator svelte-161wzr1"><span class="status-icon svelte-161wzr1">${escape_html(getStatusIcon(roomState2.status))}</span> <div class="status-details svelte-161wzr1"><div${attr_class(`status-text ${stringify(getStatusColor(roomState2.status))}`, "svelte-161wzr1")}>${escape_html(getStatusMessage(roomState2.status))}</div> `);
  if (roomState2.loadingMessage) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="loading-message svelte-161wzr1">${escape_html(roomState2.loadingMessage)}</div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div> `);
  if (roomState2.isLoading) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="loading-spinner svelte-161wzr1"></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> `);
  if (roomState2.status === RoomStatus.InRoom || roomState2.status === RoomStatus.Starting) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="room-details svelte-161wzr1"><div class="detail-item svelte-161wzr1"><span class="detail-label svelte-161wzr1">Room:</span> <span class="detail-value svelte-161wzr1">${escape_html(roomState2.roomCode || roomState2.roomId)}</span></div> <div class="detail-item svelte-161wzr1"><span class="detail-label svelte-161wzr1">Game:</span> <span class="detail-value svelte-161wzr1">${escape_html(roomState2.gameId)}</span></div> <div class="detail-item svelte-161wzr1"><span class="detail-label svelte-161wzr1">Players:</span> <span class="detail-value svelte-161wzr1">${escape_html(connectedPlayersCount)}/${escape_html(roomState2.maxPlayers)} `);
    if (roomState2.players.length > 1) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`(${escape_html(readyPlayersCount)} ready)`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></span></div> `);
    if (roomState2.isHost) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="detail-item svelte-161wzr1"><span class="detail-label svelte-161wzr1">Role:</span> <span class="detail-value host-role svelte-161wzr1">Host</span></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (roomState2.error) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="error-section svelte-161wzr1"><div class="error-icon svelte-161wzr1">⚠️</div> <div class="error-message svelte-161wzr1">${escape_html(roomState2.error)}</div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (!roomState2.isConnected && roomState2.status !== RoomStatus.NotInRoom) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="connection-warning svelte-161wzr1"><div class="warning-icon svelte-161wzr1">📡</div> <div class="warning-message svelte-161wzr1">Connection lost - attempting to reconnect...</div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function StartScreen($$payload, $$props) {
  push();
  var $$store_subs;
  let { gameId } = $$props;
  let isStarting = false;
  const showRoomInterface = store_get($$store_subs ??= {}, "$roomState", roomState).status === RoomStatus.InRoom || store_get($$store_subs ??= {}, "$roomState", roomState).status === RoomStatus.Starting || store_get($$store_subs ??= {}, "$roomState", roomState).status === RoomStatus.Joining;
  const showMainMenu = !showRoomInterface && true && true && store_get($$store_subs ??= {}, "$roomState", roomState).status === RoomStatus.NotInRoom;
  $$payload.out.push(`<div class="game-start-container svelte-1qryxho"><div class="background svelte-1qryxho"></div> <img class="game-title pulse svelte-1qryxho"${attr("src", `/assets-${stringify(gameId)}/images/game_name.png`)} alt="Game Title"/> `);
  if (showMainMenu) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="menu-container svelte-1qryxho"><button class="menu-button solo-button svelte-1qryxho"${attr("disabled", isStarting, true)}><div class="btn-border svelte-1qryxho"></div> <div class="btn-background svelte-1qryxho"></div> <span class="btn-text svelte-1qryxho">PLAY SOLO</span></button> <button class="menu-button multiplayer-button svelte-1qryxho"${attr("disabled", isStarting, true)}><div class="btn-border svelte-1qryxho"></div> <div class="btn-background svelte-1qryxho"></div> <span class="btn-text svelte-1qryxho">MULTIPLAYER</span></button></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (showRoomInterface) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="room-interface svelte-1qryxho"><div class="room-header svelte-1qryxho">`);
    RoomInfo($$payload, {
      roomState: store_get($$store_subs ??= {}, "$roomState", roomState)
    });
    $$payload.out.push(`<!----> <button class="back-button svelte-1qryxho"><svg viewBox="0 0 20 20" fill="currentColor" class="svelte-1qryxho"><path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" class="svelte-1qryxho"></path></svg> Leave</button></div> <div class="room-content svelte-1qryxho"><div class="room-left svelte-1qryxho">`);
    PlayerList($$payload, {
      players: store_get($$store_subs ??= {}, "$roomState", roomState).players,
      currentUserId: store_get($$store_subs ??= {}, "$roomState", roomState).currentUserId,
      maxPlayers: store_get($$store_subs ??= {}, "$roomState", roomState).maxPlayers
    });
    $$payload.out.push(`<!----></div> <div class="room-right svelte-1qryxho">`);
    RoomControls($$payload, {
      roomState: store_get($$store_subs ??= {}, "$roomState", roomState)
    });
    $$payload.out.push(`<!----></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Preloading($$payload, $$props) {
  let { progress } = $$props;
  let isComplete = progress >= 1;
  if (
    // $: isComplete = progress >= 1;
    !isComplete
  ) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="absolute w-screen h-screen z-1000 flex flex-col justify-center items-center"><div class="background svelte-2ea9pu"></div>  <div class="w-80 h-4 bg-gray-700 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"${attr_style(`width: ${stringify(Math.max(0, Math.min(100, progress * 100)))}%`)}></div></div> <p class="text-sm text-gray-300 mt-4">${escape_html(Math.round(Math.max(0, Math.min(100, progress * 100))))}%</p></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
}
function ErrorModal($$payload, $$props) {
  push();
  let isVisible = fallback($$props["isVisible"], false);
  let errorMessage = fallback($$props["errorMessage"], "");
  let errorType = fallback($$props["errorType"], "");
  let onClose = fallback($$props["onClose"], () => {
  });
  if (isVisible) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="modal-backdrop svelte-jbysz9" role="dialog" aria-modal="true"><div class="modal-container svelte-jbysz9"><div class="modal-blur-bg svelte-jbysz9"></div> <div class="modal-content svelte-jbysz9"><div class="error-icon svelte-jbysz9"><svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-jbysz9"><circle cx="12" cy="12" r="10" stroke="#ff4444" stroke-width="2" fill="none" class="svelte-jbysz9"></circle><path d="M15 9l-6 6" stroke="#ff4444" stroke-width="2" stroke-linecap="round" class="svelte-jbysz9"></path><path d="M9 9l6 6" stroke="#ff4444" stroke-width="2" stroke-linecap="round" class="svelte-jbysz9"></path></svg></div> <div class="error-title svelte-jbysz9"><h2 class="svelte-jbysz9">Game Error</h2></div> `);
    if (errorType) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div class="error-type-badge svelte-jbysz9">${escape_html(errorType.toUpperCase())}</div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> <div class="error-message svelte-jbysz9"><p class="svelte-jbysz9">${escape_html(errorMessage)}</p></div> <div class="modal-actions svelte-jbysz9"><button class="close-btn svelte-jbysz9"><span class="svelte-jbysz9">Close</span></button></div></div></div></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { isVisible, errorMessage, errorType, onClose });
  pop();
}
function PopupLeaderboard($$payload, $$props) {
  push();
  var $$store_subs;
  let roomId = $$props["roomId"];
  let open = fallback($$props["open"], false);
  const leaderboard = writable([]);
  const each_array = ensure_array_like(store_get($$store_subs ??= {}, "$leaderboard", leaderboard));
  $$payload.out.push(`<div id="popup-leaderboard" class="overlay svelte-fpngv5"${attr_style(`display: ${stringify(open ? "block" : "none")}`)}><div class="modal svelte-fpngv5"><div class="header svelte-fpngv5"><div class="title svelte-fpngv5">Room Leaderboard</div> <button class="close-btn svelte-fpngv5">×</button></div> <div class="list svelte-fpngv5"><!--[-->`);
  for (let idx = 0, $$length = each_array.length; idx < $$length; idx++) {
    let entry = each_array[idx];
    $$payload.out.push(`<div class="row svelte-fpngv5"><div class="rank svelte-fpngv5">${escape_html(idx + 1)}</div> <div class="userid svelte-fpngv5">${escape_html(entry.name ?? entry.userId)}</div> <div class="score svelte-fpngv5">${escape_html(entry.score)}</div> <div class="status svelte-fpngv5">${escape_html(entry.status)}</div></div>`);
  }
  $$payload.out.push(`<!--]--></div></div></div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { roomId, open });
  pop();
}
function LeaderboardToggle($$payload, $$props) {
  let onToggle = $$props["onToggle"];
  $$payload.out.push(`<button class="fixed bottom-4 right-4 z-[3500] rounded-full bg-[#151a22] border border-[#222a36] text-white shadow-lg p-3" aria-label="Toggle Leaderboard">`);
  Icon($$payload, { icon: "mdi:trophy", height: "24" });
  $$payload.out.push(`<!----></button>`);
  bind_props($$props, { onToggle });
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let showLeaderboard = false;
  let roomId = "default-room";
  let showCountdown = false;
  let showErrorModal = false;
  let errorMessage = "";
  let errorType = "";
  const gameId = page.params.id;
  page.url.searchParams.get("token");
  function showError(message, type = "error") {
    errorMessage = message;
    errorType = type;
    showErrorModal = true;
  }
  function handleErrorClose() {
  }
  if (typeof window !== "undefined") {
    window.showGameError = showError;
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>TicTaps - ${escape_html(gameId)}</title>`;
  });
  Preloading($$payload, {
    progress: store_get($$store_subs ??= {}, "$gameState", gameState).loadingProgress
  });
  $$payload.out.push(`<!----> `);
  if (store_get($$store_subs ??= {}, "$gameState", gameState).status === GameStatus.Waiting) {
    $$payload.out.push("<!--[-->");
    StartScreen($$payload, { gameId });
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="w-screen h-screen overflow-hidden relative">`);
  if (store_get($$store_subs ??= {}, "$gameState", gameState).status === GameStatus.Active) {
    $$payload.out.push("<!--[-->");
    GameHUD($$payload, {
      score: store_get($$store_subs ??= {}, "$gameState", gameState).score,
      time: store_get($$store_subs ??= {}, "$gameState", gameState).time,
      totalTime: store_get($$store_subs ??= {}, "$gameState", gameState).totalTime,
      lives: store_get($$store_subs ??= {}, "$gameState", gameState).lives,
      maxLives: store_get($$store_subs ??= {}, "$gameState", gameState).maxLives,
      opponentScore: store_get($$store_subs ??= {}, "$opponentState", opponentState).opponent?.score ?? null,
      opponentLives: store_get($$store_subs ??= {}, "$opponentState", opponentState).opponent?.lives ?? null,
      opponentWaiting: store_get($$store_subs ??= {}, "$opponentState", opponentState).waiting,
      opponentName: store_get($$store_subs ??= {}, "$opponentState", opponentState).opponent?.name
    });
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> <div class="w-full h-full box-border" id="game-container"></div> `);
  Countdown($$payload, { show: showCountdown, duration: 3 });
  $$payload.out.push(`<!----> `);
  EndGame($$payload, {
    show: store_get($$store_subs ??= {}, "$gameState", gameState).status === GameStatus.Ended,
    finalScore: store_get($$store_subs ??= {}, "$gameState", gameState).score
  });
  $$payload.out.push(`<!----> `);
  ErrorModal($$payload, {
    isVisible: showErrorModal,
    errorMessage,
    errorType,
    onClose: handleErrorClose
  });
  $$payload.out.push(`<!----> `);
  if (store_get($$store_subs ??= {}, "$gameState", gameState).status === GameStatus.Active || store_get($$store_subs ??= {}, "$gameState", gameState).status === GameStatus.Ended) {
    $$payload.out.push("<!--[-->");
    LeaderboardToggle($$payload, { onToggle: () => showLeaderboard = !showLeaderboard });
    $$payload.out.push(`<!----> `);
    PopupLeaderboard($$payload, { roomId, open: showLeaderboard });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CyCjrDJm.js.map
