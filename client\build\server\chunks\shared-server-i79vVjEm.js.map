{"version": 3, "file": "shared-server-i79vVjEm.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/shared-server.js"], "sourcesContent": ["let public_env = {};\nlet safe_public_env = {};\nfunction set_private_env(environment) {\n}\nfunction set_public_env(environment) {\n  public_env = environment;\n}\nfunction set_safe_public_env(environment) {\n  safe_public_env = environment;\n}\nexport {\n  set_public_env as a,\n  set_private_env as b,\n  set_safe_public_env as c,\n  public_env as p,\n  safe_public_env as s\n};\n"], "names": [], "mappings": "AAAG,IAAC,UAAU,GAAG;AACd,IAAC,eAAe,GAAG;AAGtB,SAAS,cAAc,CAAC,WAAW,EAAE;AACrC,EAAE,UAAU,GAAG,WAAW;AAC1B;AACA,SAAS,mBAAmB,CAAC,WAAW,EAAE;AAC1C,EAAE,eAAe,GAAG,WAAW;AAC/B;;;;"}