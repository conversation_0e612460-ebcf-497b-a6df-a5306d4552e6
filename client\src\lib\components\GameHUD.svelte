<script lang="ts">
  import HUDPlayer from "./HUDPlayer.svelte";
  import HUDOpponent from "./HUDOpponent.svelte";

  interface Props {
    score: number;
    time: number;
    totalTime: number;
    lives: number;
    maxLives: number;
    opponentScore?: number | null;
    opponentLives?: number | null;
    opponentWaiting?: boolean;
    playerName?: string;
    playerAvatarUrl?: string;
    opponentName?: string;
    opponentAvatarUrl?: string;
  }

  let {
    score,
    time,
    totalTime,
    lives,
    maxLives,
    opponentScore = null,
    opponentLives = null,
    opponentWaiting = true,
    playerName,
    playerAvatarUrl,
    opponentName,
    opponentAvatarUrl,
  }: Props = $props();

  function formatTime(time: number): string {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }

  type Compare = "win" | "lose" | "tie" | null;
  let playerCompare: Compare = $derived(
    opponentScore == null
      ? null
      : score > opponentScore
        ? "win"
        : score < opponentScore
          ? "lose"
          : "tie"
  );
  let opponentCompare: Compare = $derived(
    opponentScore == null
      ? null
      : opponentScore > score
        ? "win"
        : opponentScore < score
          ? "lose"
          : "tie"
  );
</script>

<div
  class="fixed left-0 right-0 z-10 px-[0vw] py-[0vh] flex flex-col items-center justify-around text-white"
>
  <!-- Corners: Player (left) and Opponent (right) -->
  <div
    class="w-full pointer-events-none relative top-0 left-0 right-0 px-[4vw] py-[2vh] flex items-center justify-between"
  >
    <HUDPlayer
      {score}
      {lives}
      {maxLives}
      name={playerName}
      avatarUrl={playerAvatarUrl}
      compare={playerCompare}
    />

    <HUDOpponent
      waiting={opponentWaiting}
      score={opponentScore}
      lives={opponentLives}
      {maxLives}
      name={opponentName}
      avatarUrl={opponentAvatarUrl}
      compare={opponentCompare}
    />
  </div>

  <!-- Timer Section (center, full width) -->
  <div class="relative w-full flex items-center bg-black/20">
    <!-- Timer Icon -->
    <!-- <div
      class="absolute left-0 top-1/2 -translate-y-1/2 w-[6vh] h-[6vh] flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10"
    >
      <Icon height="3vh" color="white" icon="material-symbols:timer" />
    </div> -->

    <!-- Time Display -->
    <div
      class="absolute left-1/2 top-1/2 -translate-y-1/2 -translate-1/2 w-[10vh] h-[6vh]
      flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10
      font-medium text-[2.5vh]"
    >
      {formatTime(time)}
    </div>

    <!-- Timer Bar -->
    <div class="relative w-full h-2 rounded-xl overflow-hidden">
      <div
        class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear"
        style="width: {(time / totalTime) * 100}%;"
      ></div>
    </div>
  </div>
</div>
