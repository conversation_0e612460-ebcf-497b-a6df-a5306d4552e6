import{R as B,ae as A,af as C,ag as L,a1 as j,T as ee,V as te,ah as F,I as H,z as ne,ai as ae,A as w,aj as N,ak as W,al as R,G as m,F as O,O as v,am as $,an as P,ao as M,B as re,ap as oe,aq as se,ar as ie,as as ue,at as le,au as ce,J as fe,p as de,e as d,av as _e,c as pe,W as S,h as D,aw as he,ax as T,g as ve,ay as be,az as U,a4 as ye,aA as ge,aa as me,aB as we,aC as Ee,aD as Te,aE as ke,aF as Se,ab as Ae}from"./4UAai7vz.js";function We(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const Ce=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function $e(e){return Ce.includes(e)}const Le={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function Ue(e){return e=e.toLowerCase(),Le[e]??e}const Oe=["touchstart","touchmove"];function De(e){return Oe.includes(e)}function Ye(e,t){if(t){const n=document.body;e.autofocus=!0,B(()=>{document.activeElement===n&&e.focus()})}}let z=!1;function xe(){z||(z=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function Y(e){var t=L,n=j;A(null),C(null);try{return e()}finally{A(t),C(n)}}function Ge(e,t,n,r=n){e.addEventListener(t,()=>Y(n));const o=e.__on_r;o?e.__on_r=()=>{o(),r(!0)}:e.__on_r=()=>r(!0),xe()}const G=new Set,I=new Set;function Me(e,t,n,r={}){function o(a){if(r.capture||E.call(t,a),!a.cancelBubble)return Y(()=>n?.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?B(()=>{t.addEventListener(e,o,r)}):t.addEventListener(e,o,r),o}function Je(e,t,n,r,o){var a={capture:r,passive:o},l=Me(e,t,n,a);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&ee(()=>{t.removeEventListener(e,l,a)})}function Ke(e){for(var t=0;t<e.length;t++)G.add(e[t]);for(var n of I)n(e)}function E(e){var t=this,n=t.ownerDocument,r=e.type,o=e.composedPath?.()||[],a=o[0]||e.target,l=0,i=e.__root;if(i){var _=o.indexOf(i);if(_!==-1&&(t===document||t===window)){e.__root=t;return}var s=o.indexOf(t);if(s===-1)return;_<=s&&(l=_)}if(a=o[l]||e.target,a!==t){te(e,"currentTarget",{configurable:!0,get(){return a||n}});var x=L,p=j;A(null),C(null);try{for(var u,c=[];a!==null;){var b=a.assignedSlot||a.parentNode||a.host||null;try{var h=a["__"+r];if(h!=null&&(!a.disabled||e.target===a))if(F(h)){var[X,...Z]=h;X.apply(a,[e,...Z])}else h.call(a,e)}catch(k){u?c.push(k):u=k}if(e.cancelBubble||b===t||b===null)break;a=b}if(u){for(let k of c)queueMicrotask(()=>{throw k});throw u}}finally{e.__root=t,delete e.currentTarget,A(x),C(p)}}}let f;function Ne(){f=void 0}function Qe(e){let t=null,n=w;var r;if(w){for(t=v,f===void 0&&(f=$(document.head));f!==null&&(f.nodeType!==N||f.data!==W);)f=R(f);f===null?m(!1):f=O(R(f))}w||(r=document.head.appendChild(H()));try{ne(()=>e(r),ae)}finally{n&&(m(!0),f=v,O(t))}}function Xe(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??=e.nodeValue)&&(e.__t=n,e.nodeValue=n+"")}function J(e,t){return K(e,t)}function Re(e,t){P(),t.intro=t.intro??!1;const n=t.target,r=w,o=v;try{for(var a=$(n);a&&(a.nodeType!==N||a.data!==W);)a=R(a);if(!a)throw M;m(!0),O(a),re();const l=K(e,{...t,anchor:a});if(v===null||v.nodeType!==N||v.data!==oe)throw se(),M;return m(!1),l}catch(l){if(l===M)return t.recover===!1&&ie(),P(),ue(n),m(!1),J(e,t);throw l}finally{m(r),O(o),Ne()}}const y=new Map;function K(e,{target:t,anchor:n,props:r={},events:o,context:a,intro:l=!0}){P();var i=new Set,_=p=>{for(var u=0;u<p.length;u++){var c=p[u];if(!i.has(c)){i.add(c);var b=De(c);t.addEventListener(c,E,{passive:b});var h=y.get(c);h===void 0?(document.addEventListener(c,E,{passive:b}),y.set(c,1)):y.set(c,h+1)}}};_(le(G)),I.add(_);var s=void 0,x=ce(()=>{var p=n??t.appendChild(H());return fe(()=>{if(a){de({});var u=d;u.c=a}o&&(r.$$events=o),w&&_e(p,null),s=e(p,r)||{},w&&(j.nodes_end=v),a&&pe()}),()=>{for(var u of i){t.removeEventListener(u,E);var c=y.get(u);--c===0?(document.removeEventListener(u,E),y.delete(u)):y.set(u,c)}I.delete(_),p!==n&&p.parentNode?.removeChild(p)}});return V.set(s,x),s}let V=new WeakMap;function Pe(e,t){const n=V.get(e);return n?(V.delete(e),n(t)):Promise.resolve()}function Ie(e,t,n){if(e==null)return t(void 0),S;const r=D(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const g=[];function Ze(e,t=S){let n=null;const r=new Set;function o(i){if(he(e,i)&&(e=i,n)){const _=!g.length;for(const s of r)s[1](),g.push(s,e);if(_){for(let s=0;s<g.length;s+=2)g[s][0](g[s+1]);g.length=0}}}function a(i){o(i(e))}function l(i,_=S){const s=[i,_];return r.add(s),r.size===1&&(n=t(o,a)||S),i(e),()=>{r.delete(s),r.size===0&&n&&(n(),n=null)}}return{set:o,update:a,subscribe:l}}function et(e){let t;return Ie(e,n=>t=n)(),t}function Ve(){return L===null&&be(),(L.ac??=new AbortController).signal}function Q(e){d===null&&T(),ye&&d.l!==null?q(d).m.push(e):ve(()=>{const t=D(e);if(typeof t=="function")return t})}function je(e){d===null&&T(),Q(()=>()=>D(e))}function qe(e,t,{bubbles:n=!1,cancelable:r=!1}={}){return new CustomEvent(e,{detail:t,bubbles:n,cancelable:r})}function ze(){const e=d;return e===null&&T(),(t,n,r)=>{const o=e.s.$$events?.[t];if(o){const a=F(o)?o.slice():[o],l=qe(t,n,r);for(const i of a)i.call(e.x,l);return!l.defaultPrevented}return!0}}function Be(e){d===null&&T(),d.l===null&&U(),q(d).b.push(e)}function Fe(e){d===null&&T(),d.l===null&&U(),q(d).a.push(e)}function q(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}const tt=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:Fe,beforeUpdate:Be,createEventDispatcher:ze,createRawSnippet:ge,flushSync:me,getAbortSignal:Ve,getAllContexts:we,getContext:Ee,hasContext:Te,hydrate:Re,mount:J,onDestroy:je,onMount:Q,setContext:ke,settled:Se,tick:Ae,unmount:Pe,untrack:D},Symbol.toStringTag,{value:"Module"}));export{Ie as a,Re as b,je as c,xe as d,Je as e,Me as f,et as g,Qe as h,We as i,Ke as j,Ye as k,$e as l,J as m,Ue as n,Q as o,Ge as p,tt as q,Xe as s,Pe as u,Ze as w};
