{"version": 3, "file": "_page.svelte-CyCjrDJm.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/game/_id_/_page.svelte.js"], "sourcesContent": ["import { O as current_component, P as spread_attributes, E as pop, A as push, J as ensure_array_like, G as attr, M as attr_class, F as escape_html, N as stringify, Q as attr_style, R as store_get, S as unsubscribe_stores, T as fallback, I as bind_props, K as head } from \"../../../../chunks/index.js\";\nimport { p as page } from \"../../../../chunks/index2.js\";\nimport { c as checkIconState, g as generateIcon, R as RoomStatus, r as roomState, G as GameStatus, a as gameState, o as opponentState } from \"../../../../chunks/MumsNumbers.svelte_svelte_type_style_lang.js\";\nimport \"phaser\";\nimport \"pixi.js\";\nimport \"@sveltejs/kit/internal\";\nimport { w as writable } from \"../../../../chunks/exports.js\";\nimport \"../../../../chunks/state.svelte.js\";\nfunction html(value) {\n  var html2 = String(value ?? \"\");\n  var open = \"<!---->\";\n  return open + html2 + \"<!---->\";\n}\nfunction onDestroy(fn) {\n  var context = (\n    /** @type {Component} */\n    current_component\n  );\n  (context.d ??= []).push(fn);\n}\nfunction Icon($$payload, $$props) {\n  push();\n  const iconState = {\n    // Last icon name\n    name: \"\",\n    // Loading status\n    loading: null,\n    // Destroyed status\n    destroyed: false\n  };\n  const { $$slots, $$events, ...props } = $$props;\n  let mounted = false;\n  let isMounted = !!props.ssr || mounted;\n  let iconData = (() => {\n    return checkIconState(props.icon, iconState, isMounted, loaded, props.onload);\n  })();\n  let data = (() => {\n    const generatedData = iconData ? generateIcon(iconData.data, props) : null;\n    if (generatedData && iconData.classes) {\n      generatedData.attributes[\"class\"] = (typeof props[\"class\"] === \"string\" ? props[\"class\"] + \" \" : \"\") + iconData.classes.join(\" \");\n    }\n    return generatedData;\n  })();\n  function loaded() {\n  }\n  onDestroy(() => {\n    iconState.destroyed = true;\n    if (iconState.loading) {\n      iconState.loading.abort();\n      iconState.loading = null;\n    }\n  });\n  if (data) {\n    $$payload.out.push(\"<!--[-->\");\n    if (data.svg) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<svg${spread_attributes({ ...data.attributes }, null, void 0, void 0, 3)}>${html(data.body)}</svg>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<span${spread_attributes({ ...data.attributes })}></span>`);\n    }\n    $$payload.out.push(`<!--]-->`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  pop();\n}\nfunction HUDPlayer($$payload, $$props) {\n  let { score, lives, maxLives, name = \"You\", avatarUrl, compare } = $$props;\n  let borderClass = compare === \"win\" ? \"border-green-400\" : compare === \"lose\" ? \"border-red-400\" : compare === \"tie\" ? \"border-yellow-400\" : \"border-white/40\";\n  const each_array = ensure_array_like(Array(maxLives));\n  $$payload.out.push(`<div class=\"pointer-events-auto flex flex-col items-start gap-2\"><div class=\"flex items-center gap-2\">`);\n  if (avatarUrl) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<img${attr(\"src\", avatarUrl)} alt=\"Player avatar\"${attr_class(`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${stringify(borderClass)}`)}/>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div${attr_class(`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${stringify(borderClass)}`)}>`);\n    Icon($$payload, { icon: \"mdi:account\", color: \"white\", height: \"3vh\" });\n    $$payload.out.push(`<!----></div>`);\n  }\n  $$payload.out.push(`<!--]--> <div class=\"flex flex-col\"><div class=\"text-[1.8vh] text-left font-medium leading-tight\">${escape_html(name)}</div> <div class=\"flex flex-row items-center gap-2\"><div class=\"flex gap-1\"><!--[-->`);\n  for (let i = 0, $$length = each_array.length; i < $$length; i++) {\n    each_array[i];\n    if (i < lives) {\n      $$payload.out.push(\"<!--[-->\");\n      Icon($$payload, {\n        height: \"2vh\",\n        color: \"red\",\n        icon: \"material-symbols:favorite\"\n      });\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      Icon($$payload, {\n        height: \"2vh\",\n        color: \"red\",\n        icon: \"material-symbols:favorite-outline\"\n      });\n    }\n    $$payload.out.push(`<!--]-->`);\n  }\n  $$payload.out.push(`<!--]--></div> <span class=\"font-bold text-[2vh] align-middle\">${escape_html(score)}</span></div></div></div></div>`);\n}\nfunction HUDOpponent($$payload, $$props) {\n  let {\n    waiting = true,\n    score = null,\n    lives = null,\n    maxLives,\n    name = \"Opponent\",\n    avatarUrl,\n    compare\n  } = $$props;\n  let borderClass = compare === \"win\" ? \"border-green-400\" : compare === \"lose\" ? \"border-red-400\" : compare === \"tie\" ? \"border-yellow-400\" : \"border-white/40\";\n  $$payload.out.push(`<div class=\"pointer-events-auto flex items-center gap-2\"><div class=\"flex flex-col items-end gap-1\">`);\n  if (waiting || score === null) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"text-[2vh] italic opacity-80\">Waiting for player…</div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    const each_array = ensure_array_like(Array(maxLives));\n    $$payload.out.push(`<div class=\"flex items-center gap-2\"><div class=\"flex flex-col\"><div class=\"text-[1.8vh] text-right font-medium leading-tight\">${escape_html(name)}</div> <div class=\"flex flex-row items-center gap-2\"><span class=\"font-bold text-[2vh] align-middle\">${escape_html(score)}</span> <div class=\"flex gap-1\"><!--[-->`);\n    for (let i = 0, $$length = each_array.length; i < $$length; i++) {\n      each_array[i];\n      if (lives !== null && i < lives) {\n        $$payload.out.push(\"<!--[-->\");\n        Icon($$payload, {\n          height: \"2vh\",\n          color: \"red\",\n          icon: \"material-symbols:favorite\"\n        });\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        Icon($$payload, {\n          height: \"2vh\",\n          color: \"red\",\n          icon: \"material-symbols:favorite-outline\"\n        });\n      }\n      $$payload.out.push(`<!--]-->`);\n    }\n    $$payload.out.push(`<!--]--></div></div></div> `);\n    if (avatarUrl) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<img${attr(\"src\", avatarUrl)} alt=\"Opponent avatar\"${attr_class(`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${stringify(borderClass)}`)}/>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<div${attr_class(`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${stringify(borderClass)}`)}>`);\n      Icon($$payload, { icon: \"mdi:account\", color: \"white\", height: \"3vh\" });\n      $$payload.out.push(`<!----></div>`);\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n}\nfunction GameHUD($$payload, $$props) {\n  push();\n  let {\n    score,\n    time,\n    totalTime,\n    lives,\n    maxLives,\n    opponentScore = null,\n    opponentLives = null,\n    opponentWaiting = true,\n    playerName,\n    playerAvatarUrl,\n    opponentName,\n    opponentAvatarUrl\n  } = $$props;\n  function formatTime(time2) {\n    const minutes = Math.floor(time2 / 60);\n    const seconds = Math.floor(time2 % 60);\n    return `${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`;\n  }\n  let playerCompare = opponentScore == null ? null : score > opponentScore ? \"win\" : score < opponentScore ? \"lose\" : \"tie\";\n  let opponentCompare = opponentScore == null ? null : opponentScore > score ? \"win\" : opponentScore < score ? \"lose\" : \"tie\";\n  $$payload.out.push(`<div class=\"fixed left-0 right-0 z-10 px-[0vw] py-[0vh] flex flex-col items-center justify-around text-white\"><div class=\"w-full pointer-events-none relative top-0 left-0 right-0 px-[4vw] py-[2vh] flex items-center justify-between\">`);\n  HUDPlayer($$payload, {\n    score,\n    lives,\n    maxLives,\n    name: playerName,\n    avatarUrl: playerAvatarUrl,\n    compare: playerCompare\n  });\n  $$payload.out.push(`<!----> `);\n  HUDOpponent($$payload, {\n    waiting: opponentWaiting,\n    score: opponentScore,\n    lives: opponentLives,\n    maxLives,\n    name: opponentName,\n    avatarUrl: opponentAvatarUrl,\n    compare: opponentCompare\n  });\n  $$payload.out.push(`<!----></div> <div class=\"relative w-full flex items-center bg-black/20\"><div class=\"absolute left-1/2 top-1/2 -translate-y-1/2 -translate-1/2 w-[10vh] h-[6vh] flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10 font-medium text-[2.5vh]\">${escape_html(formatTime(time))}</div> <div class=\"relative w-full h-2 rounded-xl overflow-hidden\"><div class=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear\"${attr_style(`width: ${stringify(time / totalTime * 100)}%;`)}></div></div></div></div>`);\n  pop();\n}\nfunction Countdown($$payload, $$props) {\n  push();\n  let { duration = 3, show = false } = $$props;\n  let currentCount = duration;\n  if (show && currentCount >= 0) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"fixed inset-0 bg-black/60 flex justify-center items-center z-[2000] svelte-vzg9ol\"><img class=\"counter svelte-vzg9ol\"${attr(\"src\", `/assets/images/counter/${stringify(currentCount === 0 ? \"GO\" : currentCount)}.svg`)} alt=\"\"/></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  pop();\n}\nfunction EndGame($$payload, $$props) {\n  let { show = false, finalScore = 0 } = $$props;\n  if (show) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"game-end-overlay svelte-1os0kc9\"><div class=\"game-background svelte-1os0kc9\"></div> <div class=\"game-panel svelte-1os0kc9\"><div class=\"panel-blur-bg svelte-1os0kc9\"></div> <div class=\"panel-content svelte-1os0kc9\"><div class=\"game-over-title svelte-1os0kc9\"><h1 class=\"game-over-text svelte-1os0kc9\">GAME OVER</h1></div> <div class=\"score-section svelte-1os0kc9\"><div class=\"score-label svelte-1os0kc9\">SCORE</div> <div class=\"score-value svelte-1os0kc9\">${escape_html(finalScore)}</div></div> <button class=\"back-to-lobby-btn svelte-1os0kc9\"><div class=\"btn-border\"></div> <div class=\"btn-background\"></div> <span class=\"btn-text\">BACK TO LOBBY</span></button></div></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n}\nfunction PlayerList($$payload, $$props) {\n  push();\n  let { players, currentUserId, maxPlayers } = $$props;\n  const sortedPlayers = [...players].sort((a, b) => {\n    if (a.isHost && !b.isHost) return -1;\n    if (!a.isHost && b.isHost) return 1;\n    return a.joinedAt.getTime() - b.joinedAt.getTime();\n  });\n  function getPlayerDisplayName(player) {\n    return player.displayName || player.name || `Player ${player.userId.slice(-4)}`;\n  }\n  function getPlayerStatus(player) {\n    if (!player.isConnected) return \"Disconnected\";\n    if (player.isReady) return \"Ready\";\n    return \"Waiting\";\n  }\n  function getStatusColor(player) {\n    if (!player.isConnected) return \"text-red-400\";\n    if (player.isReady) return \"text-green-400\";\n    return \"text-yellow-400\";\n  }\n  const each_array = ensure_array_like(sortedPlayers);\n  const each_array_1 = ensure_array_like(Array(Math.max(0, maxPlayers - players.length)));\n  $$payload.out.push(`<div class=\"player-list svelte-1pruxy9\"><div class=\"player-list-header\"><h3 class=\"text-lg font-semibold text-white mb-2\">Players (${escape_html(players.length)}/${escape_html(maxPlayers)})</h3></div> <div class=\"player-list-content svelte-1pruxy9\"><!--[-->`);\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let player = each_array[$$index];\n    $$payload.out.push(`<div${attr_class(\"player-item svelte-1pruxy9\", void 0, {\n      \"current-player\": player.userId === currentUserId,\n      \"disconnected\": !player.isConnected\n    })}><div class=\"player-info svelte-1pruxy9\"><div class=\"player-name svelte-1pruxy9\">${escape_html(getPlayerDisplayName(player))} `);\n    if (player.isHost) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"host-badge svelte-1pruxy9\">HOST</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--> `);\n    if (player.userId === currentUserId) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<span class=\"you-badge svelte-1pruxy9\">YOU</span>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div> <div${attr_class(`player-status ${stringify(getStatusColor(player))}`, \"svelte-1pruxy9\")}>${escape_html(getPlayerStatus(player))}</div></div> <div class=\"player-indicators svelte-1pruxy9\">`);\n    if (player.isConnected) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"connection-indicator connected svelte-1pruxy9\" title=\"Connected\"></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<div class=\"connection-indicator disconnected svelte-1pruxy9\" title=\"Disconnected\"></div>`);\n    }\n    $$payload.out.push(`<!--]--> `);\n    if (player.isReady) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"ready-indicator ready svelte-1pruxy9\" title=\"Ready\">✓</div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<div class=\"ready-indicator not-ready svelte-1pruxy9\" title=\"Not Ready\">○</div>`);\n    }\n    $$payload.out.push(`<!--]--></div></div>`);\n  }\n  $$payload.out.push(`<!--]--> <!--[-->`);\n  for (let index = 0, $$length = each_array_1.length; index < $$length; index++) {\n    each_array_1[index];\n    $$payload.out.push(`<div class=\"player-item empty-slot svelte-1pruxy9\"><div class=\"player-info svelte-1pruxy9\"><div class=\"player-name text-gray-500 svelte-1pruxy9\">Empty Slot</div></div></div>`);\n  }\n  $$payload.out.push(`<!--]--></div></div>`);\n  pop();\n}\nfunction RoomControls($$payload, $$props) {\n  push();\n  let { roomState: roomState2 } = $$props;\n  let isReady = false;\n  const allPlayersReady = roomState2.players.length > 1 && roomState2.players.filter((p) => p.isConnected).every((p) => p.isReady);\n  const canStartGame = roomState2.isHost && roomState2.players.length > 1 && allPlayersReady && !roomState2.isLoading;\n  $$payload.out.push(`<div class=\"room-controls svelte-u634gp\"><div class=\"room-info-section svelte-u634gp\"><div class=\"room-code-display svelte-u634gp\"><div class=\"room-code-label svelte-u634gp\">Room Code</div> <div class=\"room-code-value svelte-u634gp\" title=\"Click to copy\">${escape_html(roomState2.roomCode || roomState2.roomId || \"N/A\")} <svg class=\"copy-icon svelte-u634gp\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path d=\"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\" class=\"svelte-u634gp\"></path><path d=\"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z\" class=\"svelte-u634gp\"></path></svg></div></div> <div class=\"game-info svelte-u634gp\"><div class=\"game-name svelte-u634gp\">Game: ${escape_html(roomState2.gameId || \"Unknown\")}</div> <div class=\"player-count svelte-u634gp\">${escape_html(roomState2.players.length)}/${escape_html(roomState2.maxPlayers)} Players</div></div></div> <div class=\"controls-section svelte-u634gp\">`);\n  if (roomState2.isHost) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"host-controls svelte-u634gp\"><div class=\"host-badge svelte-u634gp\"><svg class=\"crown-icon svelte-u634gp\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z\" clip-rule=\"evenodd\" class=\"svelte-u634gp\"></path></svg> You are the Host</div> <button type=\"button\" class=\"start-game-button svelte-u634gp\"${attr(\"disabled\", !canStartGame, true)}${attr(\"title\", !canStartGame ? \"Waiting for all players to be ready\" : \"Start the game for all players\")}>`);\n    if (roomState2.isLoading) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"loading-spinner svelte-u634gp\"></div> Starting...`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      if (!allPlayersReady && roomState2.players.length > 1) {\n        $$payload.out.push(\"<!--[-->\");\n        $$payload.out.push(`Waiting for Players`);\n      } else {\n        $$payload.out.push(\"<!--[!-->\");\n        if (roomState2.players.length <= 1) {\n          $$payload.out.push(\"<!--[-->\");\n          $$payload.out.push(`Need More Players`);\n        } else {\n          $$payload.out.push(\"<!--[!-->\");\n          $$payload.out.push(`Start Game`);\n        }\n        $$payload.out.push(`<!--]-->`);\n      }\n      $$payload.out.push(`<!--]-->`);\n    }\n    $$payload.out.push(`<!--]--></button></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n    $$payload.out.push(`<div class=\"player-controls svelte-u634gp\"><button type=\"button\"${attr_class(\"ready-button svelte-u634gp\", void 0, { \"ready\": isReady })}${attr(\"disabled\", roomState2.isLoading, true)}>`);\n    {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`<svg class=\"clock-icon svelte-u634gp\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\" class=\"svelte-u634gp\"></path></svg> Not Ready`);\n    }\n    $$payload.out.push(`<!--]--></button> <div class=\"waiting-message svelte-u634gp\">`);\n    if (roomState2.isHost) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`Waiting for you to start the game...`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n      $$payload.out.push(`Waiting for host to start the game...`);\n    }\n    $$payload.out.push(`<!--]--></div></div>`);\n  }\n  $$payload.out.push(`<!--]--> <button type=\"button\" class=\"leave-room-button svelte-u634gp\"${attr(\"disabled\", roomState2.isLoading, true)}><svg class=\"exit-icon svelte-u634gp\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fill-rule=\"evenodd\" d=\"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z\" clip-rule=\"evenodd\" class=\"svelte-u634gp\"></path></svg> Leave Room</button></div> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nfunction RoomInfo($$payload, $$props) {\n  push();\n  let { roomState: roomState2 } = $$props;\n  function getStatusMessage(status) {\n    switch (status) {\n      case RoomStatus.NotInRoom:\n        return \"Not in a room\";\n      case RoomStatus.Joining:\n        return \"Joining room...\";\n      case RoomStatus.InRoom:\n        return \"In room - waiting for game to start\";\n      case RoomStatus.Starting:\n        return \"Starting game...\";\n      case RoomStatus.GameActive:\n        return \"Game in progress\";\n      case RoomStatus.Leaving:\n        return \"Leaving room...\";\n      case RoomStatus.Error:\n        return \"Room error occurred\";\n      default:\n        return \"Unknown status\";\n    }\n  }\n  function getStatusColor(status) {\n    switch (status) {\n      case RoomStatus.NotInRoom:\n        return \"text-gray-400\";\n      case RoomStatus.Joining:\n      case RoomStatus.Starting:\n      case RoomStatus.Leaving:\n        return \"text-yellow-400\";\n      case RoomStatus.InRoom:\n        return \"text-blue-400\";\n      case RoomStatus.GameActive:\n        return \"text-green-400\";\n      case RoomStatus.Error:\n        return \"text-red-400\";\n      default:\n        return \"text-gray-400\";\n    }\n  }\n  function getStatusIcon(status) {\n    switch (status) {\n      case RoomStatus.NotInRoom:\n        return \"🏠\";\n      case RoomStatus.Joining:\n        return \"🚪\";\n      case RoomStatus.InRoom:\n        return \"👥\";\n      case RoomStatus.Starting:\n        return \"🎮\";\n      case RoomStatus.GameActive:\n        return \"🎯\";\n      case RoomStatus.Leaving:\n        return \"🚶\";\n      case RoomStatus.Error:\n        return \"⚠️\";\n      default:\n        return \"❓\";\n    }\n  }\n  const connectedPlayersCount = roomState2.players.filter((p) => p.isConnected).length;\n  const readyPlayersCount = roomState2.players.filter((p) => p.isConnected && p.isReady).length;\n  $$payload.out.push(`<div class=\"room-info svelte-161wzr1\"><div class=\"status-section svelte-161wzr1\"><div class=\"status-indicator svelte-161wzr1\"><span class=\"status-icon svelte-161wzr1\">${escape_html(getStatusIcon(roomState2.status))}</span> <div class=\"status-details svelte-161wzr1\"><div${attr_class(`status-text ${stringify(getStatusColor(roomState2.status))}`, \"svelte-161wzr1\")}>${escape_html(getStatusMessage(roomState2.status))}</div> `);\n  if (roomState2.loadingMessage) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"loading-message svelte-161wzr1\">${escape_html(roomState2.loadingMessage)}</div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div></div> `);\n  if (roomState2.isLoading) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"loading-spinner svelte-161wzr1\"></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div> `);\n  if (roomState2.status === RoomStatus.InRoom || roomState2.status === RoomStatus.Starting) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"room-details svelte-161wzr1\"><div class=\"detail-item svelte-161wzr1\"><span class=\"detail-label svelte-161wzr1\">Room:</span> <span class=\"detail-value svelte-161wzr1\">${escape_html(roomState2.roomCode || roomState2.roomId)}</span></div> <div class=\"detail-item svelte-161wzr1\"><span class=\"detail-label svelte-161wzr1\">Game:</span> <span class=\"detail-value svelte-161wzr1\">${escape_html(roomState2.gameId)}</span></div> <div class=\"detail-item svelte-161wzr1\"><span class=\"detail-label svelte-161wzr1\">Players:</span> <span class=\"detail-value svelte-161wzr1\">${escape_html(connectedPlayersCount)}/${escape_html(roomState2.maxPlayers)} `);\n    if (roomState2.players.length > 1) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`(${escape_html(readyPlayersCount)} ready)`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></span></div> `);\n    if (roomState2.isHost) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"detail-item svelte-161wzr1\"><span class=\"detail-label svelte-161wzr1\">Role:</span> <span class=\"detail-value host-role svelte-161wzr1\">Host</span></div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (roomState2.error) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"error-section svelte-161wzr1\"><div class=\"error-icon svelte-161wzr1\">⚠️</div> <div class=\"error-message svelte-161wzr1\">${escape_html(roomState2.error)}</div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (!roomState2.isConnected && roomState2.status !== RoomStatus.NotInRoom) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"connection-warning svelte-161wzr1\"><div class=\"warning-icon svelte-161wzr1\">📡</div> <div class=\"warning-message svelte-161wzr1\">Connection lost - attempting to reconnect...</div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  pop();\n}\nfunction StartScreen($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let { gameId } = $$props;\n  let isStarting = false;\n  const showRoomInterface = store_get($$store_subs ??= {}, \"$roomState\", roomState).status === RoomStatus.InRoom || store_get($$store_subs ??= {}, \"$roomState\", roomState).status === RoomStatus.Starting || store_get($$store_subs ??= {}, \"$roomState\", roomState).status === RoomStatus.Joining;\n  const showMainMenu = !showRoomInterface && true && true && store_get($$store_subs ??= {}, \"$roomState\", roomState).status === RoomStatus.NotInRoom;\n  $$payload.out.push(`<div class=\"game-start-container svelte-1qryxho\"><div class=\"background svelte-1qryxho\"></div> <img class=\"game-title pulse svelte-1qryxho\"${attr(\"src\", `/assets-${stringify(gameId)}/images/game_name.png`)} alt=\"Game Title\"/> `);\n  if (showMainMenu) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"menu-container svelte-1qryxho\"><button class=\"menu-button solo-button svelte-1qryxho\"${attr(\"disabled\", isStarting, true)}><div class=\"btn-border svelte-1qryxho\"></div> <div class=\"btn-background svelte-1qryxho\"></div> <span class=\"btn-text svelte-1qryxho\">PLAY SOLO</span></button> <button class=\"menu-button multiplayer-button svelte-1qryxho\"${attr(\"disabled\", isStarting, true)}><div class=\"btn-border svelte-1qryxho\"></div> <div class=\"btn-background svelte-1qryxho\"></div> <span class=\"btn-text svelte-1qryxho\">MULTIPLAYER</span></button></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> `);\n  if (showRoomInterface) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"room-interface svelte-1qryxho\"><div class=\"room-header svelte-1qryxho\">`);\n    RoomInfo($$payload, {\n      roomState: store_get($$store_subs ??= {}, \"$roomState\", roomState)\n    });\n    $$payload.out.push(`<!----> <button class=\"back-button svelte-1qryxho\"><svg viewBox=\"0 0 20 20\" fill=\"currentColor\" class=\"svelte-1qryxho\"><path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" class=\"svelte-1qryxho\"></path></svg> Leave</button></div> <div class=\"room-content svelte-1qryxho\"><div class=\"room-left svelte-1qryxho\">`);\n    PlayerList($$payload, {\n      players: store_get($$store_subs ??= {}, \"$roomState\", roomState).players,\n      currentUserId: store_get($$store_subs ??= {}, \"$roomState\", roomState).currentUserId,\n      maxPlayers: store_get($$store_subs ??= {}, \"$roomState\", roomState).maxPlayers\n    });\n    $$payload.out.push(`<!----></div> <div class=\"room-right svelte-1qryxho\">`);\n    RoomControls($$payload, {\n      roomState: store_get($$store_subs ??= {}, \"$roomState\", roomState)\n    });\n    $$payload.out.push(`<!----></div></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  pop();\n}\nfunction Preloading($$payload, $$props) {\n  let { progress } = $$props;\n  let isComplete = progress >= 1;\n  if (\n    // $: isComplete = progress >= 1;\n    !isComplete\n  ) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"absolute w-screen h-screen z-1000 flex flex-col justify-center items-center\"><div class=\"background svelte-2ea9pu\"></div>  <div class=\"w-80 h-4 bg-gray-700 rounded-full overflow-hidden\"><div class=\"h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out\"${attr_style(`width: ${stringify(Math.max(0, Math.min(100, progress * 100)))}%`)}></div></div> <p class=\"text-sm text-gray-300 mt-4\">${escape_html(Math.round(Math.max(0, Math.min(100, progress * 100))))}%</p></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n}\nfunction ErrorModal($$payload, $$props) {\n  push();\n  let isVisible = fallback($$props[\"isVisible\"], false);\n  let errorMessage = fallback($$props[\"errorMessage\"], \"\");\n  let errorType = fallback($$props[\"errorType\"], \"\");\n  let onClose = fallback($$props[\"onClose\"], () => {\n  });\n  if (isVisible) {\n    $$payload.out.push(\"<!--[-->\");\n    $$payload.out.push(`<div class=\"modal-backdrop svelte-jbysz9\" role=\"dialog\" aria-modal=\"true\"><div class=\"modal-container svelte-jbysz9\"><div class=\"modal-blur-bg svelte-jbysz9\"></div> <div class=\"modal-content svelte-jbysz9\"><div class=\"error-icon svelte-jbysz9\"><svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"svelte-jbysz9\"><circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#ff4444\" stroke-width=\"2\" fill=\"none\" class=\"svelte-jbysz9\"></circle><path d=\"M15 9l-6 6\" stroke=\"#ff4444\" stroke-width=\"2\" stroke-linecap=\"round\" class=\"svelte-jbysz9\"></path><path d=\"M9 9l6 6\" stroke=\"#ff4444\" stroke-width=\"2\" stroke-linecap=\"round\" class=\"svelte-jbysz9\"></path></svg></div> <div class=\"error-title svelte-jbysz9\"><h2 class=\"svelte-jbysz9\">Game Error</h2></div> `);\n    if (errorType) {\n      $$payload.out.push(\"<!--[-->\");\n      $$payload.out.push(`<div class=\"error-type-badge svelte-jbysz9\">${escape_html(errorType.toUpperCase())}</div>`);\n    } else {\n      $$payload.out.push(\"<!--[!-->\");\n    }\n    $$payload.out.push(`<!--]--> <div class=\"error-message svelte-jbysz9\"><p class=\"svelte-jbysz9\">${escape_html(errorMessage)}</p></div> <div class=\"modal-actions svelte-jbysz9\"><button class=\"close-btn svelte-jbysz9\"><span class=\"svelte-jbysz9\">Close</span></button></div></div></div></div>`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]-->`);\n  bind_props($$props, { isVisible, errorMessage, errorType, onClose });\n  pop();\n}\nfunction PopupLeaderboard($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let roomId = $$props[\"roomId\"];\n  let open = fallback($$props[\"open\"], false);\n  const leaderboard = writable([]);\n  const each_array = ensure_array_like(store_get($$store_subs ??= {}, \"$leaderboard\", leaderboard));\n  $$payload.out.push(`<div id=\"popup-leaderboard\" class=\"overlay svelte-fpngv5\"${attr_style(`display: ${stringify(open ? \"block\" : \"none\")}`)}><div class=\"modal svelte-fpngv5\"><div class=\"header svelte-fpngv5\"><div class=\"title svelte-fpngv5\">Room Leaderboard</div> <button class=\"close-btn svelte-fpngv5\">×</button></div> <div class=\"list svelte-fpngv5\"><!--[-->`);\n  for (let idx = 0, $$length = each_array.length; idx < $$length; idx++) {\n    let entry = each_array[idx];\n    $$payload.out.push(`<div class=\"row svelte-fpngv5\"><div class=\"rank svelte-fpngv5\">${escape_html(idx + 1)}</div> <div class=\"userid svelte-fpngv5\">${escape_html(entry.name ?? entry.userId)}</div> <div class=\"score svelte-fpngv5\">${escape_html(entry.score)}</div> <div class=\"status svelte-fpngv5\">${escape_html(entry.status)}</div></div>`);\n  }\n  $$payload.out.push(`<!--]--></div></div></div>`);\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  bind_props($$props, { roomId, open });\n  pop();\n}\nfunction LeaderboardToggle($$payload, $$props) {\n  let onToggle = $$props[\"onToggle\"];\n  $$payload.out.push(`<button class=\"fixed bottom-4 right-4 z-[3500] rounded-full bg-[#151a22] border border-[#222a36] text-white shadow-lg p-3\" aria-label=\"Toggle Leaderboard\">`);\n  Icon($$payload, { icon: \"mdi:trophy\", height: \"24\" });\n  $$payload.out.push(`<!----></button>`);\n  bind_props($$props, { onToggle });\n}\nfunction _page($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let showLeaderboard = false;\n  let roomId = \"default-room\";\n  let showCountdown = false;\n  let showErrorModal = false;\n  let errorMessage = \"\";\n  let errorType = \"\";\n  const gameId = page.params.id;\n  page.url.searchParams.get(\"token\");\n  function showError(message, type = \"error\") {\n    errorMessage = message;\n    errorType = type;\n    showErrorModal = true;\n  }\n  function handleErrorClose() {\n  }\n  if (typeof window !== \"undefined\") {\n    window.showGameError = showError;\n  }\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>TicTaps - ${escape_html(gameId)}</title>`;\n  });\n  Preloading($$payload, {\n    progress: store_get($$store_subs ??= {}, \"$gameState\", gameState).loadingProgress\n  });\n  $$payload.out.push(`<!----> `);\n  if (store_get($$store_subs ??= {}, \"$gameState\", gameState).status === GameStatus.Waiting) {\n    $$payload.out.push(\"<!--[-->\");\n    StartScreen($$payload, { gameId });\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"w-screen h-screen overflow-hidden relative\">`);\n  if (store_get($$store_subs ??= {}, \"$gameState\", gameState).status === GameStatus.Active) {\n    $$payload.out.push(\"<!--[-->\");\n    GameHUD($$payload, {\n      score: store_get($$store_subs ??= {}, \"$gameState\", gameState).score,\n      time: store_get($$store_subs ??= {}, \"$gameState\", gameState).time,\n      totalTime: store_get($$store_subs ??= {}, \"$gameState\", gameState).totalTime,\n      lives: store_get($$store_subs ??= {}, \"$gameState\", gameState).lives,\n      maxLives: store_get($$store_subs ??= {}, \"$gameState\", gameState).maxLives,\n      opponentScore: store_get($$store_subs ??= {}, \"$opponentState\", opponentState).opponent?.score ?? null,\n      opponentLives: store_get($$store_subs ??= {}, \"$opponentState\", opponentState).opponent?.lives ?? null,\n      opponentWaiting: store_get($$store_subs ??= {}, \"$opponentState\", opponentState).waiting,\n      opponentName: store_get($$store_subs ??= {}, \"$opponentState\", opponentState).opponent?.name\n    });\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--> <div class=\"w-full h-full box-border\" id=\"game-container\"></div> `);\n  Countdown($$payload, { show: showCountdown, duration: 3 });\n  $$payload.out.push(`<!----> `);\n  EndGame($$payload, {\n    show: store_get($$store_subs ??= {}, \"$gameState\", gameState).status === GameStatus.Ended,\n    finalScore: store_get($$store_subs ??= {}, \"$gameState\", gameState).score\n  });\n  $$payload.out.push(`<!----> `);\n  ErrorModal($$payload, {\n    isVisible: showErrorModal,\n    errorMessage,\n    errorType,\n    onClose: handleErrorClose\n  });\n  $$payload.out.push(`<!----> `);\n  if (store_get($$store_subs ??= {}, \"$gameState\", gameState).status === GameStatus.Active || store_get($$store_subs ??= {}, \"$gameState\", gameState).status === GameStatus.Ended) {\n    $$payload.out.push(\"<!--[-->\");\n    LeaderboardToggle($$payload, { onToggle: () => showLeaderboard = !showLeaderboard });\n    $$payload.out.push(`<!----> `);\n    PopupLeaderboard($$payload, { roomId, open: showLeaderboard });\n    $$payload.out.push(`<!---->`);\n  } else {\n    $$payload.out.push(\"<!--[!-->\");\n  }\n  $$payload.out.push(`<!--]--></div>`);\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;AAQA,SAAS,IAAI,CAAC,KAAK,EAAE;AACrB,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;AACjC,EAAE,IAAI,IAAI,GAAG,SAAS;AACtB,EAAE,OAAO,IAAI,GAAG,KAAK,GAAG,SAAS;AACjC;AACA,SAAS,SAAS,CAAC,EAAE,EAAE;AACvB,EAAE,IAAI,OAAO;AACb;AACA,IAAI;AACJ,GAAG;AACH,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;AAC7B;AACA,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,SAAS,GAAG;AACpB;AACA,IAAI,IAAI,EAAE,EAAE;AACZ;AACA,IAAI,OAAO,EAAE,IAAI;AACjB;AACA,IAAI,SAAS,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AACjD,EAAE,IAAI,OAAO,GAAG,KAAK;AACrB,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO;AACxC,EAAE,IAAI,QAAQ,GAAG,CAAC,MAAM;AACxB,IAAI,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;AACjF,EAAE,CAAC,GAAG;AACN,EAAE,IAAI,IAAI,GAAG,CAAC,MAAM;AACpB,IAAI,MAAM,aAAa,GAAG,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI;AAC9E,IAAI,IAAI,aAAa,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC3C,MAAM,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACvI,IAAI;AACJ,IAAI,OAAO,aAAa;AACxB,EAAE,CAAC,GAAG;AACN,EAAE,SAAS,MAAM,GAAG;AACpB,EAAE;AACF,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI;AAC9B,IAAI,IAAI,SAAS,CAAC,OAAO,EAAE;AAC3B,MAAM,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE;AAC/B,MAAM,SAAS,CAAC,OAAO,GAAG,IAAI;AAC9B,IAAI;AACJ,EAAE,CAAC,CAAC;AACJ,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAClB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9H,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrF,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,GAAG,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;AAC5E,EAAE,IAAI,WAAW,GAAG,OAAO,KAAK,KAAK,GAAG,kBAAkB,GAAG,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,OAAO,KAAK,KAAK,GAAG,mBAAmB,GAAG,iBAAiB;AAChK,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACvD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sGAAsG,CAAC,CAAC;AAC9H,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,mDAAmD,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1K,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,mFAAmF,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5J,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC3E,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACvC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kGAAkG,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,qFAAqF,CAAC,CAAC;AACnO,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACnE,IAAI,UAAU,CAAC,CAAC,CAAC;AACjB,IAAI,IAAI,CAAC,GAAG,KAAK,EAAE;AACnB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,+BAA+B,CAAC,CAAC;AAC3I;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI;AACN,IAAI,OAAO,GAAG,IAAI;AAClB,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,QAAQ;AACZ,IAAI,IAAI,GAAG,UAAU;AACrB,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,WAAW,GAAG,OAAO,KAAK,KAAK,GAAG,kBAAkB,GAAG,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,OAAO,KAAK,KAAK,GAAG,mBAAmB,GAAG,iBAAiB;AAChK,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oGAAoG,CAAC,CAAC;AAC5H,EAAE,IAAI,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mEAAmE,CAAC,CAAC;AAC7F,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+HAA+H,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,qGAAqG,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,wCAAwC,CAAC,CAAC;AAC/U,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACrE,MAAM,UAAU,CAAC,CAAC,CAAC;AACnB,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,EAAE;AACvC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,MAAM,EAAE,KAAK;AACvB,UAAU,KAAK,EAAE,KAAK;AACtB,UAAU,IAAI,EAAE;AAChB,SAAS,CAAC;AACV,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,MAAM,EAAE,KAAK;AACvB,UAAU,KAAK,EAAE,KAAK;AACtB,UAAU,IAAI,EAAE;AAChB,SAAS,CAAC;AACV,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,CAAC,CAAC;AACrD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC,mDAAmD,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9K,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,mFAAmF,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9J,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC7E,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;AACzC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;AAC7B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC1C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAC1C,IAAI,OAAO,CAAC,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1F,EAAE;AACF,EAAE,IAAI,aAAa,GAAG,aAAa,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,aAAa,GAAG,KAAK,GAAG,KAAK,GAAG,aAAa,GAAG,MAAM,GAAG,KAAK;AAC3H,EAAE,IAAI,eAAe,GAAG,aAAa,IAAI,IAAI,GAAG,IAAI,GAAG,aAAa,GAAG,KAAK,GAAG,KAAK,GAAG,aAAa,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AAC7H,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wOAAwO,CAAC,CAAC;AAChQ,EAAE,SAAS,CAAC,SAAS,EAAE;AACvB,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,SAAS,EAAE,eAAe;AAC9B,IAAI,OAAO,EAAE;AACb,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,OAAO,EAAE,eAAe;AAC5B,IAAI,KAAK,EAAE,aAAa;AACxB,IAAI,KAAK,EAAE,aAAa;AACxB,IAAI,QAAQ;AACZ,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,SAAS,EAAE,iBAAiB;AAChC,IAAI,OAAO,EAAE;AACb,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sRAAsR,EAAE,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,yMAAyM,EAAE,UAAU,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC;AAC9mB,EAAE,GAAG,EAAE;AACP;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,IAAI,GAAG,KAAK,EAAE,GAAG,OAAO;AAC9C,EAAE,IAAI,YAAY,GAAG,QAAQ;AAC7B,EAAE,IAAI,IAAI,IAAI,YAAY,IAAI,CAAC,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iIAAiI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,uBAAuB,EAAE,SAAS,CAAC,YAAY,KAAK,CAAC,GAAG,IAAI,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;AAC7Q,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO;AAChD,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mdAAmd,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,sMAAsM,CAAC,CAAC;AAC7sB,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,OAAO;AACtD,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACpD,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE;AACxC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;AACvC,IAAI,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE;AACtD,EAAE,CAAC,CAAC;AACJ,EAAE,SAAS,oBAAoB,CAAC,MAAM,EAAE;AACxC,IAAI,OAAO,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF,EAAE;AACF,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;AACnC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,cAAc;AAClD,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,OAAO;AACtC,IAAI,OAAO,SAAS;AACpB,EAAE;AACF,EAAE,SAAS,cAAc,CAAC,MAAM,EAAE;AAClC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,cAAc;AAClD,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,gBAAgB;AAC/C,IAAI,OAAO,iBAAiB;AAC5B,EAAE;AACF,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,CAAC;AACrD,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACzF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mIAAmI,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,qEAAqE,CAAC,CAAC;AACzR,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;AACpC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,4BAA4B,EAAE,MAAM,EAAE;AAC/E,MAAM,gBAAgB,EAAE,MAAM,CAAC,MAAM,KAAK,aAAa;AACvD,MAAM,cAAc,EAAE,CAAC,MAAM,CAAC;AAC9B,KAAK,CAAC,CAAC,iFAAiF,EAAE,WAAW,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvI,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE;AACvB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mDAAmD,CAAC,CAAC;AAC/E,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,aAAa,EAAE;AACzC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iDAAiD,CAAC,CAAC;AAC7E,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,2DAA2D,CAAC,CAAC;AACrO,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;AAC5B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mFAAmF,CAAC,CAAC;AAC/G,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yFAAyF,CAAC,CAAC;AACrH,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE;AACxB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uEAAuE,CAAC,CAAC;AACnG,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+EAA+E,CAAC,CAAC;AAC3G,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC9C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACzC,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,GAAG,QAAQ,EAAE,KAAK,EAAE,EAAE;AACjF,IAAI,YAAY,CAAC,KAAK,CAAC;AACvB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6KAA6K,CAAC,CAAC;AACvM,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,OAAO;AACzC,EAAE,IAAI,OAAO,GAAG,KAAK;AACrB,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AAClI,EAAE,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,IAAI,CAAC,UAAU,CAAC,SAAS;AACrH,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+PAA+P,EAAE,WAAW,CAAC,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,yYAAyY,EAAE,WAAW,CAAC,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,+CAA+C,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,uEAAuE,CAAC,CAAC;AACp9B,EAAE,IAAI,UAAU,CAAC,MAAM,EAAE;AACzB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+pBAA+pB,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,YAAY,GAAG,qCAAqC,GAAG,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC50B,IAAI,IAAI,UAAU,CAAC,SAAS,EAAE;AAC9B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6DAA6D,CAAC,CAAC;AACzF,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,IAAI,CAAC,eAAe,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7D,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACjD,MAAM,CAAC,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;AAC5C,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACjD,QAAQ,CAAC,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC;AAC1C,QAAQ;AACR,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACtC,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACjD,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gEAAgE,EAAE,UAAU,CAAC,4BAA4B,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnN,IAAI;AACJ,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gSAAgS,CAAC,CAAC;AAC5T,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6DAA6D,CAAC,CAAC;AACvF,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE;AAC3B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oCAAoC,CAAC,CAAC;AAChE,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qCAAqC,CAAC,CAAC;AACjE,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC9C,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sEAAsE,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,yWAAyW,CAAC,CAAC;AACtf,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,OAAO;AACzC,EAAE,SAAS,gBAAgB,CAAC,MAAM,EAAE;AACpC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,UAAU,CAAC,SAAS;AAC/B,QAAQ,OAAO,eAAe;AAC9B,MAAM,KAAK,UAAU,CAAC,OAAO;AAC7B,QAAQ,OAAO,iBAAiB;AAChC,MAAM,KAAK,UAAU,CAAC,MAAM;AAC5B,QAAQ,OAAO,qCAAqC;AACpD,MAAM,KAAK,UAAU,CAAC,QAAQ;AAC9B,QAAQ,OAAO,kBAAkB;AACjC,MAAM,KAAK,UAAU,CAAC,UAAU;AAChC,QAAQ,OAAO,kBAAkB;AACjC,MAAM,KAAK,UAAU,CAAC,OAAO;AAC7B,QAAQ,OAAO,iBAAiB;AAChC,MAAM,KAAK,UAAU,CAAC,KAAK;AAC3B,QAAQ,OAAO,qBAAqB;AACpC,MAAM;AACN,QAAQ,OAAO,gBAAgB;AAC/B;AACA,EAAE;AACF,EAAE,SAAS,cAAc,CAAC,MAAM,EAAE;AAClC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,UAAU,CAAC,SAAS;AAC/B,QAAQ,OAAO,eAAe;AAC9B,MAAM,KAAK,UAAU,CAAC,OAAO;AAC7B,MAAM,KAAK,UAAU,CAAC,QAAQ;AAC9B,MAAM,KAAK,UAAU,CAAC,OAAO;AAC7B,QAAQ,OAAO,iBAAiB;AAChC,MAAM,KAAK,UAAU,CAAC,MAAM;AAC5B,QAAQ,OAAO,eAAe;AAC9B,MAAM,KAAK,UAAU,CAAC,UAAU;AAChC,QAAQ,OAAO,gBAAgB;AAC/B,MAAM,KAAK,UAAU,CAAC,KAAK;AAC3B,QAAQ,OAAO,cAAc;AAC7B,MAAM;AACN,QAAQ,OAAO,eAAe;AAC9B;AACA,EAAE;AACF,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,UAAU,CAAC,SAAS;AAC/B,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,UAAU,CAAC,OAAO;AAC7B,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,UAAU,CAAC,MAAM;AAC5B,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,UAAU,CAAC,QAAQ;AAC9B,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,UAAU,CAAC,UAAU;AAChC,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,UAAU,CAAC,OAAO;AAC7B,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,UAAU,CAAC,KAAK;AAC3B,QAAQ,OAAO,IAAI;AACnB,MAAM;AACN,QAAQ,OAAO,GAAG;AAClB;AACA,EAAE;AACF,EAAE,MAAM,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM;AACtF,EAAE,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;AAC/F,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uKAAuK,EAAE,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,uDAAuD,EAAE,UAAU,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/b,EAAE,IAAI,UAAU,CAAC,cAAc,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AACrH,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAC7C,EAAE,IAAI,UAAU,CAAC,SAAS,EAAE;AAC5B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kDAAkD,CAAC,CAAC;AAC5E,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC;AACvC,EAAE,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,QAAQ,EAAE;AAC5F,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,kLAAkL,EAAE,WAAW,CAAC,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,uJAAuJ,EAAE,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,0JAA0J,EAAE,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAClqB,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC;AACrE,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAChD,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE;AAC3B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oKAAoK,CAAC,CAAC;AAChM,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACxC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE;AACxB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,oIAAoI,EAAE,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC;AAC1M,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,SAAS,EAAE;AAC7E,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qMAAqM,CAAC,CAAC;AAC/N,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO;AAC1B,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,MAAM,iBAAiB,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO;AACnS,EAAE,MAAM,YAAY,GAAG,CAAC,iBAAiB,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,SAAS;AACpJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2IAA2I,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC1P,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iGAAiG,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,8NAA8N,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,wKAAwK,CAAC,CAAC;AAC3kB,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;AACjC,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,mFAAmF,CAAC,CAAC;AAC7G,IAAI,QAAQ,CAAC,SAAS,EAAE;AACxB,MAAM,SAAS,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS;AACvE,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,wbAAwb,CAAC,CAAC;AACld,IAAI,UAAU,CAAC,SAAS,EAAE;AAC1B,MAAM,OAAO,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,OAAO;AAC9E,MAAM,aAAa,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,aAAa;AAC1F,MAAM,UAAU,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AAC1E,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qDAAqD,CAAC,CAAC;AAC/E,IAAI,YAAY,CAAC,SAAS,EAAE;AAC5B,MAAM,SAAS,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS;AACvE,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,CAAC;AACnD,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,GAAG,EAAE;AACP;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC5B,EAAE,IAAI,UAAU,GAAG,QAAQ,IAAI,CAAC;AAChC,EAAE;AACF;AACA,IAAI,CAAC;AACL,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,uTAAuT,EAAE,UAAU,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AACviB,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC;AACvD,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;AAC1D,EAAE,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC;AACpD,EAAE,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,MAAM;AACnD,EAAE,CAAC,CAAC;AACJ,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,6wBAA6wB,CAAC,CAAC;AACvyB,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACrH,IAAI,CAAC,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2EAA2E,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,qKAAqK,CAAC,CAAC;AACtS,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AACtE,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;AAChC,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC;AAC7C,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,CAAC;AAClC,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;AACnG,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yDAAyD,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,6NAA6N,CAAC,CAAC;AAC7W,EAAE,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,EAAE,EAAE;AACzE,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC;AAC/B,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,+DAA+D,EAAE,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,wCAAwC,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC;AACvV,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAClD,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACvC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC;AACpC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2JAA2J,CAAC,CAAC;AACnL,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACvD,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACxC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,CAAC;AACnC;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,IAAI,MAAM,GAAG,cAAc;AAC7B,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,cAAc,GAAG,KAAK;AAC5B,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,SAAS,GAAG,EAAE;AACpB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;AAC/B,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AACpC,EAAE,SAAS,SAAS,CAAC,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE;AAC9C,IAAI,YAAY,GAAG,OAAO;AAC1B,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,cAAc,GAAG,IAAI;AACzB,EAAE;AACF,EAAE,SAAS,gBAAgB,GAAG;AAC9B,EAAE;AACF,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACrC,IAAI,MAAM,CAAC,aAAa,GAAG,SAAS;AACpC,EAAE;AACF,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC;AACxE,EAAE,CAAC,CAAC;AACJ,EAAE,UAAU,CAAC,SAAS,EAAE;AACxB,IAAI,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AACtE,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO,EAAE;AAC7F,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC;AACtC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,iEAAiE,CAAC,CAAC;AACzF,EAAE,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;AAC5F,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,OAAO,CAAC,SAAS,EAAE;AACvB,MAAM,KAAK,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,KAAK;AAC1E,MAAM,IAAI,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,IAAI;AACxE,MAAM,SAAS,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,SAAS;AAClF,MAAM,KAAK,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,KAAK;AAC1E,MAAM,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,QAAQ;AAChF,MAAM,aAAa,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,QAAQ,EAAE,KAAK,IAAI,IAAI;AAC5G,MAAM,aAAa,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,QAAQ,EAAE,KAAK,IAAI,IAAI;AAC5G,MAAM,eAAe,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,OAAO;AAC9F,MAAM,YAAY,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,QAAQ,EAAE;AAC9F,KAAK,CAAC;AACN,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,0EAA0E,CAAC,CAAC;AAClG,EAAE,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC5D,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,OAAO,CAAC,SAAS,EAAE;AACrB,IAAI,IAAI,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,KAAK;AAC7F,IAAI,UAAU,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AACxE,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,UAAU,CAAC,SAAS,EAAE;AACxB,IAAI,SAAS,EAAE,cAAc;AAC7B,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,OAAO,EAAE;AACb,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChC,EAAE,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,KAAK,EAAE;AACnL,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,iBAAiB,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,eAAe,GAAG,CAAC,eAAe,EAAE,CAAC;AACxF,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,gBAAgB,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;AAClE,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;AACjC,EAAE,CAAC,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;AACnC,EAAE;AACF,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,GAAG,EAAE;AACP;;;;"}