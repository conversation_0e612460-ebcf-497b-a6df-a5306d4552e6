import type { SocketClient } from '$lib/socket';
import { gameActions } from '$lib/stores';
import MumsNumbers from './MumsNumbers.svelte';
import { mount, unmount } from 'svelte';

export interface GameConfig {
  gameId: string;
  containerId: string;
  socketClient: SocketClient;
  onScoreUpdate?: (score: number) => void;
  onGameComplete?: (finalScore: number) => void;
}

export class MumsNumbersGame {
  private gameId: string;
  private containerId: string;
  private socketClient: SocketClient;
  private container: HTMLElement | null = null;
  private svelteComponent: any = null;
  private config: GameConfig;

  constructor(config: GameConfig) {
    this.gameId = config.gameId;
    this.containerId = config.containerId;
    this.socketClient = config.socketClient;
    this.config = config;
  }

  async init(): Promise<void> {
    gameActions.updateLoadingProgress(1);
    gameActions.preloadComplete();

    console.log(`Initializing Mums Numbers game in container: ${this.containerId}`);
    
    this.container = document.getElementById(this.containerId);
    if (!this.container) {
      throw new Error(`Container element with ID "${this.containerId}" not found`);
    }

    // Clear container
    this.container.innerHTML = '';
    
    // Create the Svelte component
    this.svelteComponent = mount(MumsNumbers, {
      target: this.container,
      props: {
        socketClient: this.socketClient,
        gameId: this.gameId,
        containerId: this.containerId,
        onScoreUpdate: this.config.onScoreUpdate,
        onGameComplete: this.config.onGameComplete
      }
    });

    console.log('Mums Numbers game initialized successfully');
  }
  
  start(): void {
    console.log('Mums Numbers game started');
    // Send initialization request to server
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.initGame();
    } else {
      console.error(
        "No server connection available - cannot initialize game"
      );
    }

    // The game starts automatically when the component is mounted
    // No additional start logic needed as the game begins when user draws
    setTimeout(() => {
      // this.svelteComponent.init();

      gameActions.startGame();
      this.svelteComponent.startGame();

      console.log('Game started');
    }, 5200);
  }

  pause(): void {
    console.log('Mums Numbers game paused');
    // Could implement pause functionality if needed
  }

  resume(): void {
    console.log('Mums Numbers game resumed');
    // Could implement resume functionality if needed
  }

  destroy(): void {
    console.log('Destroying Mums Numbers game');
    
    if (this.svelteComponent) {
      unmount(this.svelteComponent);
      this.svelteComponent = null;
    }
    
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}

// Export for use in the main games index
export { MumsNumbers };
export default MumsNumbersGame;
