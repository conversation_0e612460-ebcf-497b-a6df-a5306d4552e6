{"name": "client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "node build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@iconify/svelte": "5.0.1", "@sveltejs/adapter-node": "^5.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "phaser": "^3.90.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "vite": "^7.0.4"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@pixi/graphics": "^7.4.3", "@pixi/text": "^7.4.3", "pixi.js": "^8.13.2", "socket.io-client": "^4.8.1"}}