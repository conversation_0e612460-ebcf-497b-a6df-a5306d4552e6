<script lang="ts">
  /**
   * Game Page Component
   *
   * This page accepts a "token" URL parameter containing a JWT with game session data:
   * - gameId: The game identifier
   * - roomId: Multiplayer room identifier
   * - scoreSubmitId: Unique ID for score submission tracking
   * - authToken: Authentication token for the game server
   *
   * Example URL: /game/[gameId]?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   */
  import { page } from "$app/state";

  import * as utilsModule from "$lib/utils";
  import * as socketModule from "$lib/socket";
  import * as gamesModule from "$lib/games";

  import {
    Preloading,
    StartScreen,
    GameHUD,
    Countdown,
    EndGame,
    ErrorModal,
    PopupLeaderboard,
    LeaderboardToggle,
  } from "$lib/components";

  import { GameStatus } from "$lib/stores";
  import { gameState, gameActions } from "$lib/stores";
  import { opponentState } from "$lib/stores/opponent";
  import { roomState, roomActions, RoomStatus } from "$lib/stores/roomState";
  import { get } from "svelte/store";

  import type { SocketClient } from "$lib/socket";

  let showLeaderboard = $state(false);
  let roomId = $state("default-room");
  $effect(() => {
    roomId = get(gameState)?.roomId ?? "default-room";
  });

  let gameInstance: any = $state(null);
  let gameContainer: HTMLDivElement | undefined = $state();
  let showCountdown = $state(false);
  let showEndGame = $state(false);
  let postMessageHandler: any = $state(null);
  let socketClient: SocketClient | null = $state(null);
  let createGame: any = $state(null);

  // Error modal state
  let showErrorModal = $state(false);
  let errorMessage = $state("");
  let errorType = $state("");

  const gameId = $derived(page.params.id);
  let token = $derived(page.url.searchParams.get("token"));
  let hasInitialized = $state(false);

  $effect(() => {
    // Store JWT token for server-side authentication (no client-side decoding)
    // if (hasInitialized) {
    //   console.log("Token already initialized, skipping");
    //   return;
    // }
    hasInitialized = true;

    console.log("Game ID:", gameId);
    console.log("Token:", token);

    if (!token) {
      console.log("No token provided - running in development mode");
      return;
    }

    console.log("JWT token received, will be validated by server");

    // Store the token for socket authentication - use local variable to avoid reactivity
    const authToken = token;
    gameActions.setAuthToken(authToken);
    gameActions.setGameId(gameId);

    // Initialize room state
    roomActions.setGameId(gameId);
    roomActions.setConnected(false); // Will be set to true when socket connects

    postMessageHandler = utilsModule.postMessageHandler;

    // Handle async operations in a separate function
    const initializeGame = async () => {
      try {
        socketClient = socketModule.socketClient;

        if (!socketClient) {
          console.error("Socket client not available");
          return;
        }

        if (!authToken) {
          console.error("No token provided - cannot connect to server");
          return;
        }

        await socketClient.connect(authToken, {
          onScoreUpdate: (score: number) => {
            gameActions.updateScore(score);
          },
          onGameComplete: (_finalScore: number) => {
            gameActions.endGame();
            showEndGame = true;
          },
        });

        // Set room connection status when socket connects
        roomActions.setConnected(true);

        createGame = gamesModule.createGame(
          gameId,
          "game-container",
          socketClient
        );

        await createGame.init();
      } catch (error) {
        console.error("Failed to initialize game:", error);
      }
    };

    // Call the async function
    initializeGame();

    // Cleanup function (equivalent to onDestroy)
    return () => {
      if (gameInstance) {
        gameInstance.destroy();
      }
      if (socketClient) {
        socketClient.disconnect();
      }
      if (createGame) {
        createGame.destroy();
      }
      // Reset room state on cleanup
      roomActions.reset();
      // gameActions.resetGame();
    };
  });

  function handleStartGameButton() {
    console.log("Countdown complete");

    showCountdown = true;
    gameActions.initGame();
    createGame.start();
  }

  // Error modal functions
  function showError(message: string, type: string = "error") {
    errorMessage = message;
    errorType = type;
    showErrorModal = true;

    // Stop the game instance
    if (gameInstance) {
      gameInstance.scene.pause();
    }
  }

  function handleErrorClose() {
    // TODO: Post message trigeer back button
  }

  // Make error function globally available for Phaser scenes
  if (typeof window !== "undefined") {
    (window as any).showGameError = showError;
  }
</script>

<svelte:head>
  <title>TicTaps - {gameId}</title>
</svelte:head>

<Preloading progress={$gameState.loadingProgress} />

<!-- <span>{JSON.stringify($gameState)}</span> -->

{#if $gameState.status === GameStatus.Waiting}
  <StartScreen handleStartClick={handleStartGameButton} {gameId} />
{/if}

<div class="w-screen h-screen overflow-hidden relative">
  {#if $gameState.status === GameStatus.Active}
    <GameHUD
      score={$gameState.score}
      time={$gameState.time}
      totalTime={$gameState.totalTime}
      lives={$gameState.lives}
      maxLives={$gameState.maxLives}
      opponentScore={$opponentState.opponent?.score ?? null}
      opponentLives={$opponentState.opponent?.lives ?? null}
      opponentWaiting={$opponentState.waiting}
      opponentName={$opponentState.opponent?.name}
    />
  {/if}

  <div
    class="w-full h-full box-border"
    bind:this={gameContainer}
    id="game-container"
  >
    <!-- games will be mounted here -->
  </div>

  <Countdown show={showCountdown} duration={3} />

  <EndGame
    show={$gameState.status === GameStatus.Ended}
    finalScore={$gameState.score}
  />

  <ErrorModal
    isVisible={showErrorModal}
    {errorMessage}
    {errorType}
    onClose={handleErrorClose}
  />

  {#if $gameState.status === GameStatus.Active || $gameState.status === GameStatus.Ended}
    <!-- Leaderboard toggle and popup -->
    <LeaderboardToggle onToggle={() => (showLeaderboard = !showLeaderboard)} />
    <PopupLeaderboard {roomId} open={showLeaderboard} />
  {/if}
</div>
