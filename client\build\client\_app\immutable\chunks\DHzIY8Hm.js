const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./B3qBAdTc.js","./BwME0dYm.js","./DsnmJJEf.js","./4UAai7vz.js","./DJNDnN69.js","./DMnCbMI3.js","./D6Z45t_z.js","../assets/MumsNumbers.yT0SHS_s.css","./BlcsiWND.js","./DmtBy-2V.js","./ZjlMlVB3.js","./BpKJYiXk.js"])))=>i.map(i=>d[i]);
import"./DsnmJJEf.js";import{t as W,A as De,B as Ne,a1 as gt,aG as ft,O as We,aj as vt,al as pt,aq as bt,ao as Ct,av as Ue,F as yt,aH as St,am as me,p as ae,v as C,w as E,o as ie,a as Q,k as m,ac as q,b as I,c as ne,aI as Ze,aJ as wt,d as y,r as b,f as z,s as x,a0 as Je,g as Te,h as ve,l as kt,x as _t,aK as Tt,$ as xt}from"./4UAai7vz.js";import{o as Be,c as $e,s as Y,e as pe,w as Gt,m as Lt,u as Et,g as It,h as Bt}from"./DJNDnN69.js";import{i as H,r as Ot,p as N,s as xe,b as be,a as et,_ as He}from"./DMnCbMI3.js";import{p as je}from"./mXOxeudE.js";import{J as Pt,K as Mt,L as Xe,e as Oe,i as Pe,N as Me,a as Ce,O as ze,Q as Ge,V as p,g as w,W as zt,X as qe,Y as Rt,Z as Ft,_ as At,$ as ee,a0 as Dt,f as Re,E as de,d as Nt,u as Wt,C as re,H as ye,I as Se,y as _e,G as Ut,D as Le,a1 as Ht,a2 as jt,x as Ye,a3 as ue,a4 as Xt,a5 as qt,a6 as Ve,a7 as le,a8 as Yt,a9 as Vt}from"./BwME0dYm.js";import{T as Kt,t as Ke,u as Qt,v as oe}from"./BlcsiWND.js";import{i as tt}from"./C1M19Mmo.js";function Zt(r,e,t=!1,s=!1,i=!1){var a=r,n="";W(()=>{var l=gt;if(n===(n=e()??"")){De&&Ne();return}if(l.nodes_start!==null&&(ft(l.nodes_start,l.nodes_end),l.nodes_start=l.nodes_end=null),n!==""){if(De){We.data;for(var o=Ne(),d=o;o!==null&&(o.nodeType!==vt||o.data!=="");)d=o,o=pt(o);if(o===null)throw bt(),Ct;Ue(We,d),a=yt(o);return}var g=n+"";t?g=`<svg>${g}</svg>`:s&&(g=`<math>${g}</math>`);var h=St(g);if((t||s)&&(h=me(h)),Ue(me(h),h.lastChild),t||s)for(;me(h);)a.before(me(h));else a.before(h)}})}var Jt=wt("<svg><!></svg>"),$t=z("<span></span>");function se(r,e){ae(e,!0);const t={name:"",loading:null,destroyed:!1},s=Ot(e,["$$slots","$$events","$$legacy"]);let i=E(!1),a=E(0),n=q(()=>!!e.ssr||m(i)),l=q(()=>(m(a),Mt(e.icon,t,m(n),d,e.onload))),o=q(()=>{const v=m(l)?Pt(m(l).data,s):null;return v&&m(l).classes&&(v.attributes.class=(typeof s.class=="string"?s.class+" ":"")+m(l).classes.join(" ")),v});function d(){Ze(a)}Be(()=>{C(i,!0)}),$e(()=>{t.destroyed=!0,t.loading&&(t.loading.abort(),t.loading=null)});var g=ie(),h=Q(g);{var u=v=>{var k=ie(),T=Q(k);{var _=L=>{var B=Jt();Xe(B,()=>({...m(o).attributes}));var F=y(B);Zt(F,()=>m(o).body,!0),b(B),I(L,B)},S=L=>{var B=$t();Xe(B,()=>({...m(o).attributes})),I(L,B)};H(T,L=>{m(o).svg?L(_):L(S,!1)})}I(v,k)};H(h,v=>{m(o)&&v(u)})}I(r,g),ne()}var es=z('<img alt="Player avatar"/>'),ts=z("<div><!></div>"),ss=z('<div class="pointer-events-auto flex flex-col items-start gap-2"><div class="flex items-center gap-2"><!> <div class="flex flex-col"><div class="text-[1.8vh] text-left font-medium leading-tight"> </div> <div class="flex flex-row items-center gap-2"><div class="flex gap-1"></div> <span class="font-bold text-[2vh] align-middle"> </span></div></div></div></div>');function is(r,e){let t=N(e,"name",3,"You"),s=q(()=>e.compare==="win"?"border-green-400":e.compare==="lose"?"border-red-400":e.compare==="tie"?"border-yellow-400":"border-white/40");var i=ss(),a=y(i),n=y(a);{var l=_=>{var S=es();W(()=>{Me(S,"src",e.avatarUrl),Ce(S,1,`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${m(s)??""}`)}),I(_,S)},o=_=>{var S=ts(),L=y(S);se(L,{icon:"mdi:account",color:"white",height:"3vh"}),b(S),W(()=>Ce(S,1,`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${m(s)??""}`)),I(_,S)};H(n,_=>{e.avatarUrl?_(l):_(o,!1)})}var d=x(n,2),g=y(d),h=y(g,!0);b(g);var u=x(g,2),v=y(u);Oe(v,21,()=>Array(e.maxLives),Pe,(_,S,L)=>{var B=ie(),F=Q(B);{var j=P=>{se(P,{height:"2vh",color:"red",icon:"material-symbols:favorite"})},X=P=>{se(P,{height:"2vh",color:"red",icon:"material-symbols:favorite-outline"})};H(F,P=>{L<e.lives?P(j):P(X,!1)})}I(_,B)}),b(v);var k=x(v,2),T=y(k,!0);b(k),b(u),b(d),b(a),b(i),W(()=>{Y(h,t()),Y(T,e.score)}),I(r,i)}var as=z('<div class="text-[2vh] italic opacity-80">Waiting for player…</div>'),ns=z('<img alt="Opponent avatar"/>'),os=z("<div><!></div>"),rs=z('<div class="flex items-center gap-2"><div class="flex flex-col"><div class="text-[1.8vh] text-right font-medium leading-tight"> </div> <div class="flex flex-row items-center gap-2"><span class="font-bold text-[2vh] align-middle"> </span> <div class="flex gap-1"></div></div></div> <!></div>'),ls=z('<div class="pointer-events-auto flex items-center gap-2"><div class="flex flex-col items-end gap-1"><!></div></div>');function cs(r,e){let t=N(e,"waiting",3,!0),s=N(e,"score",3,null),i=N(e,"lives",3,null),a=N(e,"name",3,"Opponent"),n=q(()=>e.compare==="win"?"border-green-400":e.compare==="lose"?"border-red-400":e.compare==="tie"?"border-yellow-400":"border-white/40");var l=ls(),o=y(l),d=y(o);{var g=u=>{var v=as();I(u,v)},h=u=>{var v=rs(),k=y(v),T=y(k),_=y(T,!0);b(T);var S=x(T,2),L=y(S),B=y(L,!0);b(L);var F=x(L,2);Oe(F,21,()=>Array(e.maxLives),Pe,(A,R,Z)=>{var D=ie(),c=Q(D);{var f=O=>{se(O,{height:"2vh",color:"red",icon:"material-symbols:favorite"})},G=O=>{se(O,{height:"2vh",color:"red",icon:"material-symbols:favorite-outline"})};H(c,O=>{i()!==null&&Z<i()?O(f):O(G,!1)})}I(A,D)}),b(F),b(S),b(k);var j=x(k,2);{var X=A=>{var R=ns();W(()=>{Me(R,"src",e.avatarUrl),Ce(R,1,`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${m(n)??""}`)}),I(A,R)},P=A=>{var R=os(),Z=y(R);se(Z,{icon:"mdi:account",color:"white",height:"3vh"}),b(R),W(()=>Ce(R,1,`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${m(n)??""}`)),I(A,R)};H(j,A=>{e.avatarUrl?A(X):A(P,!1)})}b(v),W(()=>{Y(_,a()),Y(B,s())}),I(u,v)};H(d,u=>{t()||s()===null?u(g):u(h,!1)})}b(o),b(l),I(r,l)}var ds=z(`<div class="fixed left-0 right-0 z-10 px-[0vw] py-[0vh] flex flex-col items-center justify-around text-white"><div class="w-full pointer-events-none relative top-0 left-0 right-0 px-[4vw] py-[2vh] flex items-center justify-between"><!> <!></div> <div class="relative w-full flex items-center bg-black/20"><div class="absolute left-1/2 top-1/2 -translate-y-1/2 -translate-1/2 w-[10vh] h-[6vh]
      flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10
      font-medium text-[2.5vh]"> </div> <div class="relative w-full h-2 rounded-xl overflow-hidden"><div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear"></div></div></div></div>`);function hs(r,e){ae(e,!0);let t=N(e,"opponentScore",3,null),s=N(e,"opponentLives",3,null),i=N(e,"opponentWaiting",3,!0);function a(S){const L=Math.floor(S/60),B=Math.floor(S%60);return`${L.toString().padStart(2,"0")}:${B.toString().padStart(2,"0")}`}let n=q(()=>t()==null?null:e.score>t()?"win":e.score<t()?"lose":"tie"),l=q(()=>t()==null?null:t()>e.score?"win":t()<e.score?"lose":"tie");var o=ds(),d=y(o),g=y(d);is(g,{get score(){return e.score},get lives(){return e.lives},get maxLives(){return e.maxLives},get name(){return e.playerName},get avatarUrl(){return e.playerAvatarUrl},get compare(){return m(n)}});var h=x(g,2);cs(h,{get waiting(){return i()},get score(){return t()},get lives(){return s()},get maxLives(){return e.maxLives},get name(){return e.opponentName},get avatarUrl(){return e.opponentAvatarUrl},get compare(){return m(l)}}),b(d);var u=x(d,2),v=y(u),k=y(v,!0);b(v);var T=x(v,2),_=y(T);b(T),b(u),b(o),W(S=>{Y(k,S),ze(_,`width: ${e.time/e.totalTime*100}%;`)},[()=>a(e.time)]),I(r,o),ne()}var ms=z('<div class="fixed inset-0 bg-black/60 flex justify-center items-center z-[2000] svelte-vzg9ol"><img class="counter svelte-vzg9ol" alt=""/></div>');function us(r,e){ae(e,!0);let t=N(e,"duration",3,3),s=N(e,"show",3,!1),i=E(Je(t())),a=E(null);Te(()=>(s()&&(console.log("Starting countdown"),n()),l));function n(){C(i,t()),C(a,setInterval(()=>{Ze(i,-1),m(i)<0&&clearInterval(m(a))},1300),!0)}function l(){m(a)&&(clearInterval(m(a)),C(a,null))}var o=ie(),d=Q(o);{var g=h=>{var u=ms(),v=y(u);b(u),W(()=>Me(v,"src",`/assets/images/counter/${(m(i)===0?"GO":m(i))??""}.svg`)),I(h,u)};H(d,h=>{s()&&m(i)>=0&&h(g)})}I(r,o),ne()}var gs=z('<div class="absolute w-screen h-screen z-1000 flex flex-col justify-center items-center"><div class="background svelte-2ea9pu"></div>  <div class="w-80 h-4 bg-gray-700 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"></div></div> <p class="text-sm text-gray-300 mt-4"> </p></div>');function fs(r,e){let t=q(()=>e.progress>=1);var s=ie(),i=Q(s);{var a=n=>{var l=gs(),o=x(y(l),2),d=y(o);b(o);var g=x(o,2),h=y(g);b(g),b(l),W((u,v)=>{ze(d,`width: ${u??""}%`),Y(h,`${v??""}%`)},[()=>Math.max(0,Math.min(100,e.progress*100)),()=>Math.round(Math.max(0,Math.min(100,e.progress*100)))]),I(n,l)};H(i,n=>{m(t)||n(a)})}I(r,s)}var vs=z('<div class="error-type-badge svelte-jbysz9"> </div>'),ps=z('<div class="modal-backdrop svelte-jbysz9" role="dialog" aria-modal="true"><div class="modal-container svelte-jbysz9"><div class="modal-blur-bg svelte-jbysz9"></div> <div class="modal-content svelte-jbysz9"><div class="error-icon svelte-jbysz9"><svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-jbysz9"><circle cx="12" cy="12" r="10" stroke="#ff4444" stroke-width="2" fill="none" class="svelte-jbysz9"></circle><path d="M15 9l-6 6" stroke="#ff4444" stroke-width="2" stroke-linecap="round" class="svelte-jbysz9"></path><path d="M9 9l6 6" stroke="#ff4444" stroke-width="2" stroke-linecap="round" class="svelte-jbysz9"></path></svg></div> <div class="error-title svelte-jbysz9"><h2 class="svelte-jbysz9">Game Error</h2></div> <!> <div class="error-message svelte-jbysz9"><p class="svelte-jbysz9"> </p></div> <div class="modal-actions svelte-jbysz9"><button class="close-btn svelte-jbysz9"><span class="svelte-jbysz9">Close</span></button></div></div></div></div>');function bs(r,e){ae(e,!1);let t=N(e,"isVisible",8,!1),s=N(e,"errorMessage",8,""),i=N(e,"errorType",8,""),a=N(e,"onClose",8,()=>{});function n(){a()()}function l(h){h.target===h.currentTarget&&n()}tt();var o=ie(),d=Q(o);{var g=h=>{var u=ps(),v=y(u),k=x(y(v),2),T=x(y(k),4);{var _=X=>{var P=vs(),A=y(P,!0);b(P),W(R=>Y(A,R),[()=>(kt(i()),ve(()=>i().toUpperCase()))]),I(X,P)};H(T,X=>{i()&&X(_)})}var S=x(T,2),L=y(S),B=y(L,!0);b(L),b(S);var F=x(S,2),j=y(F);b(F),b(k),b(v),b(u),W(()=>Y(B,s())),pe("click",j,n),pe("click",u,l),I(h,u)};H(d,h=>{t()&&h(g)})}I(r,o),ne()}var Cs=z('<div class="row svelte-fpngv5"><div class="rank svelte-fpngv5"></div> <div class="userid svelte-fpngv5"> </div> <div class="score svelte-fpngv5"> </div> <div class="status svelte-fpngv5"> </div></div>'),ys=z('<div id="popup-leaderboard" class="overlay svelte-fpngv5"><div class="modal svelte-fpngv5"><div class="header svelte-fpngv5"><div class="title svelte-fpngv5">Room Leaderboard</div> <button class="close-btn svelte-fpngv5">×</button></div> <div class="list svelte-fpngv5"></div></div></div>');function Ss(r,e){ae(e,!1);const[t,s]=et(),i=()=>xe(o,"$leaderboard",t);let a=N(e,"roomId",8),n=N(e,"open",12,!1),l=_t(void 0);const o=Gt([]);function d(_){!_||_.roomId!==a()||o.set(_.leaderboard)}function g(){n(!n()),m(l)&&Tt(l,m(l).style.display=n()?"block":"none")}Be(()=>{const _=S=>d(S);return Ge.addCustomEventListener("leaderboard_update",_),()=>{Ge.removeCustomEventListener("leaderboard_update",_)}}),tt();var h=ys(),u=y(h),v=y(u),k=x(y(v),2);b(v);var T=x(v,2);Oe(T,5,i,Pe,(_,S,L)=>{var B=Cs(),F=y(B);F.textContent=L+1;var j=x(F,2),X=y(j,!0);b(j);var P=x(j,2),A=y(P,!0);b(P);var R=x(P,2),Z=y(R,!0);b(R),b(B),W(()=>{Y(X,(m(S),ve(()=>m(S).name??m(S).userId))),Y(A,(m(S),ve(()=>m(S).score))),Y(Z,(m(S),ve(()=>m(S).status)))}),I(_,B)}),b(T),b(u),b(h),be(h,_=>C(l,_),()=>m(l)),W(()=>ze(h,`display: ${n()?"block":"none"}`)),pe("click",k,g),I(r,h),ne(),s()}var ws=z('<button class="fixed bottom-4 right-4 z-[3500] rounded-full bg-[#151a22] border border-[#222a36] text-white shadow-lg p-3" aria-label="Toggle Leaderboard"><!></button>');function ks(r,e){let t=N(e,"onToggle",8);var s=ws(),i=y(s);se(i,{icon:"mdi:trophy",height:"24"}),b(s),pe("click",s,function(...a){t()?.apply(this,a)}),I(r,s)}let _s=class extends p.Scene{constructor(){super({key:"PreloadScene"})}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("block_active","/assets-finger-frenzy/images/block_active.png"),this.load.image("block_inactive","/assets-finger-frenzy/images/block_inactive.png"),this.load.image("game_name","/assets-finger-frenzy/images/game_name.png"),this.load.image("game_start","/assets/images/game_start.png"),this.load.image("timer_icon","/assets/images/timer_icon.png"),this.load.image("countdown-3","/assets/images/countdown-3.png"),this.load.image("countdown-2","/assets/images/countdown-2.png"),this.load.image("countdown-1","/assets/images/countdown-1.png"),this.load.image("countdown-go","/assets/images/countdown-go.png"),this.load.image("back_to_lobby","/assets/images/back_to_lobby.png"),this.load.image("game_bg","/assets/images/game_bg.png"),this.load.svg("heart","/assets/images/mdi--heart.svg"),this.load.svg("heart_outline","/assets/images/mdi-light--heart.svg"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.svg("button_bg","/assets/images/button_bg.svg"),this.load.svg("game_over","/assets/images/game_over.svg"),this.load.svg("timer_bg","/assets/images/timer_bg.svg"),this.load.image("timer_countdown_bg","/assets/images/timer_countdown_bg.png"),this.load.audio("click",["/assets/audio/click.ogg","/assets/audio/click.mp3","/assets/audio/click.wav"]),this.load.audio("wrong",["/assets/audio/wrong.ogg","/assets/audio/wrong.mp3","/assets/audio/wrong.wav"]),this.load.audio("countdown",["/assets/audio/countdown.ogg","/assets/audio/countdown.mp3","/assets/audio/countdown.wav"]),this.load.audio("go",["/assets/audio/go.mp3","/assets/audio/go.wav"]),this.load.audio("tap",["/assets-finger-frenzy/sounds/tap.ogg","/assets-finger-frenzy/sounds/tap.mp3","/assets-finger-frenzy/sounds/tap.wav"]),this.load.audio("right",["/assets-finger-frenzy/sounds/right.ogg","/assets-finger-frenzy/sounds/right.mp3","/assets-finger-frenzy/sounds/right.wav"]),this.load.audio("timeout",["/assets-finger-frenzy/sounds/timeout.ogg","/assets-finger-frenzy/sounds/timeout.mp3","/assets-finger-frenzy/sounds/timeout.wav"])}create(){}},we=class{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}hasNotifiedReady=!1;notifyGameReady(){console.log("notifyGameReady -- ENTER"),!this.hasNotifiedReady&&(this.hasNotifiedReady=!0,console.log("Notifying game ready"),this.sendMessage({type:"gameReady"}))}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}},Ts=class extends p.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new we,this.add.image(0,0,"game_bg").setOrigin(0,0).setDisplaySize(e,t);const s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8);s.setScale(i),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5);const a=Math.min(e*.6/this.startButton.width,.4);this.startButton.setScale(a);const n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5),l=this.startButton.displayWidth*.6/n.width;n.setScale(l),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.ticTaps.notifyGameReady(),this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:100,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:.7}),this.tweens.add({targets:s,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameScene")}})}})}},st=class{scene;config;score;scoreText;scoreLabel;container;events;constructor(e,t={}){this.scene=e,this.events=new p.Events.EventEmitter,this.config={initialScore:t.initialScore??0,fontFamily:t.fontFamily??"Arial",fontSize:t.fontSize??"80px",labelFontSize:t.labelFontSize??"28px",scoreColor:t.scoreColor??"#33DDFF",labelColor:t.labelColor??"#FFFFFF",animationColor:t.animationColor??"#ffff00",animationDuration:t.animationDuration??800},this.score=this.config.initialScore}createUI(e,t,s){this.container=this.scene.add.container(0,0),this.scoreLabel=this.scene.add.text(e,t-30,"Total Point",{fontFamily:this.config.fontFamily,fontSize:this.config.labelFontSize,fontStyle:"bold",color:this.config.labelColor}).setOrigin(.5),this.scoreText=this.scene.add.text(e,t+30,this.score.toString(),{fontFamily:this.config.fontFamily,fontSize:this.config.fontSize,fontStyle:"bold",color:this.config.scoreColor}).setOrigin(.5),this.container.add([this.scoreLabel,this.scoreText]),s&&s.add(this.container)}addPoints(e,t){this.score+=e,this.updateScoreDisplay(),t&&this.createScoreAnimation(t),w.updateScore(this.score),this.events.emit("scoreChanged",this.score,e)}subtractPoints(e,t){this.score=Math.max(0,this.score-e),this.updateScoreDisplay(),t&&this.createScoreAnimation(t),w.updateScore(this.score),this.events.emit("scoreChanged",this.score,-e)}setScore(e){const t=this.score;this.score=e,this.updateScoreDisplay(),this.events.emit("scoreChanged",this.score,this.score-t)}getScore(){return this.score}reset(){this.score=this.config.initialScore,this.updateScoreDisplay(),this.events.emit("scoreReset",this.score)}updateScoreDisplay(){this.scoreText&&this.scoreText.setText(this.score.toString())}createScoreAnimation(e){const t=this.scene.add.text(e.startX,e.startY,`+${e.points}`,{fontFamily:this.config.fontFamily,fontSize:"24px",color:e.color??this.config.animationColor,stroke:"#000000",strokeThickness:3});t.setOrigin(.5),this.scene.tweens.add({targets:t,y:e.startY-50,alpha:0,scale:1.2,duration:e.duration??this.config.animationDuration,ease:"Power2",onComplete:()=>{t.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.scoreText=void 0,this.scoreLabel=void 0,this.container=void 0}},Fe=class{scene;hearts=[];container;events;constructor(e){this.scene=e,this.events=new p.Events.EventEmitter}deductHeart(e,t,s){e!==void 0&&t!==void 0&&(s||this.container)?(console.log("deducting heart"),this.createFlyingHeartAnimation(e,t,s)):console.error("No coordinates provided for LivesManager",e,t,s)}createFlyingHeartAnimation(e,t,s){if(!this.container&&!s){console.error("No container found for LivesManager");return}const i=this.scene.add.image(e,t,"heart_broken").setOrigin(.5).setScale(1.5).setAlpha(.4);this.scene.tweens.add({targets:i,y:t-200,scale:3,alpha:.8,duration:600,ease:"Power2",onComplete:()=>{i.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.container=void 0}},xs=class extends p.Scene{blocks=[];gameEnd=!1;gridContainer;scoreManager;livesManager;ticTaps;socketClient=null;roomId="room-"+Date.now().toString(36);gameId="finger-frenzy";constructor(){super({key:"GameScene"}),this.ticTaps=new we}init(){this.gameEnd=!1;const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.roomId=e?.roomId||"room-"+Date.now().toString(36),this.gameId=e?.gameId||"finger-frenzy",this.setupSocketEventListeners(),this.scoreManager=new st(this,{initialScore:0,fontSize:"80px",scoreColor:"#33DDFF"}),this.livesManager=new Fe(this)}create(){this.createBackground(),this.createGrid(),this.initializeGame()}shutdown(){this.tweens.killAll(),this.cleanupSocketEventListeners(),this.scoreManager&&this.scoreManager.destroy(),this.livesManager&&this.livesManager.destroy()}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for FingerFrenzy GameScene"))}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Game initialized by server:",e),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="tile_tap"&&this.handleTileTapResult(e.data)}),this.socketClient.addCustomEventListener("game_error",e=>{console.error("Game error from server:",e)}),this.socketClient.addCustomEventListener("game_fatal_error",e=>{console.error("Fatal game error from server:",e),this.handleFatalError(e.message,e.errorType)}),console.log("Socket event listeners setup for FingerFrenzy GameScene"))}createBackground(){const{width:e,height:t}=this.cameras.main,s=this.textures.createCanvas("bgTexture",e,t),i=s?.getContext();if(i&&s){const a=i.createLinearGradient(0,0,0,t);a.addColorStop(0,"#212429"),a.addColorStop(1,"#1C1D22"),i.fillStyle=a,i.fillRect(0,0,e,t),s.refresh(),this.add.image(e/2,t/2,"bgTexture").setOrigin(.5)}else this.cameras.main.setBackgroundColor("#1C1D22")}initializeGame(){console.log("Initializing Finger Frenzy game..."),w.initGame(),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.gameEnd=!0,this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()&&this.socketClient.startGame()}createGrid(){const{width:e,height:t}=this.cameras.main,s=Math.min(e*.9,550),i=s*1.2;this.gridContainer=this.add.container(e/2,t*.6);const a=this.add.graphics();a.fillStyle(1710618,1),a.fillRoundedRect(-s/2,-i/2,s,i,20),a.lineStyle(4,4553205,1),a.strokeRoundedRect(-s/2,-i/2,s,i,20),this.gridContainer.add(a),this.blocks=[];const n=s*.03,l=(s-n*5)/4,o=(i-n*5)/4,d=-s/2+n,g=-i/2+n;for(let h=0;h<4;h++)for(let u=0;u<4;u++){const v=d+u*(l+n)+l/2,k=g+h*(o+n)+o/2,T=new zt(this,v,k,l,o,h,u);this.blocks.push(T),this.gridContainer.add(T),T.on("pointerdown",_=>this.onBlockClick(T,_))}}onBlockClick(e,t){if(this.gameEnd||!e||t.getDuration()>100)return;e.disableInteractive();const i=Date.now()-e.getActivationTime();if(console.log("reactionTime",i),this.socketClient&&this.socketClient.isConnected()){const a=this.getTileId(e);this.socketClient.sendTileTap(a,i)}}activateRandomBlock(e){if(this.gameEnd||this.blocks.filter(i=>i.getBlockActive()).length>=qe.INITIAL_ACTIVE_BLOCKS)return;const s=[];if(this.blocks.forEach((i,a)=>{!i.getBlockActive()&&!(e!==void 0&&a===e)&&s.push(a)}),s.length>0){const i=p.Utils.Array.GetRandom(s);this.blocks[i].setBlockActive(!0)}}startGame(){this.blocks.forEach(t=>t.reset());const e=[{row:1,col:2},{row:2,col:3},{row:0,col:1}];for(const t of e){const s=t.row*4+t.col;s<this.blocks.length&&this.blocks[s].setBlockActive(!0)}console.log("Game started with 3 active blocks")}endGame(){this.gameEnd||(this.gameEnd=!0,w.endGame(),this.sound.play("timeout"),this.blocks.forEach(e=>e.disableInteractive()))}quitGame(){this.endGame(),this.socketClient&&this.socketClient.isConnected()&&this.socketClient.endGame("manual")}getTileId(e){const t=this.blocks.indexOf(e),s=Math.floor(t/4),i=t%4;return`block_${s}_${i}`}handleTileTapResult(e){console.log("Handling tile tap result:",e);const t=this.blocks.find(s=>s.getTileId()===e.tileId);if(!t){console.warn("Block not found for tile ID:",e.tileId);return}console.log("Block found:",t.getTileId()),e.isCorrect?(this.sound.play("right"),t.setBlockActive(!1),this.scoreManager.addPoints(e.points,{startX:this.gridContainer.x+t.x,startY:this.gridContainer.y+t.y,color:"#ffff00",points:e.points})):(this.sound.play("wrong"),t.setBlockWrong(),w.updateLives(e.newLives),this.scoreManager.subtractPoints(e.points,{startX:this.gridContainer.x+t.x,startY:this.gridContainer.y+t.y,color:"#ff0000",points:e.points}),this.livesManager.deductHeart(this.gridContainer.x+t.x,this.gridContainer.y+t.y,this.gridContainer)),this.time.delayedCall(qe.COOLDOWN_DURATION*1e3,()=>{t.setInteractive({useHandCursor:!0})}),e.gameState&&w.updateScore(e.gameState.score),e.gridState&&this.syncGridState(e.gridState),e.gameEnded&&this.endGame()}syncGridState(e){!e||!e.blocks||e.blocks.forEach(t=>{const s=t.index;s>=0&&s<this.blocks.length&&this.blocks[s].setBlockActive(t.isActive)})}},Gs=class extends p.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene"),this.ticTaps=new we}init(e){this.score=e.score||0,console.log("GameEndScene init - Score:",this.score)}create(){this.ticTaps=new we,this.add.image(0,0,"game_bg").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===p.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===p.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,Ls(o,2,2,e,t,18),a.refresh();const g=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(g)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const l=new p.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(l,p.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const l="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(l)&&this.textures.remove(l);const o=Math.max(400,s.length*i*.7),d=i*1.5,g=this.textures.createCanvas(l,o,d);if(!g)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=g.getContext(),u=h.createLinearGradient(0,0,o,d*.5);return a?(u.addColorStop(0,"#4cffae"),u.addColorStop(.4,"#32c4ff"),u.addColorStop(1,"#5c67ff")):n?(u.addColorStop(0,"#32c4ff"),u.addColorStop(.5,"#7f54ff"),u.addColorStop(1,"#b63efc")):(u.addColorStop(0,"#33DDFF"),u.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=u,h.fillText(s,o/2,d/2),g.refresh(),this.add.image(e,t,l).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function Ls(r,e,t,s,i,a,n,l){r.beginPath(),r.moveTo(e+a,t),r.lineTo(e+s-a,t),r.quadraticCurveTo(e+s,t,e+s,t+a),r.lineTo(e+s,t+i-a),r.quadraticCurveTo(e+s,t+i,e+s-a,t+i),r.lineTo(e+a,t+i),r.quadraticCurveTo(e,t+i,e,t+i-a),r.lineTo(e,t+a),r.quadraticCurveTo(e,t,e+a,t),r.closePath(),r.stroke()}class Es{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing FingerFrenzy game...");const e={type:p.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:this.config.containerId,scene:[_s,Ts,xs,Gs],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:p.Scale.EXPAND,autoCenter:p.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new p.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting FingerFrenzy game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing FingerFrenzy game...")}resume(){console.log("Resuming FingerFrenzy game...")}destroy(){console.log("Destroying FingerFrenzy game..."),this.gameInstance&&(this.gameInstance=null)}getCurrentScore(){return 0}}let Is=class extends p.Scene{constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("game_background","/assets/images/game_bg.png"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.audio("click",["/assets/audio/click.mp3","/assets/audio/click.ogg"]),this.load.audio("wrong",["/assets/audio/wrong.ogg","/assets/audio/wrong.mp3","/assets/audio/wrong.wav"]),this.load.audio("countdown",["/assets/audio/countdown.mp3","/assets/audio/countdown.ogg"]),this.load.audio("go",["/assets/audio/go.mp3","/assets/audio/go.wav"]),this.load.audio("match",["/assets-bingo/audio/match.mp3","/assets-bingo/audio/match.ogg"]),this.load.audio("win",["/assets-bingo/audio/win.mp3","/assets-bingo/audio/win.ogg"]),this.load.audio("number-appear",["/assets-bingo/audio/number_appear.mp3","/assets-bingo/audio/number_appear.ogg"])}create(){}},it=class{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}notifyGameReady(){this.sendMessage({type:"gameReady"})}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}},Bs=class extends p.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new it,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(e,t);const s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8);s.setScale(i),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5);const a=Math.min(e*.6/this.startButton.width,.4);this.startButton.setScale(a);const n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5),l=this.startButton.displayWidth*.6/n.width;n.setScale(l),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.ticTaps.notifyGameReady(),this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:100,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:.7}),this.tweens.add({targets:s,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameScene")}})}})}};const Os={COLUMNS:["B","I","N","G","O"]},Ps={horizontal:"LINE BINGO!",vertical:"LINE BINGO!",diagonal:"DIAGONAL BINGO!",fullCard:"FULL CARD!"};let Ms=class at extends p.Scene{static BINGO_COLUMNS=Os.COLUMNS;gameEnd;UIContainer;socketClient=null;gameId="bingo";roomId="room-"+Date.now().toString(36);bingoCard;bgoCells;rightNumbers;rightPositions;countdownPanel;countdownText;scoreManager;livesManager;cameraWidth;cameraHeight;centerX;centerY;constructor(){super({key:"GameScene"})}init(){const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.gameId=e?.gameId||"bingo",this.roomId=e?.roomId||"room-"+Date.now().toString(36),this.UIContainer=this.add.container(0,0),this.socketClient&&this.setupSocketEventListeners(),this.scoreManager=new st(this,{initialScore:0,fontSize:"80px",scoreColor:"#33DDFF"}),this.livesManager=new Fe(this)}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Bingo game initialized by server:",e),console.log("Received bingoGameState:",e.bingoGameState),e.bingoGameState?(console.log("Syncing bingo card from server:",e.bingoGameState.bingoCard),this.syncGameStateFromServer(e.bingoGameState)):console.error("No bingoGameState received from server!"),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Bingo game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="cell_mark"&&this.handleCellMarkResult(e.data)}),this.socketClient.addCustomEventListener("number_called",e=>{console.log("Number called by server:",e),this.handleNumberCalled(e.calledNumber,e.gameState)}),this.socketClient.addCustomEventListener("ended",e=>{console.log("Game ended by server:",e),this.endGame()}),this.socketClient.addCustomEventListener("game_error",e=>{console.error("Game error from server:",e)}),this.socketClient.addCustomEventListener("game_fatal_error",e=>{console.error("Fatal game error from server:",e),this.handleFatalError(e.message,e.errorType)}),console.log("Socket event listeners setup for Bingo GameScene"))}syncGameStateFromServer(e){console.log("Syncing game state from server:",e),e.bingoCard?(console.log("Setting bingo card from server:",e.bingoCard),this.bingoCard=e.bingoCard):console.warn("No bingoCard in server game state")}handleCellMarkResult(e){const{cellId:t,isCorrect:s,points:i,newScore:a,newLives:n,gameEnded:l,winResult:o,gameState:d,matchedCalledNumber:g}=e;d&&this.syncGameStateFromServer(d);const h=this.findBingoCellById(t);if(h&&s){if(this.scoreManager.addPoints(i,{startX:h.x,startY:h.y,points:i}),h.mark(),this.sound.play("match"),g){const u=`${g.column}${g.number}`,v=this.rightNumbers.find(k=>k.name===u);v&&(this.children.list.forEach(k=>{k.type==="Graphics"&&k.x===h.x-40&&k.y===h.y-40&&k.destroy()}),this.rightNumbers=this.rightNumbers.filter(k=>k!==v),v.destroy())}}else s||(this.sound.play("wrong"),w.updateLives(e.newLives),h&&this.UIContainer&&this.livesManager.deductHeart(h.x,h.y,this.UIContainer));l&&(o?.hasWon?this.handleBingoWin(o):this.endGame())}handleNumberCalled(e,t){this.syncGameStateFromServer(t),this.addRightItemFromServer(e)}addRightItemFromServer(e){if(this.gameEnd)return;this.sound.play("number-appear"),this.moveExistingRightItems();const t=this.getRightPosition(4),s=new Rt(this,t.x,t.y,4,e.column,e.number);this.rightNumbers.push(s)}create(){this.cameraWidth=this.cameras.main.width,this.cameraHeight=this.cameras.main.height,this.centerX=this.cameraWidth/2,this.centerY=this.cameraHeight/2,this.gameEnd=!1,this.bgoCells=[],this.rightNumbers=[],this.rightPositions=this.calculateRightPositions(),this.createBackground(),this.initializeGame()}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("number_called",()=>{}),this.socketClient.removeCustomEventListener("ended",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for Bingo GameScene"))}shutdown(){this.cleanupSocketEventListeners(),this.scoreManager&&this.scoreManager.destroy()}createBackground(){this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameraWidth,this.cameraHeight)}createCountdownOverlay(){this.countdownPanel=this.add.container(0,0),this.countdownPanel.setDepth(2);const e=this.add.rectangle(0,0,this.cameraWidth,this.cameraHeight,0,.7).setOrigin(0,0);this.countdownPanel.add(e),this.countdownText=this.add.image(this.centerX,this.centerY,"countdown-3").setScale(0).setOrigin(.5),this.countdownPanel.add(this.countdownText)}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}initializeGame(){console.log("Initializing Bingo game..."),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()?this.socketClient.startGame():console.error("No server connection available - cannot start Bingo game")}startGame(){if(!this.bingoCard||this.bingoCard.length===0){console.error("No bingo card data received from server!");return}console.log("Starting game with server-provided bingo card:",this.bingoCard),this.createBingoBoard()}createBingoBoard(){if(console.log("Creating bingo board with server data:",this.bingoCard),!this.bingoCard||this.bingoCard.length===0){console.error("Cannot create bingo board: no server data available");return}const e=80,t=10,s=(e+t)*5-t,i=this.centerX-s/2,n=this.centerY+150-(e+t)*2.5,l=n+(e+t);for(let o=0;o<5;o++){const d=i+o*(e+t)+e/2,g=n,h=this.add.graphics();h.fillGradientStyle(3172095,4674303,6180351,2187007,1),h.lineStyle(3,58798,1),h.fillRoundedRect(d-e/2,g-e/2,e,e,16),h.strokeRoundedRect(d-e/2,g-e/2,e,e,16),this.add.text(d,g,at.BINGO_COLUMNS[o],{fontFamily:'"TT Neoris", Arial, sans-serif',fontSize:"48px",color:"#FFFFFF",align:"center",fontStyle:"bold",stroke:"#000000",strokeThickness:3,shadow:{offsetX:2,offsetY:2,color:"#000000",blur:2,fill:!0}}).setOrigin(.5)}for(let o=0;o<5;o++)for(let d=0;d<5;d++){const g=i+d*(e+t)+e/2,h=l+o*(e+t),u=this.bingoCard[o][d],v=new Ft(this,g,h,u.column,u.isFree?0:u.number,u.isFree);u.isFree||(v.letterText.alpha=0),v.setScale(0),this.bgoCells.push(v),this.time.delayedCall(150*o+50*d,()=>{this.tweens.add({targets:v,scale:1,duration:300,ease:"Back.Out"})})}}calculateRightPositions(){const e=[],a=this.centerX-220,n=this.centerY-180;for(let l=0;l<5;l++)e.push({x:a+l*90+80/2,y:n});return e}getRightPosition(e){return e>=0&&e<this.rightPositions.length?this.rightPositions[e]:{x:this.centerX+100,y:this.centerY}}moveExistingRightItems(){for(const e of this.rightNumbers)e.moveToPosition(e.rightIndex-1);this.rightNumbers=this.rightNumbers.filter(e=>e.rightIndex>=0)}checkForMatch(e){if(!this.gameEnd){if(this.socketClient&&this.socketClient.isConnected()){this.socketClient.sendGameAction("cell_mark",{cellId:e.name});return}this.checkForMatchLocal(e)}}checkForMatchLocal(e){const t=this.rightNumbers.find(s=>s.name===e.name);t?(e.mark(),this.children.list.forEach(s=>{s.type==="Graphics"&&s.x===e.x-40&&s.y===e.y-40&&s.destroy()}),this.sound.play("match"),this.rightNumbers=this.rightNumbers.filter(s=>s!==t),t.destroy()):this.sound.play("wrong")}handleBingoWin(e){this.gameEnd||(this.gameEnd=!0,this.endGame(),this.createWinAnnouncement(e),this.createCelebrationEffects())}createWinAnnouncement(e){const t=this.add.text(this.centerX,this.centerY-100,Ps[e.pattern],{fontFamily:'"TT Neoris", Arial, sans-serif',fontSize:"64px",color:"#FFD700",align:"center",fontStyle:"bold",stroke:"#000000",strokeThickness:4,shadow:{offsetX:3,offsetY:3,color:"#000000",blur:5,fill:!0}}).setOrigin(.5).setDepth(10);t.setScale(0),this.tweens.add({targets:t,scale:1,duration:500,ease:"Back.Out",onComplete:()=>{this.tweens.add({targets:t,scale:{from:1,to:1.1},duration:800,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"})}})}findBingoCellById(e){return this.bgoCells.find(t=>t.name===e)||null}findBingoCellByName(e){return this.bgoCells.find(t=>t.name===e)||null}endGame(){this.gameEnd||(this.gameEnd=!0,w.endGame(),this.sound.play("timeout"),this.createCelebrationEffects())}createCelebrationEffects(){this.sound.play("win");for(const e of this.bgoCells)e.marked||e.mark(),this.time.delayedCall(Math.random()*1e3,()=>{e.createWinParticles()})}},zs=class extends p.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene")}init(e){this.score=e.score||0}create(){this.ticTaps=new it,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===p.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===p.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,Rs(o,2,2,e,t,18),a.refresh();const g=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(g)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const l=new p.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(l,p.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const l="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(l)&&this.textures.remove(l);const o=Math.max(400,s.length*i*.7),d=i*1.5,g=this.textures.createCanvas(l,o,d);if(!g)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=g.getContext(),u=h.createLinearGradient(0,0,o,d*.5);return a?(u.addColorStop(0,"#4cffae"),u.addColorStop(.4,"#32c4ff"),u.addColorStop(1,"#5c67ff")):n?(u.addColorStop(0,"#32c4ff"),u.addColorStop(.5,"#7f54ff"),u.addColorStop(1,"#b63efc")):(u.addColorStop(0,"#33DDFF"),u.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=u,h.fillText(s,o/2,d/2),g.refresh(),this.add.image(e,t,l).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function Rs(r,e,t,s,i,a,n,l){r.beginPath(),r.moveTo(e+a,t),r.lineTo(e+s-a,t),r.quadraticCurveTo(e+s,t,e+s,t+a),r.lineTo(e+s,t+i-a),r.quadraticCurveTo(e+s,t+i,e+s-a,t+i),r.lineTo(e+a,t+i),r.quadraticCurveTo(e,t+i,e,t+i-a),r.lineTo(e,t+a),r.quadraticCurveTo(e,t,e+a,t),r.closePath(),r.stroke()}class Fs{config;gameInstance=null;constructor(e){this.config=e;const t={type:p.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:e.containerId,scene:[Is,Bs,Ms,zs],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:p.Scale.EXPAND,autoCenter:p.Scale.CENTER_BOTH},render:{antialias:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new p.Game(t),this.gameInstance.registry.set("gameConfig",this.config)}async init(){console.log("Initializing Bingo game...")}start(){console.log("Starting Bingo game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing Bingo game...")}resume(){console.log("Resuming Bingo game...")}destroy(){console.log("Destroying Bingo game..."),this.gameInstance&&(this.gameInstance.destroy(!0),this.gameInstance=null)}getCurrentScore(){return 0}}const K={BACKGROUND_COLOR:"#0E0F1E",CARD_SIZE:150,ANIMAL_COUNT:4,COLOR_COUNT:3,OPTION_CARDS_COUNT:5},As={0:6750207,1:6750054,2:16777062};let Ds=class extends p.Scene{animalImages=[];constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("card_bg","/assets-matching-mayhem/images/card_bg.svg"),this.load.image("card_correct_bg","/assets-matching-mayhem/images/card_correct_bg.png"),this.load.image("card_incorrect_bg","/assets-matching-mayhem/images/card_incorrect_bg.png"),this.load.image("timer_bg","/assets/images/timer_bg.svg"),this.load.image("timer_icon","/assets/images/timer_icon.png"),this.load.image("timer_countdown_bg","/assets/images/timer_countdown_bg.png"),this.load.svg("heart","/assets/images/mdi--heart.svg"),this.load.svg("heart_outline","/assets/images/mdi-light--heart.svg"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.image("game_name","/assets-matching-mayhem/images/game_name.svg"),this.load.image("game_background","/assets/images/game_bg.png"),this.load.image("button_bg","/assets/images/button_bg.svg"),this.load.image("game_start","/assets/images/game_start.png"),this.load.image("game_over","/assets/images/game_over.svg"),this.load.image("back_to_lobby","/assets/images/back_to_lobby.png");for(let e=0;e<K.ANIMAL_COUNT;e++)this.animalImages[e]=[];for(let e=0;e<K.ANIMAL_COUNT;e++)for(let t=0;t<K.COLOR_COUNT;t++){const s=`image_${t}_${e}`;this.load.image(s,`/assets-matching-mayhem/images/${t}/${e}.png`),this.animalImages[e][t]=s}this.load.audio("countdown",["/assets/audio/countdown.mp3","/assets/audio/countdown.wav"]),this.load.audio("go",["/assets/audio/go.mp3","/assets/audio/go.wav"]),this.load.audio("wrong",["/assets/audio/wrong.mp3","/assets/audio/wrong.wav"]),this.load.audio("end",["/assets-matching-mayhem/sounds/end.mp3","/assets-matching-mayhem/sounds/end.wav"]),this.load.audio("correct",["/assets-matching-mayhem/sounds/correct.mp3","/assets-matching-mayhem/sounds/correct.wav"]),this.load.audio("round",["/assets-matching-mayhem/sounds/round.mp3","/assets-matching-mayhem/sounds/round.wav"]),this.load.audio("laser",["/assets-matching-mayhem/sounds/laser.mp3","/assets-matching-mayhem/sounds/laser.wav"]),this.load.image("countdown-3","/assets/images/countdown-3.png"),this.load.image("countdown-2","/assets/images/countdown-2.png"),this.load.image("countdown-1","/assets/images/countdown-1.png"),this.load.image("countdown-go","/assets/images/countdown-go.png"),this.load.bitmapFont("game_font","/assets-matching-mayhem/fonts/font.png","/assets-matching-mayhem/fonts/font.xml")}getAnimalImageKey(e,t){return e>=0&&e<this.animalImages.length&&t>=0&&t<this.animalImages[e].length?this.animalImages[e][t]:(console.error(`Invalid animal or color index: ${e}, ${t}`),"")}create(){this.game.registry.set("animalImages",this.animalImages)}},nt=class{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}notifyGameReady(){this.sendMessage({type:"gameReady"})}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}},Ns=class extends p.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new nt,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(e,t);const s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8);s.setScale(i),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5);const a=Math.min(e*.6/this.startButton.width,.4);this.startButton.setScale(a);const n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5),l=this.startButton.displayWidth*.6/n.width;n.setScale(l),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.ticTaps.notifyGameReady(),this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:100,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:.7}),this.tweens.add({targets:s,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameScene")}})}})}};class Ws{scene;config;container;backgroundGraphics;progressGraphics;maskGraphics;constructor(e,t){this.scene=e,this.config={x:t.x,y:t.y,size:t.size,cornerRadius:t.cornerRadius??16,backgroundColor:t.backgroundColor??3355443,backgroundAlpha:t.backgroundAlpha??.6,borderWidth:t.borderWidth??8,startColor:t.startColor??"#ff4d4d",endColor:t.endColor??"#33ff55"}}create(e){this.container=this.scene.add.container(0,0),this.createBackground(),this.createProgress(),e&&e.add(this.container)}updateProgress(e){if(!this.progressGraphics||(e=Math.max(0,Math.min(1,e)),this.maskGraphics&&(this.maskGraphics.destroy(),this.maskGraphics=void 0),this.progressGraphics.clearMask(),this.progressGraphics.clear(),e<=0))return;const t=p.Display.Color.Interpolate.ColorWithColor(p.Display.Color.HexStringToColor(this.config.startColor),p.Display.Color.HexStringToColor(this.config.endColor),1,e),s=p.Display.Color.GetColor(t.r,t.g,t.b);if(this.maskGraphics=this.createRadialMask(e),this.progressGraphics.lineStyle(this.config.borderWidth,s,1),this.progressGraphics.strokeRoundedRect(this.config.x-this.config.size/2,this.config.y-this.config.size/2,this.config.size,this.config.size,this.config.cornerRadius),this.maskGraphics){const i=new p.Display.Masks.GeometryMask(this.scene,this.maskGraphics);this.progressGraphics.setMask(i),this.maskGraphics.setVisible(!1)}}createBackground(){this.container&&(this.backgroundGraphics=this.scene.add.graphics(),this.container.add(this.backgroundGraphics),this.backgroundGraphics.lineStyle(this.config.borderWidth,this.config.backgroundColor,this.config.backgroundAlpha),this.backgroundGraphics.strokeRoundedRect(this.config.x-this.config.size/2,this.config.y-this.config.size/2,this.config.size,this.config.size,this.config.cornerRadius))}createProgress(){this.container&&(this.progressGraphics=this.scene.add.graphics(),this.container.add(this.progressGraphics))}createRadialMask(e){this.maskGraphics=this.scene.add.graphics();const t=-Math.PI/2,s=2*Math.PI*e,i=t+s;this.maskGraphics.fillStyle(16777215,1),this.maskGraphics.beginPath(),this.maskGraphics.moveTo(this.config.x,this.config.y);const a=this.config.size*.8;return this.maskGraphics.arc(this.config.x,this.config.y,a,t,i,!1),this.maskGraphics.closePath(),this.maskGraphics.fillPath(),this.maskGraphics}setDepth(e){this.container&&this.container.setDepth(e)}destroy(){this.maskGraphics&&(this.maskGraphics.destroy(),this.maskGraphics=void 0),this.progressGraphics&&(this.progressGraphics.destroy(),this.progressGraphics=void 0),this.backgroundGraphics&&(this.backgroundGraphics.destroy(),this.backgroundGraphics=void 0),this.container&&(this.container.destroy(),this.container=void 0)}getContainer(){return this.container}}let Us=class extends p.Scene{animalImages=[];mainImage;optionCards=[];radialTimerUI;bonusScoreText;UIContainer;livesManager;gamePanel;isLocked=!1;isGameOver=!1;currentRoundTime=0;roundStartTime=0;roundDuration=5e3;timerUpdateEvent;socketClient=null;roomId="room-"+Date.now().toString(36);gameId="matching-mayhem";currentRoundData=null;isWaitingForServer=!1;constructor(){super("GameScene"),console.log("Matching Mayhem Game initialized")}init(){this.isGameOver=!1,this.isWaitingForServer=!1;const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.roomId=e?.roomId||"room-"+Date.now().toString(36),this.gameId=e?.gameId||"matching-mayhem",this.setupSocketEventListeners(),this.livesManager=new Fe(this)}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Matching Mayhem game initialized by server:",e),e.firstRound&&(console.log("First round data received from server:",e.firstRound),this.currentRoundData=e.firstRound,this.setupRoundFromServer(e.firstRound)),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Matching Mayhem game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="card_select"&&this.handleCardSelectResult(e.data)}),this.socketClient.addCustomEventListener("ended",e=>{console.log("Game ended by server:",e),this.endGame()}),this.socketClient.addCustomEventListener("game_error",e=>{console.error("Game error from server:",e)}),this.socketClient.addCustomEventListener("game_fatal_error",e=>{console.error("Fatal game error from server:",e),this.handleFatalError(e.message,e.errorType)}),console.log("Socket event listeners setup for Matching Mayhem GameScene"))}create(){this.animalImages=this.game.registry.get("animalImages")||[],this.cameras.main.setBackgroundColor(K.BACKGROUND_COLOR);const e=this.add.container(0,0);e.setDepth(-10);const t=this.cameras.main.width,s=this.cameras.main.height,i=this.textures.createCanvas("gradientBg",t,s);if(i){const n=i.getContext(),l=n.createRadialGradient(t/2,s/2,0,t/2,s/2,s*.8);l.addColorStop(0,"#151B30"),l.addColorStop(1,"#0E0F1E"),n.fillStyle=l,n.fillRect(0,0,t,s);for(let d=0;d<5e3;d++){const g=Math.random()*t,h=Math.random()*s,u=Math.random()*2,v=Math.random()*.05;n.fillStyle=`rgba(255, 255, 255, ${v})`,n.fillRect(g,h,u,u)}i.refresh();const o=this.add.image(0,0,"gradientBg").setOrigin(0,0);e.add(o),console.log("Created gradient background as fallback")}else{const n=this.add.rectangle(0,0,this.cameras.main.width,this.cameras.main.height,921374).setOrigin(0,0);e.add(n)}const a=this.add.image(0,0,"game_background").setOrigin(0,0);a.displayWidth=this.cameras.main.width,a.displayHeight=this.cameras.main.height,e.add(a),this.gamePanel=this.add.container(0,0),this.gamePanel.setVisible(!1),this.gamePanel.setDepth(1),this.createUI(),this.initializeGame()}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("ended",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for Matching Mayhem GameScene"))}shutdown(){this.cleanupSocketEventListeners(),this.stopLocalTimer(),this.radialTimerUI&&this.radialTimerUI.destroy(),this.livesManager&&this.livesManager.destroy()}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}initializeGame(){console.log("Initializing Matching Mayhem game..."),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()&&this.socketClient.startGame()}createUI(){this.UIContainer=this.add.container(0,0),this.gamePanel.add(this.UIContainer),this.createCenter(),this.createRoundTimerUI()}createCenter(){const t=this.cameras.main.width/2,s=this.cameras.main.height*.55,i=[{x:t-110,y:s-110},{x:t+110,y:s-110},{x:t,y:s},{x:t-110,y:s+110},{x:t+110,y:s+110}];for(let d=0;d<K.OPTION_CARDS_COUNT;d++){const g=new At(this,i[d].x,i[d].y,K.CARD_SIZE);this.optionCards.push(g),d!==2&&g.on("pointerdown",()=>{this.checkAnswer(g.getCardId())})}for(let d=0;d<K.OPTION_CARDS_COUNT;d++)d!==2&&(this.gamePanel.add(this.optionCards[d]),this.optionCards[d].setDepth(1));const a=this.optionCards[2],n=this.add.graphics();n.fillStyle(1579032,1);const l=K.CARD_SIZE*.9;n.fillRoundedRect(i[2].x-l/2,i[2].y-l/2,l,l,16),this.gamePanel.add(n),n.setDepth(8);const o=this.add.image(i[2].x,i[2].y,"card_bg").setOrigin(.5).setDisplaySize(K.CARD_SIZE+5,K.CARD_SIZE+5).setTint(3399167).setAlpha(.4);this.gamePanel.add(o),o.setDepth(9),this.gamePanel.add(a),a.setDepth(10),a.getCardBackground().setAlpha(1),a.disableInteractive(),console.log("Center card added to game panel"),console.log("Center card position:",a.x,a.y),console.log("Center card visible:",a.visible),console.log("Center card alpha:",a.alpha),console.log("Game panel children count:",this.gamePanel.list.length)}createRoundTimerUI(){const{width:e,height:t}=this.cameras.main;this.radialTimerUI=new Ws(this,{x:e/2,y:t*.55,size:K.CARD_SIZE*3,cornerRadius:16,borderWidth:8}),this.radialTimerUI.create(),this.radialTimerUI.setDepth(7);const s=this.radialTimerUI.getContainer();s&&this.gamePanel.add(s),this.radialTimerUI.updateProgress(1)}updateRadialTimer(e){this.radialTimerUI&&this.radialTimerUI.updateProgress(e)}startLocalTimer(){this.stopLocalTimer(),this.timerUpdateEvent=this.time.addEvent({delay:50,callback:this.updateLocalTimer,callbackScope:this,loop:!0})}stopLocalTimer(){this.timerUpdateEvent&&(this.timerUpdateEvent.destroy(),this.timerUpdateEvent=void 0)}updateLocalTimer(){if(this.isLocked||this.isGameOver){this.stopLocalTimer();return}const t=Date.now()-this.roundStartTime,s=Math.max(0,this.roundDuration-t),i=s/this.roundDuration;this.currentRoundTime=s/1e3,this.updateRadialTimer(i),s<=0&&this.stopLocalTimer()}startGame(){console.log("Starting game with server data"),w.startGame(),this.gamePanel.setVisible(!0),console.log("Game panel made visible")}setupRoundFromServer(e){console.log("Setting up round from server:",e),this.isLocked=!1,this.isWaitingForServer=!1,this.roundStartTime=e.startTime,this.roundDuration=e.timeLimit,this.currentRoundTime=e.timeLimit/1e3,this.updateRadialTimer(1),this.startLocalTimer(),this.setupCardsFromServerData(e)}setupCardsFromServerData(e){console.log("Setting up cards from server data:",e);const t=e.mainCard.imageKey;this.mainImage?.setTexture(t);for(let s=0;s<this.optionCards.length;s++){this.optionCards[s].resetSelection();const i=e.cards[s];i?(this.optionCards[s].setCardId(i.id),this.optionCards[s].setCardImage(i.imageKey),this.optionCards[s].setTint(As[i.colorIndex]),console.log(`Card ${s}: ${i.imageKey}, ID: ${i.id}, position: ${i.position}`)):console.error(`No card data provided for card ${s}`),this.optionCards[s].animateCardImage(200+s*50)}}checkAnswer(e){if(this.isLocked||this.isGameOver||this.isWaitingForServer)return;this.isLocked=!0,this.isWaitingForServer=!0;const t=this.currentRoundData?Date.now()-this.currentRoundData.startTime:0;console.log("Card selected:",e,"Reaction time:",t),this.socketClient&&this.socketClient.isConnected()?this.socketClient.sendCardSelect(e,t):(console.error("Socket client not connected - game requires server connection"),this.isLocked=!1,this.isWaitingForServer=!1)}findCardById(e){return this.optionCards.find(t=>t.getCardId()===e)||null}handleCardSelectResult(e){console.log("Handling card select result:",e),this.stopLocalTimer();const{cardId:t,isCorrect:s,points:i,newScore:a,newLives:n,gameEnded:l,nextRound:o,correctCardId:d}=e,g=this.findCardById(t),h=this.findCardById(d);h&&d!==t&&h.markSelected(!0),s?(this.sound.play("correct"),g&&g.markSelected(!0),w.updateScore(a),this.showBonusText(`Good Choice +${i}`,!0)):(this.sound.play("wrong"),g&&g.markSelected(!1),w.updateScore(a),w.updateLives(n),this.livesManager.deductHeart(this.cameras.main.width/2,this.cameras.main.height/2,this.gamePanel),this.showBonusText(`Bad Choice -${i}`,!1)),l?this.time.delayedCall(1e3,()=>{this.endGame()}):o?this.time.delayedCall(780,()=>{this.setupRoundFromServer(o)}):this.time.delayedCall(500,()=>{this.isLocked=!1,this.isWaitingForServer=!1})}showBonusText(e,t){!this.bonusScoreText||!(this.bonusScoreText instanceof p.GameObjects.Text)?(this.bonusScoreText=this.add.text(this.cameras.main.width/2,this.cameras.main.height/2.5,e,{fontFamily:"Arial",fontSize:"32px",fontStyle:"italic",color:t?"#4FFFAA":"#FF4F59",stroke:"#000000",strokeThickness:3,shadow:{offsetX:1,offsetY:1,color:"#000000",blur:2,stroke:!0,fill:!0}}).setOrigin(.5).setDepth(100).setAlpha(0),this.gamePanel.add(this.bonusScoreText)):(this.bonusScoreText.setText(e),this.bonusScoreText.setColor(t?"#4FFFAA":"#FF4F59"),this.bonusScoreText.setPosition(this.cameras.main.width/2,this.cameras.main.height/2.5));const s=this.cameras.main.height/2.5-50;this.tweens.killTweensOf(this.bonusScoreText),this.bonusScoreText.setAlpha(0),this.bonusScoreText.setPosition(this.cameras.main.width/2,this.cameras.main.height/2.5),this.tweens.add({targets:this.bonusScoreText,alpha:1,duration:200,ease:"Linear"}),this.tweens.add({targets:this.bonusScoreText,y:s,duration:700,ease:"Cubic.easeOut"}),this.tweens.add({targets:this.bonusScoreText,alpha:0,delay:600,duration:300,ease:"Linear"})}endGame(){this.isGameOver||(this.stopLocalTimer(),this.isGameOver=!0,w.endGame(),this.sound.play("end"))}},Hs=class extends p.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene")}init(e){this.score=e.score||0}create(){this.ticTaps=new nt,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===p.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===p.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,js(o,2,2,e,t,18),a.refresh();const g=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(g)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const l=new p.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(l,p.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const l="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(l)&&this.textures.remove(l);const o=Math.max(400,s.length*i*.7),d=i*1.5,g=this.textures.createCanvas(l,o,d);if(!g)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=g.getContext(),u=h.createLinearGradient(0,0,o,d*.5);return a?(u.addColorStop(0,"#4cffae"),u.addColorStop(.4,"#32c4ff"),u.addColorStop(1,"#5c67ff")):n?(u.addColorStop(0,"#32c4ff"),u.addColorStop(.5,"#7f54ff"),u.addColorStop(1,"#b63efc")):(u.addColorStop(0,"#33DDFF"),u.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=u,h.fillText(s,o/2,d/2),g.refresh(),this.add.image(e,t,l).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function js(r,e,t,s,i,a,n,l){r.beginPath(),r.moveTo(e+a,t),r.lineTo(e+s-a,t),r.quadraticCurveTo(e+s,t,e+s,t+a),r.lineTo(e+s,t+i-a),r.quadraticCurveTo(e+s,t+i,e+s-a,t+i),r.lineTo(e+a,t+i),r.quadraticCurveTo(e,t+i,e,t+i-a),r.lineTo(e,t+a),r.quadraticCurveTo(e,t,e+a,t),r.closePath(),r.stroke()}class Xs{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing MatchingMayhemGame game...");const e={type:p.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:this.config.containerId,scene:[Ds,Ns,Us,Hs],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:p.Scale.EXPAND,autoCenter:p.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new p.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting MatchingMayhemGame game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing MatchingMayhemGame game...")}resume(){console.log("Resuming MatchingMayhemGame game...")}destroy(){console.log("Destroying MatchingMayhemGame game..."),this.gameInstance&&(this.gameInstance=null)}getCurrentScore(){return 0}}class qs extends p.Scene{constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("countdown-1","/assets/images/countdown-1.png"),this.load.image("countdown-2","/assets/images/countdown-2.png"),this.load.image("countdown-3","/assets/images/countdown-3.png"),this.load.image("countdown-go","/assets/images/countdown-go.png"),this.load.svg("heart","/assets/images/mdi--heart.svg"),this.load.svg("heart_outline","/assets/images/mdi-light--heart.svg"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.image("game_name","/assets-numbers/images/game_name.svg"),this.load.image("button_bg","/assets/images/button_bg.svg"),this.load.image("game_start","/assets/images/game_start.png"),this.load.image("game_background","/assets/images/game_bg.png"),this.load.image("game_over","/assets/images/game_over.svg"),this.load.image("back_to_lobby","/assets/images/back_to_lobby.png"),this.load.image("timer_bg","/assets/images/timer_bg.svg"),this.load.image("timer_icon","/assets/images/timer_icon.png"),this.load.image("timer_countdown_bg","/assets/images/timer_countdown_bg.png"),this.load.image("circle","/assets-numbers/images/circle.png"),this.load.audio("countdown",["/assets/audio/countdown.ogg","/assets/audio/countdown.mp3"]),this.load.audio("click",["/assets/audio/click.ogg","/assets/audio/click.mp3"]),this.load.audio("go",["/assets/sounds/go.ogg","/assets/sounds/go.mp3"]),this.load.audio("collect",["/assets-numbers/sounds/collect.ogg","/assets-numbers/sounds/collect.mp3"]),this.load.audio("complete",["/assets-numbers/sounds/complete.ogg","/assets-numbers/sounds/complete.mp3"]),this.load.audio("error",["/assets-numbers/sounds/error.ogg","/assets-numbers/sounds/error.mp3"]),this.load.audio("timeout",["/assets-numbers/sounds/timeout.ogg","/assets-numbers/sounds/timeout.mp3"])}create(){}}class Ae{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}notifyGameReady(){this.sendMessage({type:"gameReady"})}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}}class Ys extends p.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new Ae,this.textures.exists("game_background")?this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(e,t):(this.cameras.main.setBackgroundColor("#000000"),console.warn("game_background asset missing, using fallback"));let s,i;this.textures.exists("game_name")?(s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8),s.setScale(i)):(s=this.add.text(e/2,t*.25,"NUMBERS GAME",{fontFamily:"NeorisTrialBold, Arial",fontSize:"48px",fontStyle:"bold",color:"#ffffff"}).setOrigin(.5),i=1,console.warn("game_name asset missing, using fallback text")),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"});let a,n;if(this.textures.exists("button_bg"))if(this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5),a=Math.min(e*.6/this.startButton.width,.4),this.startButton.setScale(a),this.textures.exists("game_start")){n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5);const l=this.startButton.displayWidth*.6/n.width;n.setScale(l)}else n=this.add.text(this.startButton.x,this.startButton.y,"START",{fontFamily:"NeorisTrialBold, Arial",fontSize:"32px",fontStyle:"bold",color:"#ffffff"}).setOrigin(.5),console.warn("game_start asset missing, using fallback text");else{const l=this.add.graphics();l.fillStyle(31436,1),l.lineStyle(2,52479,1),l.fillRoundedRect(-120,-40,240,80,20),l.strokeRoundedRect(-120,-40,240,80,20),this.startButton=this.add.container(e/2,t*.6,[l]),a=1,n=this.add.text(this.startButton.x,this.startButton.y,"START GAME",{fontFamily:"NeorisTrialBold, Arial",fontSize:"32px",fontStyle:"bold",color:"#ffffff"}).setOrigin(.5),console.warn("button_bg asset missing, using fallback button")}this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{try{this.sound.get("countdown")&&this.sound.play("countdown",{volume:ee.SOUND_VOLUME})}catch(l){console.warn("Sound playback failed:",l)}this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:ee.FLASH_DURATION,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:ee.FLASH_DURATION,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:ee.SOUND_VOLUME}),this.tweens.add({targets:s,alpha:0,delay:ee.TRANSITION_DELAY,duration:ee.TRANSITION_FADE_DURATION,ease:"Sine.easeIn",onComplete:()=>{this.ticTaps.notifyGameReady(),this.scene.start("GameScene")}})}})}}class Vs{scene;config;score;scoreText;scoreLabel;container;events;constructor(e,t={}){this.scene=e,this.events=new p.Events.EventEmitter,this.config={initialScore:t.initialScore??0,fontFamily:t.fontFamily??"Arial",fontSize:t.fontSize??"80px",labelFontSize:t.labelFontSize??"28px",scoreColor:t.scoreColor??"#33DDFF",labelColor:t.labelColor??"#FFFFFF",animationColor:t.animationColor??"#ffff00",animationDuration:t.animationDuration??800},this.score=this.config.initialScore}createUI(e,t,s){this.container=this.scene.add.container(0,0),this.scoreLabel=this.scene.add.text(e,t-30,"Total Point",{fontFamily:this.config.fontFamily,fontSize:this.config.labelFontSize,fontStyle:"bold",color:this.config.labelColor}).setOrigin(.5),this.scoreText=this.scene.add.text(e,t+30,this.score.toString(),{fontFamily:this.config.fontFamily,fontSize:this.config.fontSize,fontStyle:"bold",color:this.config.scoreColor}).setOrigin(.5),this.container.add([this.scoreLabel,this.scoreText]),s&&s.add(this.container)}addPoints(e,t){this.score+=e,this.updateScoreDisplay(),t&&this.createScoreAnimation(t),this.events.emit("scoreChanged",this.score,e)}setScore(e){const t=this.score;this.score=e,this.updateScoreDisplay(),this.events.emit("scoreChanged",this.score,this.score-t)}getScore(){return this.score}reset(){this.score=this.config.initialScore,this.updateScoreDisplay(),this.events.emit("scoreReset",this.score)}updateScoreDisplay(){this.scoreText&&this.scoreText.setText(this.score.toString())}createScoreAnimation(e){const t=this.scene.add.text(e.startX,e.startY,`+${e.points}`,{fontFamily:this.config.fontFamily,fontSize:"24px",color:e.color??this.config.animationColor,stroke:"#000000",strokeThickness:3});t.setOrigin(.5),this.scene.tweens.add({targets:t,y:e.startY-50,alpha:0,scale:1.2,duration:e.duration??this.config.animationDuration,ease:"Power2",onComplete:()=>{t.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.scoreText=void 0,this.scoreLabel=void 0,this.container=void 0}}class Ks{scene;config;lives;hearts=[];container;events;constructor(e,t={}){this.scene=e,this.events=new p.Events.EventEmitter,this.config={initialLives:t.initialLives??3},this.lives=this.config.initialLives}createUI(e,t,s){this.container=this.scene.add.container(e,t);const a=-((this.lives-1)*40)/2;for(let n=0;n<this.lives;n++){let l=this.scene.add.image(a+n*40,0,"heart").setOrigin(.5).setScale(1.5);this.hearts.push(l)}this.container.add(this.hearts),s&&s.add(this.container)}deductHeart(e,t){this.lives--,this.hearts[this.lives]&&this.hearts[this.lives].setTexture("heart_outline"),e!==void 0&&t!==void 0&&this.container&&this.createFlyingHeartAnimation(e,t),this.events.emit("heartDeducted",this.lives)}createFlyingHeartAnimation(e,t){if(!this.container)return;const s=this.scene.add.image(e,t,"heart_broken").setOrigin(.5).setScale(1.5).setAlpha(.4);this.scene.tweens.add({targets:s,y:t-200,scale:3,alpha:.8,duration:600,ease:"Power2",onComplete:()=>{s.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.container=void 0}}class Qs extends p.Scene{ticTaps;gamePanel;countdownPanel;socketClient;numberObjects=[];currentIndex=0;isLocked=!1;roundText;currentSequence=[];scoreManager;livesManager;line;confettiEmitter;lastTappedValue=-1;constructor(){super("GameScene")}playSound(e){try{this.sound.play(e)}catch(t){console.warn("Sound playback failed:",t)}}init(e){this.socketClient=e.socketClient,this.currentIndex=0,this.isLocked=!1,this.scoreManager=new Vs(this,{initialScore:0,fontSize:"80px",scoreColor:"#33DDFF"}),this.livesManager=new Ks(this),this.socketClient.addCustomEventListener("initialized",t=>{console.log("Number Sequence game initialized by server:",t),t.currentSequence&&(this.currentSequence=t.currentSequence,this.generateNumbers(t.currentSequence)),this.startCountdown()}),this.socketClient.addCustomEventListener("started",t=>{console.log("Number Sequence game started by server:",t),this.countdownPanel.visible=!1,this.gamePanel.visible=!0}),this.socketClient.addCustomEventListener("action_result",t=>{if(!t||t.actionType!=="number_select")return;const s=t.data;if(s.isCorrect){const i=this.lastTappedValue,a=this.numberObjects.find(n=>n.numberValue===i);a&&(a.disableInteractive(),a.setActive(!0),this.updateLine(a.x,a.y),this.playSound("collect"),a.playDisappearAnimation(()=>{})),this.currentIndex=s.currentIndex,w.updateScore(s.newScore),this.updateGradientRoundText(`${this.currentIndex} to ${this.currentSequence.length}`),s.nextSequence&&(this.currentSequence=s.nextSequence,this.generateNumbers(s.nextSequence))}else{const i=this.lastTappedValue,a=this.numberObjects.find(n=>n.numberValue===i)||this.numberObjects.find(n=>n.numberValue===this.currentIndex);if(a?(a.setError(),this.playSound("error"),w.updateLives(s.newLives),this.livesManager.deductHeart(a.x,a.y)):this.livesManager.deductHeart(),this.currentIndex=s.currentIndex,s.gameEnded){this.isLocked=!0;return}}}),this.socketClient.addCustomEventListener("game_error",t=>{console.error("Game error from server:",t)}),this.socketClient.addCustomEventListener("game_fatal_error",t=>{console.error("Fatal game error from server:",t),this.handleFatalError(t.message,t.errorType)}),this.socketClient.addCustomEventListener("ended",t=>{this.handleServerEnded(t)}),this.livesManager.on("heartDeducted",t=>{t===0&&this.timeOut()})}create(){this.ticTaps=new Ae,this.add.image(this.cameras.main.width/2,this.cameras.main.height/2,"game_background").setOrigin(.5).setDisplaySize(this.cameras.main.width,this.cameras.main.height),this.createGamePanel(),this.line=this.add.graphics(),this.createConfettiSystem(),this.time.delayedCall(100,()=>{this.initializeGame()})}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for Number Sequence GameScene"))}shutdown(){this.cleanupSocketEventListeners(),this.scoreManager&&this.scoreManager.destroy(),this.livesManager&&this.livesManager.destroy(),this.confettiEmitter&&this.confettiEmitter.stop(),this.numberObjects.forEach(e=>e.destroy()),this.numberObjects=[]}createGamePanel(){this.gamePanel=this.add.container(0,0),this.gamePanel.visible=!1;const{width:e,height:t}=this.cameras.main;this.scoreManager.createUI(e/2,t*.09,this.gamePanel),this.createGradientRoundText(),this.gamePanel.add([])}createGradientRoundText(){this.updateGradientRoundText("0 to 6")}updateGradientRoundText(e){const{height:t}=this.cameras.main,s="roundText-"+e.replace(/\s+/g,"-");this.textures.exists(s)&&this.textures.remove(s);const i=24,a=Math.max(120,e.length*i*.6),n=i*1.5,l=this.textures.createCanvas(s,a,n);if(!l){console.error("Failed to create gradient round text canvas"),this.roundText&&this.roundText.destroy(),this.roundText=this.add.text(50,t-20,e,{fontSize:"24px",fontFamily:"Arial",color:"#999999"}).setOrigin(0,1),this.gamePanel.add(this.roundText);return}const o=l.getContext(),d=o.createLinearGradient(0,0,a,n*.5);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.font=`bold ${i}px Arial`,o.textAlign="left",o.textBaseline="middle",o.fillStyle=d,o.fillText(e,0,n/2),l.refresh(),this.roundText&&this.roundText.destroy(),this.roundText=this.add.image(50,t-20,s).setOrigin(0,1),this.gamePanel.add(this.roundText)}initializeGame(){console.log("Initializing Number Sequence game..."),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()?this.socketClient.startGame():console.error("No server connection available - cannot start game")}generateNumbers(e){this.numberObjects.forEach(s=>s.destroy()),this.numberObjects=[],this.currentIndex=0,this.line.clear(),this.updateGradientRoundText(`0 to ${e.length}`);const t=this.generatePositions(e.length);e.forEach((s,i)=>{this.time.delayedCall(i*100,()=>{const a=new Dt(this,t[i].x,t[i].y,s);this.numberObjects.push(a)})})}generatePositions(e){const{width:t,height:s}=this.cameras.main,i=ee.CIRCLE_RADIUS,a=i*2+20,n=s*.3,l=s*.95,o=t*.06,d=t*.94,g=[],h=(k,T)=>{if(k-i<o||k+i>d||T-i<n||T+i>l)return!1;for(const _ of g)if(Math.sqrt(Math.pow(k-_.x,2)+Math.pow(T-_.y,2))<a)return!1;return!0};let u=0;const v=1e3;for(;g.length<e&&u<v;){const k=o+i+Math.random()*(d-o-i*2),T=n+i+Math.random()*(l-n-i*2);h(k,T)&&g.push({x:k,y:T}),u++}return g.length<e?(console.warn("Using grid fallback for remaining positions"),this.generateGridBasedPositions(e)):g}generateGridBasedPositions(e){const{width:t,height:s}=this.cameras.main,i=ee.CIRCLE_RADIUS*2,a=Math.min(5,Math.floor(t/i)),n=Math.ceil(e/a),l=a*i,o=n*i,d=(t-l)/2+i/2,g=s*.75,v=s*.2+(g-o)/2,k=[];for(let _=0;_<e;_++){const S=Math.floor(_/a),L=_%a;k.push({x:d+L*i,y:v+S*i})}return[...k].sort(()=>Math.random()-.5)}timeOut(){this.isLocked=!0,this.playSound("timeout"),this.confettiEmitter.start(),w.endGame()}handleServerEnded(e){this.isLocked=!0;try{this.playSound("complete")}catch{}w.endGame()}handleNumberClick(e){this.isLocked||(this.lastTappedValue=e.numberValue,this.socketClient&&this.socketClient.isConnected()?this.socketClient.sendNumberSelect(e.numberValue):console.error("Socket client not connected - game requires server connection"))}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.isLocked=!0,this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}updateLine(e,t){this.currentIndex===1&&(this.line.lineStyle(4,52479),this.line.moveTo(this.numberObjects[0].x,this.numberObjects[0].y)),this.line.lineTo(e,t)}createConfettiSystem(){const{width:e}=this.cameras.main,t=this.textures.createCanvas("particleTexture",10,10);if(!t){console.warn("Failed to create particle texture, using fallback"),this.confettiEmitter=this.add.particles(e/2,-50,"",{speed:{min:150,max:350},scale:{start:.8,end:0},lifespan:{min:1500,max:2500},gravityY:400,rotate:{min:-180,max:180},frequency:50,quantity:3,tint:[16711680,65280,255,16776960,16711935,65535],blendMode:"ADD",emitting:!1});return}const s=t.getContext();s.fillStyle="#ffffff",s.fillRect(0,0,10,10),t.refresh(),this.confettiEmitter=this.add.particles(e/2,-50,"particleTexture",{speed:{min:150,max:350},scale:{start:.8,end:0},lifespan:{min:1500,max:2500},gravityY:400,scaleX:{onUpdate:(i,a,n)=>Math.sin(n*Math.PI*8)},rotate:{min:-180,max:180},frequency:50,quantity:3,tint:[16711680,65280,255,16776960,16711935,65535],blendMode:"ADD",emitting:!1})}}class Zs extends p.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene")}init(e){this.score=e.score||0}create(){this.ticTaps=new Ae,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===p.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===p.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,Js(o,2,2,e,t,18),a.refresh();const g=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(g)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const l=new p.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(l,p.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const l="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(l)&&this.textures.remove(l);const o=Math.max(400,s.length*i*.7),d=i*1.5,g=this.textures.createCanvas(l,o,d);if(!g)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=g.getContext(),u=h.createLinearGradient(0,0,o,d*.5);return a?(u.addColorStop(0,"#4cffae"),u.addColorStop(.4,"#32c4ff"),u.addColorStop(1,"#5c67ff")):n?(u.addColorStop(0,"#32c4ff"),u.addColorStop(.5,"#7f54ff"),u.addColorStop(1,"#b63efc")):(u.addColorStop(0,"#33DDFF"),u.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=u,h.fillText(s,o/2,d/2),g.refresh(),this.add.image(e,t,l).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}}function Js(r,e,t,s,i,a,n,l){r.beginPath(),r.moveTo(e+a,t),r.lineTo(e+s-a,t),r.quadraticCurveTo(e+s,t,e+s,t+a),r.lineTo(e+s,t+i-a),r.quadraticCurveTo(e+s,t+i,e+s-a,t+i),r.lineTo(e+a,t+i),r.quadraticCurveTo(e,t+i,e,t+i-a),r.lineTo(e,t+a),r.quadraticCurveTo(e,t,e+a,t),r.closePath(),r.stroke()}class $s{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing NumberSequence game...");const e={type:p.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:this.config.containerId,scene:[qs,Ys,Qs,Zs],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:p.Scale.EXPAND,autoCenter:p.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new p.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting NumberSequence game..."),this.gameInstance?.scene.start("GameScene",{socketClient:this.config.socketClient})}pause(){console.log("Pausing NumberSequence game...")}resume(){console.log("Resuming NumberSequence game...")}destroy(){console.log("Destroying NumberSequence game..."),this.gameInstance&&(this.gameInstance=null)}getCurrentScore(){return 0}}const Ee=[];Re.handleByNamedList(de.Environment,Ee);async function ei(r){if(!r)for(let e=0;e<Ee.length;e++){const t=Ee[e];if(t.value.test()){await t.value.load();return}}}let ce;function ti(){if(typeof ce=="boolean")return ce;try{ce=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{ce=!1}return ce}var ot=(r=>(r[r.NONE=0]="NONE",r[r.COLOR=16384]="COLOR",r[r.STENCIL=1024]="STENCIL",r[r.DEPTH=256]="DEPTH",r[r.COLOR_DEPTH=16640]="COLOR_DEPTH",r[r.COLOR_STENCIL=17408]="COLOR_STENCIL",r[r.DEPTH_STENCIL=1280]="DEPTH_STENCIL",r[r.ALL=17664]="ALL",r))(ot||{});class si{constructor(e){this.items=[],this._name=e}emit(e,t,s,i,a,n,l,o){const{name:d,items:g}=this;for(let h=0,u=g.length;h<u;h++)g[h][d](e,t,s,i,a,n,l,o);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const ii=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],rt=class lt extends Nt{constructor(e){super(),this.uid=Wt("renderer"),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...ii,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await ei(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const s in this._systemsHash)e={...this._systemsHash[s].constructor.defaultOptions,...e};e={...lt.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let s=0;s<this.runners.init.items.length;s++)await this.runners.init.items[s].init(e);this._initOptions=e}render(e,t){let s=e;if(s instanceof re&&(s={container:s},t&&(ye(Se,"passing a second argument is deprecated, please use render options instead"),s.target=t.renderTexture)),s.target||(s.target=this.view.renderTarget),s.target===this.view.renderTarget&&(this._lastObjectRendered=s.container,s.clearColor??(s.clearColor=this.background.colorRgba),s.clear??(s.clear=this.background.clearBeforeRender)),s.clearColor){const i=Array.isArray(s.clearColor)&&s.clearColor.length===4;s.clearColor=i?s.clearColor:_e.shared.setValue(s.clearColor).toArray()}s.transform||(s.container.updateLocalTransform(),s.transform=s.container.localTransform),s.container.visible&&(s.container.enableRenderGroup(),this.runners.prerender.emit(s),this.runners.renderStart.emit(s),this.runners.render.emit(s),this.runners.renderEnd.emit(s),this.runners.postrender.emit(s))}resize(e,t,s){const i=this.view.resolution;this.view.resize(e,t,s),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),s!==void 0&&s!==i&&this.runners.resolutionChange.emit(s)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=ot.ALL);const{clear:s,clearColor:i,target:a}=e;_e.shared.setValue(i??this.background.colorRgba),t.renderTarget.clear(a,s,_e.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new si(t)})}_addSystems(e){let t;for(t in e){const s=e[t];this._addSystem(s.value,s.name)}}_addSystem(e,t){const s=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=s,this._systemsHash[t]=s;for(const i in this.runners)this.runners[i].add(s);return this}_addPipes(e,t){const s=t.reduce((i,a)=>(i[a.name]=a.value,i),{});e.forEach(i=>{const a=i.value,n=i.name,l=s[n];this.renderPipes[n]=new a(this,l?new l:null),this.runners.destroy.add(this.renderPipes[n])})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),(e===!0||typeof e=="object"&&e.releaseGlobalResources)&&Ut.release(),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!ti())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};rt.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let ct=rt,ge;function ai(r){return ge!==void 0||(ge=(()=>{const e={stencil:!0,failIfMajorPerformanceCaveat:r??ct.defaultOptions.failIfMajorPerformanceCaveat};try{if(!Le.get().getWebGLRenderingContext())return!1;let s=Le.get().createCanvas().getContext("webgl",e);const i=!!s?.getContextAttributes()?.stencil;if(s){const a=s.getExtension("WEBGL_lose_context");a&&a.loseContext()}return s=null,i}catch{return!1}})()),ge}let fe;async function ni(r={}){return fe!==void 0||(fe=await(async()=>{const e=Le.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(r)).requestDevice(),!0}catch{return!1}})()),fe}const Qe=["webgl","webgpu","canvas"];async function oi(r){let e=[];r.preference?(e.push(r.preference),Qe.forEach(a=>{a!==r.preference&&e.push(a)})):e=Qe.slice();let t,s={};for(let a=0;a<e.length;a++){const n=e[a];if(n==="webgpu"&&await ni()){const{WebGPURenderer:l}=await He(async()=>{const{WebGPURenderer:o}=await import("./B3qBAdTc.js");return{WebGPURenderer:o}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]),import.meta.url);t=l,s={...r,...r.webgpu};break}else if(n==="webgl"&&ai(r.failIfMajorPerformanceCaveat??ct.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:l}=await He(async()=>{const{WebGLRenderer:o}=await import("./BpKJYiXk.js");return{WebGLRenderer:o}},__vite__mapDeps([11,1,2,3,4,5,6,7,8,9,10]),import.meta.url);t=l,s={...r,...r.webgl};break}else if(n==="canvas")throw s={...r},new Error("CanvasRenderer is not yet implemented")}if(delete s.webgpu,delete s.webgl,!t)throw new Error("No available renderer for the current environment");const i=new t;return await i.init(s),i}const dt="8.13.2";class ht{static init(){globalThis.__PIXI_APP_INIT__?.(this,dt)}static destroy(){}}ht.extension=de.Application;class ri{constructor(e){this._renderer=e}init(){globalThis.__PIXI_RENDERER_INIT__?.(this._renderer,dt)}destroy(){this._renderer=null}}ri.extension={type:[de.WebGLSystem,de.WebGPUSystem],name:"initHook",priority:-10};const mt=class Ie{constructor(...e){this.stage=new re,e[0]!==void 0&&ye(Se,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await oi(e),Ie._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return ye(Se,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const s=Ie._plugins.slice(0);s.reverse(),s.forEach(i=>{i.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};mt._plugins=[];let ut=mt;Re.handleByList(de.Application,ut._plugins);Re.add(ht);class li extends Ht{constructor(e,t){const{text:s,resolution:i,style:a,anchor:n,width:l,height:o,roundPixels:d,...g}=e;super({...g}),this.batched=!0,this._resolution=null,this._autoResolution=!0,this._didTextUpdate=!0,this._styleClass=t,this.text=s??"",this.style=a,this.resolution=i??null,this.allowChildren=!1,this._anchor=new jt({_onUpdate:()=>{this.onViewUpdate()}}),n&&(this.anchor=n),this.roundPixels=d??!1,l!==void 0&&(this.width=l),o!==void 0&&(this.height=o)}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}set text(e){e=e.toString(),this._text!==e&&(this._text=e,this.onViewUpdate())}get text(){return this._text}set resolution(e){this._autoResolution=e===null,this._resolution=e,this.onViewUpdate()}get resolution(){return this._resolution}get style(){return this._style}set style(e){e||(e={}),this._style?.off("update",this.onViewUpdate,this),e instanceof this._styleClass?this._style=e:this._style=new this._styleClass(e),this._style.on("update",this.onViewUpdate,this),this.onViewUpdate()}get width(){return Math.abs(this.scale.x)*this.bounds.width}set width(e){this._setWidth(e,this.bounds.width)}get height(){return Math.abs(this.scale.y)*this.bounds.height}set height(e){this._setHeight(e,this.bounds.height)}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this.bounds.width,e.height=Math.abs(this.scale.y)*this.bounds.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this.bounds.width),t!==void 0&&this._setHeight(t,this.bounds.height)}containsPoint(e){const t=this.bounds.width,s=this.bounds.height,i=-t*this.anchor.x;let a=0;return e.x>=i&&e.x<=i+t&&(a=-s*this.anchor.y,e.y>=a&&e.y<=a+s)}onViewUpdate(){this.didViewUpdate||(this._didTextUpdate=!0),super.onViewUpdate()}destroy(e=!1){super.destroy(e),this.owner=null,this._bounds=null,this._anchor=null,(typeof e=="boolean"?e:e?.style)&&this._style.destroy(e),this._style=null,this._text=null}get styleKey(){return`${this._text}:${this._style.styleKey}:${this._resolution}`}}function ci(r,e){let t=r[0]??{};return(typeof t=="string"||r[1])&&(ye(Se,`use new ${e}({ text: "hi!", style }) instead`),t={text:t,style:r[1]}),t}class di extends li{constructor(...e){const t=ci(e,"Text");super(t,Kt),this.renderPipeId="text",t.textureStyle&&(this.textureStyle=t.textureStyle instanceof Ye?t.textureStyle:new Ye(t.textureStyle))}updateBounds(){const e=this._bounds,t=this._anchor;let s=0,i=0;if(this._style.trim){const{frame:a,canvasAndContext:n}=Ke.getCanvasAndContext({text:this.text,style:this._style,resolution:1});Ke.returnCanvasAndContext(n),s=a.width,i=a.height}else{const a=Qt.measureText(this._text,this._style);s=a.width,i=a.height}e.minX=-t._x*s,e.maxX=e.minX+s,e.minY=-t._y*i,e.maxY=e.minY+i}}var hi=z('<div class="level-complete-overlay svelte-1f4jq90"><div class="level-complete-panel svelte-1f4jq90"><div class="level-complete-blur-bg svelte-1f4jq90"></div> <div class="level-complete-content svelte-1f4jq90"><h2 class="level-complete-title svelte-1f4jq90"> </h2> <p class="level-complete-subtitle svelte-1f4jq90"> </p></div></div></div>'),mi=z('<div class="mums-numbers-game svelte-1f4jq90"><div class="game-board-container svelte-1f4jq90"><div class="game-canvas svelte-1f4jq90"></div> <p class="board-time svelte-1f4jq90">Board: <span class="board-timer svelte-1f4jq90"> </span></p></div></div> <!>',1);function ui(r,e){ae(e,!0);let t=E(void 0),s=E(void 0),i=E(null),a=E(0),n=E(1),l=E(0),o=E(3),d=E(1),g=E(0),h=E("00:00"),u=E(Je([])),v=E(!1),k=E(null),T=E(!1);class _{app;container;gridSize=5;cellSize=90;canvasSize=this.gridSize*this.cellSize;gridContainer;pathContainer;numbersContainer;highlightContainer;grid=[];solutionPath=[];numberPositions={};playerPath=[];currentPath=[];previewPath=[];isDrawing=!1;isDragging=!1;lastCell=null;currentNumber=1;gameStarted=!1;gameEnded=!1;moveCount=0;timerInterval=null;boardStartTime=null;boardTimerInterval=null;constructor(c){this.container=c,this.app=new ut,this.gridContainer=new re,this.pathContainer=new re,this.numbersContainer=new re,this.highlightContainer=new re,this.app.stage.addChild(this.gridContainer),this.app.stage.addChild(this.pathContainer),this.app.stage.addChild(this.highlightContainer),this.app.stage.addChild(this.numbersContainer),this.initPixi()}async initPixi(){await this.app.init({width:this.canvasSize,height:this.canvasSize,backgroundAlpha:0,antialias:!0}),this.container.appendChild(this.app.canvas),setTimeout(()=>{this.init()},100)}init(){this.initializeGrid(),this.initializeEventListeners(),this.draw(),this.resizeCanvas(),this.setupSocketListeners()}setupSocketListeners(){e.socketClient&&(e.socketClient.addCustomEventListener("initialized",c=>{console.log("Game initialized:",c),c.puzzleState&&(this.loadPuzzleFromServer(c.puzzleState),C(k,c.puzzleState,!0)),c.gameState&&(C(l,c.gameState.score,!0),C(o,c.gameState.lives,!0),w.updateScore(m(l)),w.updateLives(m(o)))}),e.socketClient.addCustomEventListener("started",c=>{console.log("Game started:",c),this.gameStarted=!0,this.boardStartTime=Date.now(),this.startBoardTimer(),C(u,[],!0)}),e.socketClient.addCustomEventListener("action_result",c=>{console.log("Action result:",c),c.actionType==="path_move"&&this.handleServerMoveResult(c.data)}),e.socketClient.addCustomEventListener("ended",c=>{console.log("Game ended:",c),this.handleGameEnd(c)}),e.socketClient.addCustomEventListener("error",c=>{console.error("Game error:",c),C(T,!1)}))}loadPuzzleFromServer(c){this.grid=c.grid,this.numberPositions=c.numberPositions,this.playerPath=c.playerPath||[],this.currentNumber=c.currentNumber||1,this.previewPath=[...this.playerPath],C(n,this.currentNumber,!0),C(a,this.playerPath.length,!0),this.draw()}sendPathToServer(c){if(!e.socketClient||!e.socketClient.isConnected()){console.error("No socket connection available");return}c.length!==0&&(C(T,!0),e.socketClient.sendGameAction("path_move",{path:c,timestamp:Date.now()}))}sendMoveToServer(c,f){if(!e.socketClient||!e.socketClient.isConnected()){console.error("No socket connection available");return}C(T,!0),e.socketClient.sendPathMove(c,f)}handleServerMoveResult(c){if(C(T,!1),c.isValid)this.playerPath=c.newPath||c.path||[],this.currentNumber=c.currentNumber||1,this.previewPath=[...this.playerPath],C(n,this.currentNumber,!0),C(a,this.playerPath.length,!0),C(l,c.newScore||c.score||m(l),!0),C(o,c.newLives||c.lives||m(o),!0),C(d,c.currentLevel||m(d),!0),C(g,c.levelsCompleted||m(g),!0),w.updateScore(m(l)),w.updateLives(m(o)),c.levelCompleted?c.newPuzzle?this.handleLevelComplete(c):this.handleGameEnd({reason:"time_up",finalScore:m(l),gameWon:!1,levelsCompleted:m(g)}):c.gameEnded&&this.handleGameEnd({reason:c.gameWon?"completed":"no_lives",finalScore:m(l),gameWon:c.gameWon,levelsCompleted:m(g)});else{const f=c.invalidReason||"Invalid path";console.warn("Invalid path:",f),this.showPathError(f),this.isDrawing=!1,this.isDragging=!1,this.previewPath=[...this.playerPath],c.newLives!==void 0&&(C(o,c.newLives,!0),w.updateLives(m(o))),c.gameEnded&&this.handleGameEnd({reason:"no_lives",finalScore:m(l),levelsCompleted:m(g)})}this.draw()}showPathError(c){const f=document.createElement("div");f.className="path-error-message",f.textContent=c,document.body.appendChild(f),setTimeout(()=>f.remove(),1200)}handleLevelComplete(c){C(v,!0),typeof c.boardTimeMs=="number"&&C(u,[...m(u),c.boardTimeMs],!0),c.newPuzzle&&this.loadPuzzleFromServer(c.newPuzzle),this.boardStartTime=Date.now(),C(h,"00:00"),this.startBoardTimer(),setTimeout(()=>{C(v,!1)},1200),console.log(`Level ${m(d)-1} completed! Starting level ${m(d)}`)}handleGameEnd(c){this.gameEnded=!0,this.stopBoardTimer(),C(l,c.finalScore||m(l),!0),w.updateScore(m(l)),w.endGame(),e.onScoreUpdate&&e.onScoreUpdate(m(l)),e.onGameComplete&&e.onGameComplete(m(l))}initializeGrid(){this.grid=Array(this.gridSize).fill(null).map(()=>Array(this.gridSize).fill(null).map(()=>({visited:!1,number:null,hasPath:!1})))}isValidPosition(c,f){return c>=0&&c<this.gridSize&&f>=0&&f<this.gridSize}resetPlayerState(){this.playerPath=[],this.currentPath=[],this.previewPath=[],this.currentNumber=1,this.lastCell=null,this.gameEnded=!1,this.moveCount=0,this.isDrawing=!1,this.isDragging=!1,C(a,this.moveCount,!0),C(n,this.currentNumber,!0);for(let c=0;c<this.gridSize;c++)for(let f=0;f<this.gridSize;f++)this.grid[c][f]={...this.grid[c][f],visited:!1,hasPath:!1}}initializeEventListeners(){const c=this.app.canvas;c.addEventListener("mousedown",f=>this.handleStart(f)),c.addEventListener("mousemove",f=>this.handleMove(f)),c.addEventListener("mouseup",()=>this.handleEnd()),c.addEventListener("mouseleave",()=>this.handleEnd()),c.addEventListener("touchstart",this.handleTouchStart.bind(this)),c.addEventListener("touchmove",this.handleTouchMove.bind(this)),c.addEventListener("touchend",f=>{f.preventDefault(),this.handleEnd()})}handleTouchStart(c){c.preventDefault(),this.handleStart(c.touches[0])}handleTouchMove(c){c.preventDefault(),this.handleMove(c.touches[0])}handleStart(c){if(this.gameEnded||m(T))return;const f=this.getCellFromEvent(c);f&&(this.gameStarted||this.startGame(),this.isDrawing=!0,this.isDragging=!0,this.previewPath=[...this.playerPath],this.addCellToPreviewPath(f),this.draw())}handleMove(c){if(!this.isDrawing||!this.isDragging||this.gameEnded)return;const f=this.getCellFromEvent(c);f&&(this.addCellToPreviewPath(f),this.draw())}handleEnd(){this.gameEnded||(this.isDrawing=!1,this.isDragging=!1,this.previewPath.length>this.playerPath.length?this.sendPathToServer(this.previewPath):(this.previewPath=[...this.playerPath],this.draw()))}addCellToPreviewPath(c){if(this.previewPath.length>0){const f=this.previewPath[this.previewPath.length-1];if(c.row===f.row&&c.col===f.col)return;const G=this.previewPath.findIndex(U=>U.row===c.row&&U.col===c.col);if(G!==-1){this.previewPath=this.previewPath.slice(0,G+1);return}if(!(Math.abs(c.row-f.row)+Math.abs(c.col-f.col)===1))return}this.previewPath.push(c)}getCellFromEvent(c){const f=this.app.canvas.getBoundingClientRect(),G=this.app.canvas.width/f.width,O=this.app.canvas.height/f.height,U=(c.clientX-f.left)*G,J=(c.clientY-f.top)*O,V=Math.floor(U/this.cellSize),he=Math.floor(J/this.cellSize);return this.isValidPosition(he,V)?{row:he,col:V}:null}draw(){this.clearContainers(),this.drawClassicGrid(),this.drawPath(),this.drawNumbers()}clearContainers(){this.gridContainer.removeChildren(),this.pathContainer.removeChildren(),this.numbersContainer.removeChildren(),this.highlightContainer.removeChildren()}drawClassicGrid(){const c=new oe;for(let f=0;f<=this.gridSize;f++){const G=f*this.cellSize;c.moveTo(G,0).lineTo(G,this.canvasSize),c.moveTo(0,G).lineTo(this.canvasSize,G)}c.stroke({color:13421772,width:1}),this.gridContainer.addChild(c)}drawPath(){if(this.playerPath.length>=2){const c=new oe,f=this.playerPath[0];c.moveTo(f.col*this.cellSize+this.cellSize/2,f.row*this.cellSize+this.cellSize/2);for(let G=1;G<this.playerPath.length;G++){const O=this.playerPath[G];c.lineTo(O.col*this.cellSize+this.cellSize/2,O.row*this.cellSize+this.cellSize/2)}c.stroke({color:2600544,width:8,cap:"round",join:"round"}),this.pathContainer.addChild(c)}if(this.previewPath.length>=2&&this.isDrawing){const c=new oe,f=this.previewPath[0];c.moveTo(f.col*this.cellSize+this.cellSize/2,f.row*this.cellSize+this.cellSize/2);for(let G=1;G<this.previewPath.length;G++){const O=this.previewPath[G];c.lineTo(O.col*this.cellSize+this.cellSize/2,O.row*this.cellSize+this.cellSize/2)}c.stroke({color:3447003,width:6,cap:"round",join:"round",alpha:.7}),this.pathContainer.addChild(c)}if(this.previewPath.length===1&&this.isDrawing){const c=this.previewPath[0],f=new oe;f.circle(c.col*this.cellSize+this.cellSize/2,c.row*this.cellSize+this.cellSize/2,this.cellSize/4).fill({color:3447003,alpha:.5}),this.pathContainer.addChild(f)}}drawNumbers(){for(let c=1;c<=5;c++){const f=this.numberPositions[c];if(f){const G=f.col*this.cellSize+this.cellSize/2,O=f.row*this.cellSize+this.cellSize/2,U=new oe,J=c<this.currentNumber?2600544:15158332;U.circle(G,O,25).fill(J),this.numbersContainer.addChild(U);const V=new di({text:c.toString(),style:{fontFamily:"Arial",fontSize:32,fontWeight:"bold",fill:16777215}});V.anchor.set(.5),V.x=G,V.y=O,this.numbersContainer.addChild(V)}}}highlightCell(c,f,G,O){const U=new oe;U.rect(f*this.cellSize+2,c*this.cellSize+2,this.cellSize-4,this.cellSize-4).fill({color:G,alpha:O}),this.highlightContainer.addChild(U)}startGame(){e.socketClient&&e.socketClient.isConnected()&&e.socketClient.startGame(),console.log("Start game request sent to server")}startBoardTimer(){this.boardTimerInterval&&clearInterval(this.boardTimerInterval),this.boardTimerInterval=setInterval(()=>{if(!this.gameEnded&&this.boardStartTime){const c=Math.floor((Date.now()-this.boardStartTime)/1e3),f=Math.floor(c/60),G=c%60;C(h,`${f.toString().padStart(2,"0")}:${G.toString().padStart(2,"0")}`)}},1e3)}stopBoardTimer(){this.boardTimerInterval&&clearInterval(this.boardTimerInterval)}resetPath(){C(l,0),w.updateScore(m(l)),this.currentPath=[],this.currentNumber=1,this.draw()}restart(){this.gameStarted=!1,this.timerInterval=null,m(o)<=0&&(C(o,3),C(l,0),w.updateLives(m(o)),w.updateScore(m(l))),e.socketClient&&e.socketClient.isConnected()&&e.socketClient.initGame()}resizeCanvas(){if(!this.app.canvas||!m(t))return;const c=Math.min(m(t).clientWidth-40,500);c<500?(this.app.canvas.style.width=c+"px",this.app.canvas.style.height=c+"px"):(this.app.canvas.style.width="500px",this.app.canvas.style.height="500px")}destroy(){if(this.stopBoardTimer(),this.app.canvas){const c=this.app.canvas;c.removeEventListener("mousedown",this.handleStart),c.removeEventListener("mousemove",this.handleMove),c.removeEventListener("mouseup",this.handleEnd),c.removeEventListener("mouseleave",this.handleEnd),c.removeEventListener("touchstart",this.handleTouchStart),c.removeEventListener("touchmove",this.handleTouchMove),c.removeEventListener("touchend",this.handleEnd)}this.app.destroy()}}Be(()=>{if(m(s)){C(o,3),C(l,0),w.updateLives(m(o)),w.updateScore(m(l)),C(i,new _(m(s)),!0);const D=()=>{m(i)&&m(i).resizeCanvas()};return window.addEventListener("resize",D),()=>{window.removeEventListener("resize",D)}}}),$e(()=>{m(i)&&m(i).destroy()});function S(){m(i)&&!m(i).gameStarted&&m(i).startGame()}var L=mi(),B=Q(L),F=y(B),j=y(F);be(j,D=>C(s,D),()=>m(s));var X=x(j,2),P=x(y(X)),A=y(P,!0);b(P),b(X),b(F),b(B),be(B,D=>C(t,D),()=>m(t));var R=x(B,2);{var Z=D=>{var c=hi(),f=y(c),G=x(y(f),2),O=y(G),U=y(O);b(O);var J=x(O,2),V=y(J);b(J),b(G),b(f),b(c),W(()=>{Y(U,`Level ${m(d)-1} Complete!`),Y(V,`Starting Level ${m(d)??""}...`)}),I(D,c)};H(R,D=>{m(v)&&D(Z)})}return W(()=>Y(A,m(h))),I(r,L),ne({startGame:S})}class gi{gameId;containerId;socketClient;container=null;svelteComponent=null;config;constructor(e){this.gameId=e.gameId,this.containerId=e.containerId,this.socketClient=e.socketClient,this.config=e}async init(){if(w.updateLoadingProgress(1),w.preloadComplete(),console.log(`Initializing Mums Numbers game in container: ${this.containerId}`),this.container=document.getElementById(this.containerId),!this.container)throw new Error(`Container element with ID "${this.containerId}" not found`);this.container.innerHTML="",this.svelteComponent=Lt(ui,{target:this.container,props:{socketClient:this.socketClient,gameId:this.gameId,containerId:this.containerId,onScoreUpdate:this.config.onScoreUpdate,onGameComplete:this.config.onGameComplete}}),console.log("Mums Numbers game initialized successfully")}start(){console.log("Mums Numbers game started"),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game"),setTimeout(()=>{w.startGame(),this.svelteComponent.startGame(),console.log("Game started")},5200)}pause(){console.log("Mums Numbers game paused")}resume(){console.log("Mums Numbers game resumed")}destroy(){console.log("Destroying Mums Numbers game"),this.svelteComponent&&(Et(this.svelteComponent),this.svelteComponent=null),this.container&&(this.container.innerHTML="")}}function fi(r,e,t,s){const i={gameId:r,containerId:e,socketClient:t,...s};switch(r){case"finger-frenzy":return new Es(i);case"bingo":return new Fs(i);case"matching-mayhem":return new Xs(i);case"numbers":return new $s(i);case"mums-numbers":return new gi(i);default:throw new Error(`Unknown game ID: ${r}`)}}const vi=!1,Hi=Object.freeze(Object.defineProperty({__proto__:null,ssr:vi},Symbol.toStringTag,{value:"Module"}));var pi=z("<!> <!>",1),bi=z('<!> <!> <div class="w-screen h-screen overflow-hidden relative"><!> <div class="w-full h-full box-border" id="game-container"></div> <!> <!> <!> <!></div>',1);function ji(r,e){ae(e,!0);const[t,s]=et(),i=()=>xe(Ve,"$gameState",t),a=()=>xe(Vt,"$opponentState",t);let n=E(!1),l=E("default-room");Te(()=>{C(l,It(Ve)?.roomId??"default-room",!0)});let o=E(void 0),d=E(!1),g=E(!1),h=E(null),u=E(null),v=E(null),k=E(!1),T=E(""),_=E("");const S=q(()=>je.params.id);let L=q(()=>je.url.searchParams.get("token")),B=E(!1);Te(()=>{if(C(B,!0),console.log("Game ID:",m(S)),console.log("Token:",m(L)),!m(L)){console.log("No token provided - running in development mode");return}console.log("JWT token received, will be validated by server");const M=m(L);return w.setAuthToken(M),w.setGameId(m(S)),ue.setGameId(m(S)),ue.setConnected(!1),C(h,qt,!0),(async()=>{try{if(C(u,Ge,!0),!m(u)){console.error("Socket client not available");return}if(!M){console.error("No token provided - cannot connect to server");return}await m(u).connect(M,{onScoreUpdate:$=>{w.updateScore($)},onGameComplete:$=>{w.endGame(),C(g,!0)}}),ue.setConnected(!0),C(v,fi(m(S),"game-container",m(u)),!0),await m(v).init()}catch($){console.error("Failed to initialize game:",$)}})(),()=>{m(u)&&m(u).disconnect(),m(v)&&m(v).destroy(),ue.reset()}});function F(){console.log("Countdown complete"),C(d,!0),w.initGame(),m(v).start()}function j(M,te="error"){C(T,M,!0),C(_,te,!0),C(k,!0)}function X(){}typeof window<"u"&&(window.showGameError=j);var P=bi();Bt(M=>{W(()=>xt.title=`TicTaps - ${m(S)??""}`)});var A=Q(P);fs(A,{get progress(){return i().loadingProgress}});var R=x(A,2);{var Z=M=>{Yt(M,{handleStartClick:F,get gameId(){return m(S)}})};H(R,M=>{i().status===le.Waiting&&M(Z)})}var D=x(R,2),c=y(D);{var f=M=>{{let te=q(()=>a().opponent?.score??null),$=q(()=>a().opponent?.lives??null),ke=q(()=>a().opponent?.name);hs(M,{get score(){return i().score},get time(){return i().time},get totalTime(){return i().totalTime},get lives(){return i().lives},get maxLives(){return i().maxLives},get opponentScore(){return m(te)},get opponentLives(){return m($)},get opponentWaiting(){return a().waiting},get opponentName(){return m(ke)}})}};H(c,M=>{i().status===le.Active&&M(f)})}var G=x(c,2);be(G,M=>C(o,M),()=>m(o));var O=x(G,2);us(O,{get show(){return m(d)},duration:3});var U=x(O,2);{let M=q(()=>i().status===le.Ended);Xt(U,{get show(){return m(M)},get finalScore(){return i().score}})}var J=x(U,2);bs(J,{get isVisible(){return m(k)},get errorMessage(){return m(T)},get errorType(){return m(_)},onClose:X});var V=x(J,2);{var he=M=>{var te=pi(),$=Q(te);ks($,{onToggle:()=>C(n,!m(n))});var ke=x($,2);Ss(ke,{get roomId(){return m(l)},get open(){return m(n)}}),I(M,te)};H(V,M=>{(i().status===le.Active||i().status===le.Ended)&&M(he)})}b(D),I(r,P),ne(),s()}export{ct as A,ot as C,ri as R,si as S,dt as V,Hi as _,ji as a,ti as u};
