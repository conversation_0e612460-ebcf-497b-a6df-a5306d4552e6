<script lang="ts">
  import type { Player } from '$lib/stores/roomState';
  
  interface Props {
    players: Player[];
    currentUserId: string | null;
    maxPlayers: number;
  }
  
  let { players, currentUserId, maxPlayers }: Props = $props();
  
  // Sort players: host first, then by join time
  const sortedPlayers = $derived(
    [...players].sort((a, b) => {
      if (a.isHost && !b.isHost) return -1;
      if (!a.isHost && b.isHost) return 1;
      return a.joinedAt.getTime() - b.joinedAt.getTime();
    })
  );
  
  function getPlayerDisplayName(player: Player): string {
    return player.displayName || player.name || `Player ${player.userId.slice(-4)}`;
  }
  
  function getPlayerStatus(player: Player): string {
    if (!player.isConnected) return 'Disconnected';
    if (player.isReady) return 'Ready';
    return 'Waiting';
  }
  
  function getStatusColor(player: Player): string {
    if (!player.isConnected) return 'text-red-400';
    if (player.isReady) return 'text-green-400';
    return 'text-yellow-400';
  }
</script>

<div class="player-list">
  <div class="player-list-header">
    <h3 class="text-lg font-semibold text-white mb-2">
      Players ({players.length}/{maxPlayers})
    </h3>
  </div>
  
  <div class="player-list-content">
    {#each sortedPlayers as player (player.userId)}
      <div 
        class="player-item"
        class:current-player={player.userId === currentUserId}
        class:disconnected={!player.isConnected}
      >
        <div class="player-info">
          <div class="player-name">
            {getPlayerDisplayName(player)}
            {#if player.isHost}
              <span class="host-badge">HOST</span>
            {/if}
            {#if player.userId === currentUserId}
              <span class="you-badge">YOU</span>
            {/if}
          </div>
          <div class="player-status {getStatusColor(player)}">
            {getPlayerStatus(player)}
          </div>
        </div>
        
        <div class="player-indicators">
          {#if player.isConnected}
            <div class="connection-indicator connected" title="Connected"></div>
          {:else}
            <div class="connection-indicator disconnected" title="Disconnected"></div>
          {/if}
          
          {#if player.isReady}
            <div class="ready-indicator ready" title="Ready">✓</div>
          {:else}
            <div class="ready-indicator not-ready" title="Not Ready">○</div>
          {/if}
        </div>
      </div>
    {/each}
    
    <!-- Empty slots -->
    {#each Array(Math.max(0, maxPlayers - players.length)) as _, index}
      <div class="player-item empty-slot">
        <div class="player-info">
          <div class="player-name text-gray-500">
            Empty Slot
          </div>
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .player-list {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .player-list-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .player-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
  }
  
  .player-item:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  
  .player-item.current-player {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
  }
  
  .player-item.disconnected {
    opacity: 0.6;
    background: rgba(239, 68, 68, 0.1);
  }
  
  .player-item.empty-slot {
    opacity: 0.4;
    border-style: dashed;
  }
  
  .player-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .player-name {
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
  }
  
  .player-status {
    font-size: 12px;
    font-weight: 500;
  }
  
  .host-badge {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .you-badge {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .player-indicators {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .connection-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  
  .connection-indicator.connected {
    background: #10b981;
    box-shadow: 0 0 6px rgba(16, 185, 129, 0.6);
  }
  
  .connection-indicator.disconnected {
    background: #ef4444;
    box-shadow: 0 0 6px rgba(239, 68, 68, 0.6);
  }
  
  .ready-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
  }
  
  .ready-indicator.ready {
    background: #10b981;
    color: white;
  }
  
  .ready-indicator.not-ready {
    background: rgba(255, 255, 255, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  /* Scrollbar styling */
  .player-list-content::-webkit-scrollbar {
    width: 4px;
  }
  
  .player-list-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
  
  .player-list-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
  
  .player-list-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
</style>
