

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/2.DL9bb4bx.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/C1M19Mmo.js","_app/immutable/chunks/4UAai7vz.js","_app/immutable/chunks/DJNDnN69.js","_app/immutable/chunks/DMnCbMI3.js","_app/immutable/chunks/BwME0dYm.js","_app/immutable/chunks/D6Z45t_z.js"];
export const stylesheets = ["_app/immutable/assets/MumsNumbers.yT0SHS_s.css","_app/immutable/assets/2.BN6ceEAQ.css"];
export const fonts = [];
