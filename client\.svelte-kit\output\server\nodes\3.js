

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/game/_id_/_page.svelte.js')).default;
export const universal = {
  "ssr": false
};
export const universal_id = "src/routes/game/[id]/+page.ts";
export const imports = ["_app/immutable/nodes/3.CQxkl77G.js","_app/immutable/chunks/DHzIY8Hm.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/4UAai7vz.js","_app/immutable/chunks/DJNDnN69.js","_app/immutable/chunks/DMnCbMI3.js","_app/immutable/chunks/mXOxeudE.js","_app/immutable/chunks/D6Z45t_z.js","_app/immutable/chunks/BwME0dYm.js","_app/immutable/chunks/C1M19Mmo.js"];
export const stylesheets = ["_app/immutable/assets/MumsNumbers.yT0SHS_s.css"];
export const fonts = [];
