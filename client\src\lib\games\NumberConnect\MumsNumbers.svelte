<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import type { SocketClient } from "$lib/socket";
  import { gameActions, gameState } from "$lib/stores";
  import type { Position } from "./LevelGenerator";
  import * as PIXI from "pixi.js";

  interface Props {
    socketClient?: SocketClient;
    gameId: string;
    containerId: string;
    onScoreUpdate?: (score: number) => void;
    onGameComplete?: (score: number) => void;
  }

  let {
    socketClient,
    gameId,
    containerId,
    onScoreUpdate,
    onGameComplete,
  }: Props = $props();

  let gameContainer: HTMLDivElement | undefined = $state();
  let pixiContainer: HTMLDivElement | undefined = $state();
  let gameInstance: MumsNumbersGame | null = $state(null);

  // Game state for UI binding
  let moves = $state(0);
  let nextNumber: number | string = $state(1);
  let currentScore = $state(0);
  let currentLives = $state(3);
  let currentLevel = $state(1);
  let levelsCompleted = $state(0);
  // Per-board timer and history
  let boardTimer = $state("00:00");
  let boardTimesMs = $state<number[]>([]);

  // End game state
  let showLevelComplete = $state(false);

  // Server state
  let serverPuzzleState: any = $state(null);
  let isWaitingForServer = $state(false);

  class MumsNumbersGame {
    app: PIXI.Application;
    container: HTMLDivElement;
    gridSize = 5;
    cellSize = 90;
    canvasSize = this.gridSize * this.cellSize;

    // PIXI containers
    gridContainer: PIXI.Container;
    pathContainer: PIXI.Container;
    numbersContainer: PIXI.Container;
    highlightContainer: PIXI.Container;

    // Game state
    grid: any[][] = [];
    solutionPath: Position[] = [];
    numberPositions: { [key: number]: Position } = {};
    playerPath: Position[] = []; // Confirmed path from server
    currentPath: Position[] = []; // Current drawing path (optimistic)
    previewPath: Position[] = []; // Preview path during drawing
    isDrawing = false;
    isDragging = false;
    lastCell: Position | null = null;
    currentNumber = 1;
    gameStarted = false;
    gameEnded = false;

    // Level generation is now handled server-side

    // Game stats
    moveCount = 0;
    timerInterval: any = null;
    boardStartTime: number | null = null;
    boardTimerInterval: any = null;

    constructor(containerElement: HTMLDivElement) {
      this.container = containerElement;

      // Initialize PIXI Application
      this.app = new PIXI.Application();

      // Initialize containers
      this.gridContainer = new PIXI.Container();
      this.pathContainer = new PIXI.Container();
      this.numbersContainer = new PIXI.Container();
      this.highlightContainer = new PIXI.Container();

      // Add containers to stage
      this.app.stage.addChild(this.gridContainer);
      this.app.stage.addChild(this.pathContainer);
      this.app.stage.addChild(this.highlightContainer);
      this.app.stage.addChild(this.numbersContainer);

      // Level generation is now handled server-side

      this.initPixi();
    }

    async initPixi() {
      await this.app.init({
        width: this.canvasSize,
        height: this.canvasSize,
        // backgroundColor: 0xffeaf0,
        backgroundAlpha: 0, // Transparent background
        antialias: true,
      });

      this.container.appendChild(this.app.canvas);

      setTimeout(() => {
        this.init();
      }, 100);
    }

    init() {
      // Initialize empty grid and UI
      this.initializeGrid();
      this.initializeEventListeners();
      this.draw();
      this.resizeCanvas();

      // Setup socket event listeners
      this.setupSocketListeners();
    }

    setupSocketListeners() {
      if (!socketClient) return;

      // Listen for game initialization response
      socketClient.addCustomEventListener("initialized", (data) => {
        console.log("Game initialized:", data);
        if (data.puzzleState) {
          this.loadPuzzleFromServer(data.puzzleState);
          serverPuzzleState = data.puzzleState;
        }
        // Update UI state
        if (data.gameState) {
          currentScore = data.gameState.score;
          currentLives = data.gameState.lives;
          gameActions.updateScore(currentScore);
          gameActions.updateLives(currentLives);
        }
      });

      // Listen for game start response
      socketClient.addCustomEventListener("started", (data) => {
        console.log("Game started:", data);
        this.gameStarted = true;

        // Start per-board timer and reset history
        this.boardStartTime = Date.now();
        this.startBoardTimer();
        boardTimesMs = [];
      });

      // Listen for action results (move validation)
      socketClient.addCustomEventListener("action_result", (data) => {
        console.log("Action result:", data);
        if (data.actionType === "path_move") {
          this.handleServerMoveResult(data.data);
        }
      });

      // Listen for game end
      socketClient.addCustomEventListener("ended", (data) => {
        console.log("Game ended:", data);
        this.handleGameEnd(data);
      });

      // Listen for errors
      socketClient.addCustomEventListener("error", (data) => {
        console.error("Game error:", data);
        isWaitingForServer = false;
      });
    }

    loadPuzzleFromServer(puzzleState: any) {
      // Load the puzzle data from server
      this.grid = puzzleState.grid;
      this.numberPositions = puzzleState.numberPositions;
      this.playerPath = puzzleState.playerPath || [];
      this.currentNumber = puzzleState.currentNumber || 1;

      // Initialize preview path to match player path
      this.previewPath = [...this.playerPath];

      // Update UI
      nextNumber = this.currentNumber;
      moves = this.playerPath.length;

      // Redraw with server data
      this.draw();
    }

    sendPathToServer(path: Position[]) {
      if (!socketClient || !socketClient.isConnected()) {
        console.error("No socket connection available");
        return;
      }

      if (path.length === 0) return;

      isWaitingForServer = true;

      // Send the complete path for validation
      socketClient.sendGameAction("path_move", {
        path: path,
        timestamp: Date.now(),
      });
    }

    sendMoveToServer(cell: Position, moveType: "start" | "continue" | "end") {
      if (!socketClient || !socketClient.isConnected()) {
        console.error("No socket connection available");
        return;
      }

      isWaitingForServer = true;
      socketClient.sendPathMove(cell, moveType);
    }

    handleServerMoveResult(data: any) {
      isWaitingForServer = false;

      if (data.isValid) {
        // Valid move - update local state with server data
        this.playerPath = data.newPath || data.path || [];
        this.currentNumber = data.currentNumber || 1;

        // Reset preview path to match confirmed path
        this.previewPath = [...this.playerPath];

        // Update UI
        nextNumber = this.currentNumber;
        moves = this.playerPath.length;
        currentScore = data.newScore || data.score || currentScore;
        currentLives = data.newLives || data.lives || currentLives;
        currentLevel = data.currentLevel || currentLevel;
        levelsCompleted = data.levelsCompleted || levelsCompleted;

        gameActions.updateScore(currentScore);
        gameActions.updateLives(currentLives);

        // Check for level completion
        if (data.levelCompleted) {
          if (data.newPuzzle) {
            // New level generated - load new puzzle (robust to missing newLevel flag)
            this.handleLevelComplete(data);
          } else {
            // Game ended after level completion (no new puzzle provided)
            this.handleGameEnd({
              reason: "time_up",
              finalScore: currentScore,
              gameWon: false,
              levelsCompleted: levelsCompleted,
            });
          }
        } else if (data.gameEnded) {
          // Game ended without level completion
          this.handleGameEnd({
            reason: data.gameWon ? "completed" : "no_lives",
            finalScore: currentScore,
            gameWon: data.gameWon,
            levelsCompleted: levelsCompleted,
          });
        }
      } else {
        // Invalid move - show error and reset drawing state
        const errorMessage = data.invalidReason || "Invalid path";
        console.warn("Invalid path:", errorMessage);

        // Show error message to user
        this.showPathError(errorMessage);

        this.isDrawing = false;
        this.isDragging = false;

        // Reset preview path to confirmed path
        this.previewPath = [...this.playerPath];

        // Update lives from server
        if (data.newLives !== undefined) {
          currentLives = data.newLives;
          gameActions.updateLives(currentLives);
        }

        // Check for game end
        if (data.gameEnded) {
          this.handleGameEnd({
            reason: "no_lives",
            finalScore: currentScore,
            levelsCompleted: levelsCompleted,
          });
        }
      }

      // Redraw the game
      this.draw();
    }

    showPathError(message: string) {
      const el = document.createElement("div");
      el.className = "path-error-message";
      el.textContent = message;
      document.body.appendChild(el);
      setTimeout(() => el.remove(), 1200);
    }

    handleLevelComplete(data: any) {
      // Show level complete notification briefly
      showLevelComplete = true;

      // Push completed board time (from server) to history
      if (typeof data.boardTimeMs === "number") {
        boardTimesMs = [...boardTimesMs, data.boardTimeMs];
      }

      // Load new puzzle data
      if (data.newPuzzle) {
        this.loadPuzzleFromServer(data.newPuzzle);
      }

      // Reset per-board timer for the next board
      this.boardStartTime = Date.now();
      boardTimer = "00:00";
      this.startBoardTimer();

      // Hide level complete notification quickly (non-blocking)
      setTimeout(() => {
        showLevelComplete = false;
      }, 1200);

      console.log(
        `Level ${currentLevel - 1} completed! Starting level ${currentLevel}`
      );
    }

    handleGameEnd(data: any) {
      this.gameEnded = true;
      this.stopBoardTimer();

      // Update final score
      currentScore = data.finalScore || currentScore;
      gameActions.updateScore(currentScore);
      gameActions.endGame();

      // Show end game screen
      // showEndGame = true;

      // Notify parent components
      if (onScoreUpdate) onScoreUpdate(currentScore);
      if (onGameComplete) onGameComplete(currentScore);
    }

    initializeGrid() {
      this.grid = Array(this.gridSize)
        .fill(null)
        .map(() =>
          Array(this.gridSize)
            .fill(null)
            .map(() => ({ visited: false, number: null, hasPath: false }))
        );
    }

    // Puzzle generation is now handled server-side

    isValidPosition(row: number, col: number): boolean {
      return row >= 0 && row < this.gridSize && col >= 0 && col < this.gridSize;
    }

    resetPlayerState() {
      this.playerPath = [];
      this.currentPath = [];
      this.previewPath = [];
      this.currentNumber = 1;
      this.lastCell = null;
      this.gameEnded = false;
      this.moveCount = 0;
      this.isDrawing = false;
      this.isDragging = false;

      // Update UI
      moves = this.moveCount;
      nextNumber = this.currentNumber;

      // Clear visited states
      for (let row = 0; row < this.gridSize; row++) {
        for (let col = 0; col < this.gridSize; col++) {
          this.grid[row][col] = {
            ...this.grid[row][col],
            visited: false,
            hasPath: false,
          };
        }
      }
    }

    // Input handling methods
    initializeEventListeners() {
      const canvas = this.app.canvas;

      // Mouse events
      canvas.addEventListener("mousedown", (e) => this.handleStart(e));
      canvas.addEventListener("mousemove", (e) => this.handleMove(e));
      canvas.addEventListener("mouseup", () => this.handleEnd());
      canvas.addEventListener("mouseleave", () => this.handleEnd());

      // Touch events
      canvas.addEventListener("touchstart", this.handleTouchStart.bind(this));
      canvas.addEventListener("touchmove", this.handleTouchMove.bind(this));
      canvas.addEventListener("touchend", (e: TouchEvent) => {
        e.preventDefault();
        this.handleEnd();
      });
    }

    handleTouchStart(e: TouchEvent) {
      e.preventDefault();
      this.handleStart(e.touches[0]);
    }

    handleTouchMove(e: TouchEvent) {
      e.preventDefault();
      this.handleMove(e.touches[0]);
    }

    handleStart(e: MouseEvent | Touch) {
      if (this.gameEnded || isWaitingForServer) return;

      const cell = this.getCellFromEvent(e);
      if (!cell) return;

      // Start game timer if not started
      if (!this.gameStarted) {
        this.startGame();
      }

      this.isDrawing = true;
      this.isDragging = true;

      // Start with current confirmed path
      this.previewPath = [...this.playerPath];

      // Add the starting cell to preview path for immediate visual feedback
      this.addCellToPreviewPath(cell);

      // Redraw immediately for responsive UI
      this.draw();
    }

    handleMove(e: MouseEvent | Touch) {
      if (!this.isDrawing || !this.isDragging || this.gameEnded) return;

      const cell = this.getCellFromEvent(e);
      if (!cell) return;

      // Add cell to preview path for immediate visual feedback
      this.addCellToPreviewPath(cell);

      // Redraw immediately for responsive UI
      this.draw();
    }

    handleEnd() {
      if (this.gameEnded) return;

      this.isDrawing = false;
      this.isDragging = false;

      // Send the complete path to server for validation if we have a preview path
      if (this.previewPath.length > this.playerPath.length) {
        // Send the entire new path to server
        this.sendPathToServer(this.previewPath);
      } else {
        // No new path, just reset preview
        this.previewPath = [...this.playerPath];
        this.draw();
      }
    }

    addCellToPreviewPath(cell: Position) {
      // Check if this is the same as the last cell
      if (this.previewPath.length > 0) {
        const lastCell = this.previewPath[this.previewPath.length - 1];
        if (cell.row === lastCell.row && cell.col === lastCell.col) {
          return; // Same cell, no need to add
        }

        // Check if this cell is already in the path (backtracking)
        const existingIndex = this.previewPath.findIndex(
          (c) => c.row === cell.row && c.col === cell.col
        );

        if (existingIndex !== -1) {
          // Backtrack to this cell
          this.previewPath = this.previewPath.slice(0, existingIndex + 1);
          return;
        }

        // Check adjacency for smooth path drawing
        const isAdjacent =
          Math.abs(cell.row - lastCell.row) +
            Math.abs(cell.col - lastCell.col) ===
          1;

        if (!isAdjacent) {
          return; // Not adjacent, don't add
        }
      }

      // Add the cell to preview path
      this.previewPath.push(cell);
    }

    getCellFromEvent(e: MouseEvent | Touch): Position | null {
      const rect = this.app.canvas.getBoundingClientRect();
      const scaleX = this.app.canvas.width / rect.width;
      const scaleY = this.app.canvas.height / rect.height;

      const x = (e.clientX - rect.left) * scaleX;
      const y = (e.clientY - rect.top) * scaleY;

      const col = Math.floor(x / this.cellSize);
      const row = Math.floor(y / this.cellSize);

      if (this.isValidPosition(row, col)) {
        return { row, col };
      }

      return null;
    }

    // Game state updates and validation are now handled server-side

    // UI and drawing methods
    draw() {
      this.clearContainers();
      this.drawClassicGrid();
      this.drawPath();
      this.drawNumbers();
    }

    clearContainers() {
      this.gridContainer.removeChildren();
      this.pathContainer.removeChildren();
      this.numbersContainer.removeChildren();
      this.highlightContainer.removeChildren();
    }

    drawClassicGrid() {
      const graphics = new PIXI.Graphics();

      for (let i = 0; i <= this.gridSize; i++) {
        const pos = i * this.cellSize;
        // Vertical lines
        graphics.moveTo(pos, 0).lineTo(pos, this.canvasSize);
        // Horizontal lines
        graphics.moveTo(0, pos).lineTo(this.canvasSize, pos);
      }

      graphics.stroke({ color: 0xcccccc, width: 1 });
      this.gridContainer.addChild(graphics);
    }

    drawPath() {
      // Draw confirmed player path (solid line)
      if (this.playerPath.length >= 2) {
        const confirmedGraphics = new PIXI.Graphics();

        const firstCell = this.playerPath[0];
        confirmedGraphics.moveTo(
          firstCell.col * this.cellSize + this.cellSize / 2,
          firstCell.row * this.cellSize + this.cellSize / 2
        );

        for (let i = 1; i < this.playerPath.length; i++) {
          const cell = this.playerPath[i];
          confirmedGraphics.lineTo(
            cell.col * this.cellSize + this.cellSize / 2,
            cell.row * this.cellSize + this.cellSize / 2
          );
        }

        confirmedGraphics.stroke({
          color: 0x27ae60, // Green for confirmed path
          width: 8,
          cap: "round",
          join: "round",
        });

        this.pathContainer.addChild(confirmedGraphics);
      }

      // Draw preview path (semi-transparent line)
      if (this.previewPath.length >= 2 && this.isDrawing) {
        const previewGraphics = new PIXI.Graphics();

        const firstCell = this.previewPath[0];
        previewGraphics.moveTo(
          firstCell.col * this.cellSize + this.cellSize / 2,
          firstCell.row * this.cellSize + this.cellSize / 2
        );

        for (let i = 1; i < this.previewPath.length; i++) {
          const cell = this.previewPath[i];
          previewGraphics.lineTo(
            cell.col * this.cellSize + this.cellSize / 2,
            cell.row * this.cellSize + this.cellSize / 2
          );
        }

        previewGraphics.stroke({
          color: 0x3498db, // Blue for preview path
          width: 6,
          cap: "round",
          join: "round",
          alpha: 0.7, // Semi-transparent
        });

        this.pathContainer.addChild(previewGraphics);
      }

      // Draw single cell highlight for start of drawing
      if (this.previewPath.length === 1 && this.isDrawing) {
        const cell = this.previewPath[0];
        const highlight = new PIXI.Graphics();
        highlight
          .circle(
            cell.col * this.cellSize + this.cellSize / 2,
            cell.row * this.cellSize + this.cellSize / 2,
            this.cellSize / 4
          )
          .fill({ color: 0x3498db, alpha: 0.5 });

        this.pathContainer.addChild(highlight);
      }
    }

    drawNumbers() {
      for (let number = 1; number <= 5; number++) {
        const pos = this.numberPositions[number];
        if (pos) {
          const x = pos.col * this.cellSize + this.cellSize / 2;
          const y = pos.row * this.cellSize + this.cellSize / 2;

          // Background circle
          const circle = new PIXI.Graphics();
          const color = number < this.currentNumber ? 0x27ae60 : 0xe74c3c;
          circle.circle(x, y, 25).fill(color);
          this.numbersContainer.addChild(circle);

          // Number text
          const text = new PIXI.Text({
            text: number.toString(),
            style: {
              fontFamily: "Arial",
              fontSize: 32,
              fontWeight: "bold",
              fill: 0xffffff,
            },
          });
          text.anchor.set(0.5);
          text.x = x;
          text.y = y;
          this.numbersContainer.addChild(text);
        }
      }
    }

    highlightCell(row: number, col: number, color: number, alpha: number) {
      const highlight = new PIXI.Graphics();
      highlight
        .rect(
          col * this.cellSize + 2,
          row * this.cellSize + 2,
          this.cellSize - 4,
          this.cellSize - 4
        )
        .fill({ color, alpha });
      this.highlightContainer.addChild(highlight);
    }

    // Game control methods
    startGame() {
      // Send start request to server
      if (socketClient && socketClient.isConnected()) {
        socketClient.startGame();
      }

      console.log("Start game request sent to server");
    }

    startBoardTimer() {
      if (this.boardTimerInterval) clearInterval(this.boardTimerInterval);
      this.boardTimerInterval = setInterval(() => {
        if (!this.gameEnded && this.boardStartTime) {
          const elapsed = Math.floor((Date.now() - this.boardStartTime) / 1000);
          const minutes = Math.floor(elapsed / 60);
          const seconds = elapsed % 60;
          boardTimer = `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        }
      }, 1000);
    }

    stopBoardTimer() {
      if (this.boardTimerInterval) {
        clearInterval(this.boardTimerInterval);
      }
    }

    resetPath() {
      currentScore = 0;
      gameActions.updateScore(currentScore);

      this.currentPath = [];
      this.currentNumber = 1;
      this.draw();
    }

    restart() {
      this.gameStarted = false;
      this.timerInterval = null;

      // Reset lives and score if starting fresh
      if (currentLives <= 0) {
        currentLives = 3;
        currentScore = 0;
        gameActions.updateLives(currentLives);
        gameActions.updateScore(currentScore);
      }

      // Request new puzzle from server
      if (socketClient && socketClient.isConnected()) {
        socketClient.initGame();
      }
    }

    resizeCanvas() {
      if (!this.app.canvas || !gameContainer) return;

      const maxWidth = Math.min(gameContainer.clientWidth - 40, 500);

      if (maxWidth < 500) {
        this.app.canvas.style.width = maxWidth + "px";
        this.app.canvas.style.height = maxWidth + "px";
      } else {
        this.app.canvas.style.width = "500px";
        this.app.canvas.style.height = "500px";
      }
    }

    destroy() {
      this.stopBoardTimer();
      if (this.app.canvas) {
        const canvas = this.app.canvas;
        canvas.removeEventListener("mousedown", this.handleStart);
        canvas.removeEventListener("mousemove", this.handleMove);
        canvas.removeEventListener("mouseup", this.handleEnd);
        canvas.removeEventListener("mouseleave", this.handleEnd);
        canvas.removeEventListener("touchstart", this.handleTouchStart);
        canvas.removeEventListener("touchmove", this.handleTouchMove);
        canvas.removeEventListener("touchend", this.handleEnd);
      }
      this.app.destroy();
    }
  }

  onMount(() => {
    if (pixiContainer) {
      // Initialize game state
      currentLives = 3;
      currentScore = 0;
      gameActions.updateLives(currentLives);
      gameActions.updateScore(currentScore);

      gameInstance = new MumsNumbersGame(pixiContainer);

      // Handle window resize
      const handleResize = () => {
        if (gameInstance) {
          gameInstance.resizeCanvas();
        }
      };

      window.addEventListener("resize", handleResize);

      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  });

  onDestroy(() => {
    if (gameInstance) {
      gameInstance.destroy();
    }
  });

  function formatMs(ms: number): string {
    const total = Math.max(0, Math.floor(ms / 1000));
    const m = Math.floor(total / 60);
    const s = total % 60;
    return `${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
  }

  export function startGame() {
    if (gameInstance && !gameInstance.gameStarted) {
      gameInstance.startGame();
    }
  }
</script>

<div class="mums-numbers-game" bind:this={gameContainer}>
  <div class="game-board-container">
    <div bind:this={pixiContainer} class="game-canvas"></div>

    <p class="board-time">
      Board: <span class="board-timer">{boardTimer}</span>
    </p>
  </div>
</div>

<!-- Level Complete Notification -->
{#if showLevelComplete}
  <div class="level-complete-overlay">
    <div class="level-complete-panel">
      <div class="level-complete-blur-bg"></div>
      <div class="level-complete-content">
        <h2 class="level-complete-title">Level {currentLevel - 1} Complete!</h2>
        <p class="level-complete-subtitle">Starting Level {currentLevel}...</p>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Global Styles */
  .mums-numbers-game {
    width: 100%;
    height: 100%;
    min-height: 100dvh;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: end;

    /* margin-top: 40vh; */
  }

  /* Game Board */
  .game-board-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;

    gap: 2rem;
  }

  .game-canvas {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    cursor: crosshair;
    max-width: 100%;
    height: auto;
  }

  .instructions {
    padding: 15px;
    border-radius: 8px;
    background: #fff;
    border: 2px solid #ddd;
    text-align: center;
  }

  .instructions p {
    margin-bottom: 8px;
    font-size: 1rem;
  }

  .board-time {
    font-size: 1.2rem;
    font-weight: bold;
    color: #fff;
  }

  /* Level Complete Notification - non-blocking bottom toast */
  .level-complete-overlay {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: transparent;
    width: auto;
    height: auto;
    display: block;
    z-index: 1000;
    pointer-events: none;
    animation: fadeIn 0.1s ease-in;
  }

  /* Level Complete compact modal panel (consistent with other modals, but non-blocking and bottom) */
  .level-complete-panel {
    position: relative;
    width: auto;
    min-width: clamp(220px, 60vw, 420px);
    max-width: 90vw;
  }

  .level-complete-blur-bg {
    position: absolute;
    inset: -2px;
    background: rgba(0, 0, 0, 0.35);
    border-radius: 14px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .level-complete-content {
    position: relative;
    background: rgba(26, 35, 49, 0.5);
    border-radius: 14px;
    padding: 0.75rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.2s ease-out;
  }

  .level-complete-title {
    margin: 0;
    font-family: Arial, sans-serif;
    font-size: clamp(1rem, 4.5vw, 1.4rem);
    font-weight: 800;
    background: linear-gradient(90deg, #4bffae 0%, #32c4ff 50%, #5c67ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 12px rgba(75, 255, 174, 0.35);
    filter: drop-shadow(0 0 8px rgba(75, 255, 174, 0.25));
  }

  .level-complete-subtitle {
    margin: 0;
    font-family: Arial, sans-serif;
    font-size: clamp(0.8rem, 3.5vw, 1rem);
    color: #ffffff;
    opacity: 0.9;
  }

  @media (max-width: 640px) {
    .level-complete-content {
      padding: 0.6rem 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .level-complete-content {
      padding: 0.5rem 0.7rem;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateY(-50px) scale(0.8);
      opacity: 0;
    }
    to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .mums-numbers-game {
      padding: 10px;
    }

    .control-btn {
      padding: 8px 12px;
      font-size: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .game-controls {
      gap: 8px;
    }

    .control-btn {
      padding: 6px 10px;
      font-size: 0.7rem;
    }

    .instructions p {
      font-size: 0.9rem;
    }
  }

  .path-error-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #e74c3c;
    color: #fff;
    padding: 12px 18px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: fadeInOut 1.2s ease-in-out;
  }
  @keyframes fadeInOut {
    0% {
      opacity: 0;
      transform: translateX(-50%) scale(0.95);
    }
    20%,
    80% {
      opacity: 1;
      transform: translateX(-50%) scale(1);
    }
    100% {
      opacity: 0;
      transform: translateX(-50%) scale(0.95);
    }
  }
</style>
