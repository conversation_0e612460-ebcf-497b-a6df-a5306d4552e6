# TicTaps Games — Brief Game Descriptions and Fairness Overview

## Platform Model and Fairness Principles

- Session isolation, equal conditions

  - Players compete on score, not direct head‑to‑head interference.
  - Participants within the same party/room receive the same session parameters (e.g., time limit, starting conditions), ensuring parity.

- Controlled randomness

  - Any random elements (e.g., tile layouts) are generated via a consistent pseudo‑random process and can be uniformly applied to all participants in a party to ensure equal difficulty.
  - Randomness never directly determines the winner; player performance does.

- Timer accuracy and device neutrality

  - Uniform time caps per game; session ends cleanly at timeout.
  - Input handling and UI are designed so faster devices do not confer material advantage.

- Scoring integrity

  - Scores reflect in‑game actions only; no pay‑to‑win or purchasable boosts.
  - Score submissions are validated; anomalous submissions can be flagged or rejected.

- Data protection and transparency
  - Minimal data for gameplay; session parameters can be logged for auditability.

---

## Game Catalog and Fairness Notes

### Finger Frenzy (Fast‑tap arcade)

- What it is
  - A timed tapping challenge. Players tap targets as they appear to accumulate points quickly within a short time window.
- Objective and scoring
  - Earn points by tapping valid targets; streaks or accuracy may increase score.
- Fairness controls
  - Identical time limit for all players.
  - Target presentation can be standardized per party (same sequence/layout) to equalize difficulty.
  - Uniform hitboxes and input thresholds; no hidden multipliers.
  - Miss‑taps and false positives are consistently handled across devices.

### Bingo (Classic bingo with multiplayer features)

- What it is
  - Traditional bingo cards with number draws; players mark cards as numbers are called. Multiplayer is supported.
- Objective and scoring
  - Complete designated patterns (e.g., lines, full card). Scoring or win condition depends on speed and correctness of marking.
- Fairness controls
  - All participants in a round receive the exact same draw order.
  - Card generation uses uniform randomness with equalized distribution.
  - No speed advantage from network latency for number availability (draws apply to all simultaneously).
  - Important legal note: Bingo contains chance by design. The platform treats it as non‑monetized entertainment (no wagers or payouts). If regulated in a given jurisdiction, features can be region‑gated or disabled as required.

### Matching Mayhem (Memory/matching)

- What it is
  - A memory game where players flip cards to find matching pairs.
- Objective and scoring
  - Maximize matched pairs and minimize mistakes/time; faster, fewer misses yields higher scores.
- Fairness controls
  - Board layout is generated uniformly; within a party, all players can receive the same layout to equalize difficulty.
  - No hints or advantage mechanics are provided to any subset of players.
  - Deterministic scoring for matches, misses, and time.

### Number Connect (Mum’s Numbers)

- What it is
  - A grid-based path puzzle where players draw a continuous path connecting numbers in ascending order (e.g., 1→2→3…) on a fixed-size board.
- Objective and scoring
  - Complete each board accurately with minimal mistakes and in the shortest time; score increases for correct connections/boards, with deductions for errors or lost lives.
- Fairness controls
  - Puzzle layouts are standardized per party via a shared server-generated state/seed so all participants face the same board and next-number sequence.
  - Move validation is applied uniformly (server-validated actions) to prevent client-only advantages.
  - Identical grid size, lives, and timing rules for all players.
  - Consistent touch/drag thresholds across devices; no power-ups or purchasable boosts.

### Number Sequence (Pattern recognition)

- What it is
  - Players identify and tap the next correct item(s) in a numeric or patterned sequence under time pressure.
- Objective and scoring
  - Correct, consecutive selections increase score; mistakes reset combos or deduct points.
- Fairness controls
  - Sequence set is standardized per party to ensure identical difficulty.
  - Clear, deterministic penalties for mistakes and timeouts.
  - Consistent touch/click thresholds and no device‑specific optimizations that change difficulty.

---

## Cross‑Cutting Fairness and Integrity Controls

- Session parameters parity

  - Time limits, starting states, and (where applicable) random seeds can be shared across a party so that every participant faces the same challenge.

- Randomness policy

  - Pseudo‑random generation for layouts/draws uses uniform distributions.
  - For auditability, seeds or draw orders can be logged and, if required, disclosed post‑session for verification.
  - Randomness never directly selects winners; players’ actions and accuracy determine outcomes.

- Timing and latency

  - In‑client timers end sessions locally at the same nominal duration for all.
  - Events like bingo draws are applied simultaneously to all participants.

- Anti‑cheat and score validation

  - Client‑side validations (e.g., impossible action rates, out‑of‑bounds scores).
  - Server‑side verification and anomaly detection planned/enhanced as backend components roll out.

- Accessibility and fairness

  - No pay‑to‑win mechanics; purely cosmetic features (if any) do not affect outcomes.
  - Clear UI affordances and consistent hit areas support equitable play.

---

## Compliance Posture and Disclosures

- Classification

  - Predominantly skill‑based mini‑games. Bingo includes a chance component but due to fairness controls, it does not confer a material advantage.

- Disclosures to users

  - Each game session is capped in time; no purchasable boosts.
  - Leaderboards compare scores achieved under the same session constraints.

- Auditability

  - Session parameters (including any seeds for random elements) can be retained for audit trails where required.

- Regional controls
  - If any jurisdiction requires restrictions for bingo‑style content, features can be disabled or adapted.

---

## Optional Technical Addendum (for deeper review)

- Randomness and seeds

  - Recommended practice: derive per‑party seeds from a verifiable source (e.g., server nonce + timestamp) and apply uniformly to any random elements within that party’s session. Persist for audit.

- Integrity checks
  - Use server‑side verification of critical events and scores as backend rolls out (e.g., signature or checksum with session ID and parameters).
  - Rate‑limit and sanity‑check inputs to prevent automated play advantages.

---

Last updated: 2025-09-18
