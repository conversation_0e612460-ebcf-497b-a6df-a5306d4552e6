<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { socketClient } from "$lib/socket";
  import { writable } from "svelte/store";

  export let roomId: string;
  export let open: boolean = false;

  let popup: HTMLDivElement | undefined = undefined;

  type Entry = {
    userId: string;
    name?: string;
    score: number;
    lives?: number;
    status: string;
  };

  const leaderboard = writable<Entry[]>([]);

  function handleLeaderboardUpdate(payload: {
    roomId: string;
    leaderboard: Entry[];
  }) {
    if (!payload || payload.roomId !== roomId) return;
    leaderboard.set(payload.leaderboard);
  }

  function toggle() {
    open = !open;

    if (popup) {
      popup.style.display = open ? "block" : "none";
    }
  }

  onMount(() => {
    const handler = (data: any) => handleLeaderboardUpdate(data);
    socketClient.addCustomEventListener("leaderboard_update", handler);

    return () => {
      socketClient.removeCustomEventListener("leaderboard_update", handler);
    };
  });
</script>

<div
  id="popup-leaderboard"
  class="overlay"
  bind:this={popup}
  style="display: {open ? 'block' : 'none'}"
>
  <div class="modal">
    <div class="header">
      <div class="title">Room Leaderboard</div>
      <button class="close-btn" on:click={toggle}>×</button>
    </div>
    <div class="list">
      {#each $leaderboard as entry, idx}
        <div class="row">
          <div class="rank">{idx + 1}</div>
          <div class="userid">{entry.name ?? entry.userId}</div>
          <div class="score">{entry.score}</div>
          <div class="status">{entry.status}</div>
        </div>
      {/each}
    </div>
  </div>
</div>

<style>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: none;
    z-index: 4000;
  }
  .modal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: min(90vw, 500px);
    background: #101218;
    color: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    overflow: hidden;
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #151a22;
    border-bottom: 1px solid #222a36;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
  }
  .close-btn {
    background: transparent;
    color: #aaa;
    border: none;
    font-size: 18px;
    cursor: pointer;
  }
  .list {
    padding: 8px 0;
    max-height: 60vh;
    overflow: auto;
  }
  .row {
    display: grid;
    grid-template-columns: 40px 1fr 80px 100px;
    gap: 8px;
    padding: 10px 16px;
    border-bottom: 1px solid #1b2230;
    align-items: center;
  }
  .rank {
    text-align: center;
    color: #7aa8ff;
    font-weight: 600;
  }
  .userid {
    color: #e1e6f0;
    font-size: 14px;
  }
  .score {
    text-align: right;
    font-variant-numeric: tabular-nums;
  }
  .status {
    text-transform: capitalize;
    text-align: right;
    color: #9fb0c7;
  }
</style>
