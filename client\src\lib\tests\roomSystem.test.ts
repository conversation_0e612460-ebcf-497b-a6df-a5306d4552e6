/**
 * Basic tests for the room system functionality
 * These are integration tests to verify the room system components work together
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { roomState, roomActions, RoomStatus } from '../stores/roomState';

describe('Room System Integration', () => {
  beforeEach(() => {
    // Reset room state before each test
    roomActions.reset();
  });

  describe('Room State Management', () => {
    it('should initialize with correct default state', () => {
      const state = get(roomState);
      
      expect(state.status).toBe(RoomStatus.NotInRoom);
      expect(state.roomId).toBeNull();
      expect(state.roomCode).toBeNull();
      expect(state.players).toEqual([]);
      expect(state.currentUserId).toBeNull();
      expect(state.isHost).toBe(false);
      expect(state.hostUserId).toBeNull();
      expect(state.maxPlayers).toBe(8);
      expect(state.gameId).toBeNull();
      expect(state.isConnected).toBe(false);
      expect(state.error).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.loadingMessage).toBeNull();
    });

    it('should handle joining a room', () => {
      const roomData = {
        roomId: 'test-room-123',
        roomCode: 'ABC123',
        players: [
          {
            userId: 'user1',
            name: 'Player 1',
            displayName: 'Player 1',
            isHost: true,
            isReady: false,
            isConnected: true,
            joinedAt: new Date()
          }
        ],
        hostUserId: 'user1',
        currentUserId: 'user1',
        gameId: 'test-game',
        maxPlayers: 4
      };

      roomActions.joinedRoom(roomData);
      
      const state = get(roomState);
      expect(state.status).toBe(RoomStatus.InRoom);
      expect(state.roomId).toBe('test-room-123');
      expect(state.roomCode).toBe('ABC123');
      expect(state.players).toHaveLength(1);
      expect(state.currentUserId).toBe('user1');
      expect(state.isHost).toBe(true);
      expect(state.hostUserId).toBe('user1');
      expect(state.gameId).toBe('test-game');
      expect(state.maxPlayers).toBe(4);
      expect(state.isConnected).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should handle adding players to room', () => {
      // First join a room
      roomActions.joinedRoom({
        roomId: 'test-room',
        roomCode: 'ABC123',
        players: [],
        hostUserId: 'user1',
        currentUserId: 'user1',
        gameId: 'test-game'
      });

      // Add a player
      const newPlayer = {
        userId: 'user2',
        name: 'Player 2',
        displayName: 'Player 2',
        isHost: false,
        isReady: false,
        isConnected: true,
        joinedAt: new Date()
      };

      roomActions.addPlayer(newPlayer);
      
      const state = get(roomState);
      expect(state.players).toHaveLength(1);
      expect(state.players[0].userId).toBe('user2');
      expect(state.players[0].isHost).toBe(false);
    });

    it('should handle host transfer', () => {
      // Setup initial room with host
      roomActions.joinedRoom({
        roomId: 'test-room',
        roomCode: 'ABC123',
        players: [
          {
            userId: 'user1',
            name: 'Player 1',
            displayName: 'Player 1',
            isHost: true,
            isReady: false,
            isConnected: true,
            joinedAt: new Date()
          },
          {
            userId: 'user2',
            name: 'Player 2',
            displayName: 'Player 2',
            isHost: false,
            isReady: false,
            isConnected: true,
            joinedAt: new Date()
          }
        ],
        hostUserId: 'user1',
        currentUserId: 'user2', // Current user is not the host
        gameId: 'test-game'
      });

      // Transfer host to user2
      roomActions.updateHost('user2', 'user2');
      
      const state = get(roomState);
      expect(state.hostUserId).toBe('user2');
      expect(state.isHost).toBe(true);
      expect(state.players[0].isHost).toBe(false); // user1 is no longer host
      expect(state.players[1].isHost).toBe(true);  // user2 is now host
    });

    it('should handle player ready status updates', () => {
      // Setup room with players
      roomActions.joinedRoom({
        roomId: 'test-room',
        roomCode: 'ABC123',
        players: [
          {
            userId: 'user1',
            name: 'Player 1',
            displayName: 'Player 1',
            isHost: true,
            isReady: false,
            isConnected: true,
            joinedAt: new Date()
          }
        ],
        hostUserId: 'user1',
        currentUserId: 'user1',
        gameId: 'test-game'
      });

      // Update ready status
      roomActions.updatePlayerReady('user1', true);
      
      const state = get(roomState);
      expect(state.players[0].isReady).toBe(true);
    });

    it('should handle leaving room', () => {
      // First join a room
      roomActions.joinedRoom({
        roomId: 'test-room',
        roomCode: 'ABC123',
        players: [],
        hostUserId: 'user1',
        currentUserId: 'user1',
        gameId: 'test-game'
      });

      // Leave the room
      roomActions.leftRoom();
      
      const state = get(roomState);
      expect(state.status).toBe(RoomStatus.NotInRoom);
      expect(state.roomId).toBeNull();
      expect(state.roomCode).toBeNull();
      expect(state.players).toEqual([]);
      expect(state.currentUserId).toBeNull();
      expect(state.isHost).toBe(false);
      expect(state.hostUserId).toBeNull();
      expect(state.isConnected).toBe(true); // Should maintain connection
    });

    it('should handle error states', () => {
      const errorMessage = 'Room not found';
      
      roomActions.setError(errorMessage);
      
      const state = get(roomState);
      expect(state.error).toBe(errorMessage);
      expect(state.status).toBe(RoomStatus.Error);
      expect(state.isLoading).toBe(false);
      expect(state.loadingMessage).toBeNull();
    });

    it('should handle loading states', () => {
      const loadingMessage = 'Joining room...';
      
      roomActions.setLoading(true, loadingMessage);
      
      const state = get(roomState);
      expect(state.isLoading).toBe(true);
      expect(state.loadingMessage).toBe(loadingMessage);
    });

    it('should clear errors', () => {
      // Set an error first
      roomActions.setError('Test error');
      
      // Clear the error
      roomActions.clearError();
      
      const state = get(roomState);
      expect(state.error).toBeNull();
      expect(state.status).toBe(RoomStatus.NotInRoom);
    });
  });

  describe('Game Flow Integration', () => {
    it('should handle game start flow', () => {
      // Setup room
      roomActions.joinedRoom({
        roomId: 'test-room',
        roomCode: 'ABC123',
        players: [],
        hostUserId: 'user1',
        currentUserId: 'user1',
        gameId: 'test-game'
      });

      // Start game
      roomActions.startGame();
      
      let state = get(roomState);
      expect(state.status).toBe(RoomStatus.Starting);
      expect(state.isLoading).toBe(true);
      expect(state.loadingMessage).toBe('Starting game...');

      // Game started
      roomActions.gameStarted();
      
      state = get(roomState);
      expect(state.status).toBe(RoomStatus.GameActive);
      expect(state.isLoading).toBe(false);
      expect(state.loadingMessage).toBeNull();
    });
  });
});
